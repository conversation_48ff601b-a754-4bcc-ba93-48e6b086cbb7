#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
测试geometry处理功能的脚本
验证缺失geometry的行数据能否正常插入
"""

import os
import sys
import pandas as pd
import geopandas as gpd
from shapely.geometry import Point, Polygon
import tempfile
import logging

# 添加当前目录到Python路径
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

from gis_data_import import GISDataImporter

# 配置日志
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

def create_test_data():
    """创建测试数据，包含有geometry和无geometry的记录"""
    
    # 创建测试数据
    data = {
        'id': [1, 2, 3, 4, 5],
        'name': ['点A', '点B', '区域C', '线D', '无几何E'],
        'type': ['point', 'point', 'polygon', 'line', 'attribute'],
        'value': [100, 200, 300, 400, 500],
        'description': ['有几何的点', '有几何的点', '有几何的面', '有几何的线', '无几何的属性数据']
    }
    
    # 创建几何数据（故意让最后一个为None）
    geometries = [
        Point(116.3974, 39.9093),  # 北京
        Point(121.4737, 31.2304),  # 上海
        Polygon([(0, 0), (1, 0), (1, 1), (0, 1), (0, 0)]),  # 简单多边形
        Point(113.2644, 23.1291),  # 广州
        None  # 无几何数据
    ]
    
    # 创建GeoDataFrame
    gdf = gpd.GeoDataFrame(data, geometry=geometries, crs='EPSG:4326')
    
    return gdf

def test_geometry_handling():
    """测试geometry处理功能"""
    
    logger.info("开始测试geometry处理功能...")
    
    # 数据库配置（请根据实际情况修改）
    db_config = {
        'host': 'localhost',
        'port': 3306,
        'user': 'root',
        'password': 'password',
        'database': 'test_db'
    }
    
    try:
        # 创建测试数据
        test_gdf = create_test_data()
        logger.info(f"创建测试数据: {len(test_gdf)} 条记录")
        logger.info(f"几何数据统计:")
        logger.info(f"  - 有几何数据: {test_gdf.geometry.notna().sum()} 条")
        logger.info(f"  - 无几何数据: {test_gdf.geometry.isna().sum()} 条")
        
        # 保存测试数据到临时文件
        with tempfile.NamedTemporaryFile(suffix='.geojson', delete=False) as tmp_file:
            test_gdf.to_file(tmp_file.name, driver='GeoJSON')
            temp_file_path = tmp_file.name
        
        logger.info(f"测试数据已保存到: {temp_file_path}")
        
        # 创建导入器
        importer = GISDataImporter(db_config)
        
        # 测试_safe_get_geometry方法
        logger.info("\n测试_safe_get_geometry方法:")
        for idx, row in test_gdf.iterrows():
            geometry, geometry_type = importer._safe_get_geometry(row, idx)
            logger.info(f"  行 {idx}: geometry={geometry is not None}, type={geometry_type}")
        
        # 创建测试表
        table_name = 'test_geometry_handling'
        logger.info(f"\n创建测试表: {table_name}")
        
        # 这里需要手动创建表结构，因为我们使用的是临时数据
        create_table_sql = f"""
        CREATE TABLE IF NOT EXISTS `{table_name}` (
            `id` varchar(64) NOT NULL COMMENT '主键ID',
            `layer_name` varchar(255) NOT NULL COMMENT '图层名称',
            `geometry_type` varchar(50) DEFAULT NULL COMMENT '几何类型',
            `geometry_wkt` LONGTEXT DEFAULT NULL COMMENT '几何数据(WKT格式)',
            `geometry_wkb` LONGBLOB DEFAULT NULL COMMENT '几何数据(WKB格式)',
            `lng` decimal(12,8) DEFAULT NULL COMMENT '经度',
            `lat` decimal(12,8) DEFAULT NULL COMMENT '纬度',
            `area` decimal(20,6) DEFAULT NULL COMMENT '面积',
            `perimeter` decimal(18,6) DEFAULT NULL COMMENT '周长',
            `name` varchar(255) DEFAULT NULL COMMENT '名称',
            `type` varchar(255) DEFAULT NULL COMMENT '类型',
            `value` int DEFAULT NULL COMMENT '数值',
            `description` varchar(500) DEFAULT NULL COMMENT '描述',
            `source_file` varchar(500) DEFAULT NULL COMMENT '源文件路径',
            `import_time` datetime DEFAULT NULL COMMENT '导入时间',
            `del_flag` char(1) DEFAULT '0' COMMENT '删除标志',
            `create_by` varchar(64) DEFAULT 'system' COMMENT '创建者',
            `create_time` datetime DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
            `update_by` varchar(64) DEFAULT 'system' COMMENT '更新者',
            `update_time` datetime DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
            `remark` varchar(500) DEFAULT NULL COMMENT '备注',
            PRIMARY KEY (`id`)
        ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='测试geometry处理表';
        """
        
        with importer.connection.cursor() as cursor:
            cursor.execute(f"DROP TABLE IF EXISTS `{table_name}`")
            cursor.execute(create_table_sql)
            importer.connection.commit()
        
        logger.info("测试表创建成功")
        
        # 手动处理数据导入（模拟import_layer_data的核心逻辑）
        logger.info("\n开始导入测试数据...")
        
        batch_records = []
        no_geometry_count = 0
        current_time = importer._get_current_time()
        
        for idx, row in test_gdf.iterrows():
            try:
                # 使用修改后的geometry处理逻辑
                geometry, geometry_type = importer._safe_get_geometry(row, idx)
                
                # 统计无geometry的记录
                if geometry is None:
                    no_geometry_count += 1
                
                # 处理坐标转换和计算
                converted_geometry = geometry
                geometry_wkt = converted_geometry.wkt if converted_geometry else None
                geometry_wkb = converted_geometry.wkb if converted_geometry else None
                
                # 计算坐标
                lng, lat = None, None
                if converted_geometry:
                    try:
                        if geometry_type == 'Point':
                            lng, lat = converted_geometry.x, converted_geometry.y
                        else:
                            centroid = converted_geometry.centroid
                            if centroid:
                                lng, lat = centroid.x, centroid.y
                        lng, lat = importer._validate_coordinates(lng, lat)
                    except Exception as e:
                        logger.warning(f"计算坐标失败: {e}")
                        lng, lat = None, None
                
                # 计算面积和周长
                area = geometry.area if geometry and hasattr(geometry, 'area') else None
                perimeter = geometry.length if geometry and hasattr(geometry, 'length') else None
                
                # 构建记录
                record = {
                    'id': importer._generate_uuid(),
                    'layer_name': 'test_layer',
                    'geometry_type': geometry_type,
                    'geometry_wkt': geometry_wkt,
                    'geometry_wkb': geometry_wkb,
                    'lng': lng,
                    'lat': lat,
                    'area': area,
                    'perimeter': perimeter,
                    'name': row['name'],
                    'type': row['type'],
                    'value': row['value'],
                    'description': row['description'],
                    'source_file': temp_file_path,
                    'import_time': current_time,
                    'del_flag': '0',
                    'create_by': 'system',
                    'create_time': current_time,
                    'update_by': 'system',
                    'update_time': current_time,
                    'remark': '测试数据导入'
                }
                
                batch_records.append(record)
                
            except Exception as e:
                logger.error(f"处理行 {idx} 失败: {e}")
                continue
        
        # 批量插入数据
        if batch_records:
            importer._batch_insert(table_name, batch_records)
            logger.info(f"成功导入 {len(batch_records)} 条记录")
            if no_geometry_count > 0:
                logger.info(f"其中 {no_geometry_count} 条记录缺失geometry数据，已正常插入")
        
        # 验证插入结果
        with importer.connection.cursor() as cursor:
            cursor.execute(f"SELECT COUNT(*) FROM `{table_name}`")
            total_count = cursor.fetchone()[0]
            
            cursor.execute(f"SELECT COUNT(*) FROM `{table_name}` WHERE geometry_type IS NULL")
            null_geometry_count = cursor.fetchone()[0]
            
            cursor.execute(f"SELECT COUNT(*) FROM `{table_name}` WHERE geometry_type IS NOT NULL")
            valid_geometry_count = cursor.fetchone()[0]
        
        logger.info(f"\n导入结果验证:")
        logger.info(f"  - 总记录数: {total_count}")
        logger.info(f"  - 有几何数据: {valid_geometry_count}")
        logger.info(f"  - 无几何数据: {null_geometry_count}")
        
        # 清理
        importer.close()
        os.unlink(temp_file_path)
        
        logger.info("\n测试完成！缺失geometry的行数据能够正常插入。")
        
    except Exception as e:
        logger.error(f"测试失败: {e}")
        raise

if __name__ == '__main__':
    test_geometry_handling()
