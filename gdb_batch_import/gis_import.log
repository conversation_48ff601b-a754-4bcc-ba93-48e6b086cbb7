2025-07-15 11:36:38,313 - INFO - 数据库连接成功
2025-07-15 11:36:38,349 - INFO - GDAL signalled an error: err_no=1, msg='PROJ: internal_proj_identify: /Users/<USER>/miniconda3/share/proj/proj.db contains DATABASE.LAYOUT.VERSION.MINOR = 2 whereas a number >= 3 is expected. It comes from another PROJ installation.'
2025-07-15 11:36:38,368 - INFO - 发现 3 个图层: ['GHFW', 'DYHF', 'DYCMYDBJGH']
2025-07-15 11:36:38,368 - INFO - 发现 3 个图层: ['GHFW', 'DYHF', 'DYCMYDBJGH']
2025-07-15 11:36:38,368 - INFO - 正在为图层 'GHFW' 创建表: 2_ghfw
2025-07-15 11:36:38,455 - INFO - 图层 GHFW 字段分析完成: {'SHAPE_Length': 'DECIMAL(18,6)', 'SHAPE_Area': 'DECIMAL(20,6)', 'BSM': 'VARCHAR(255)', 'YSDM': 'VARCHAR(255)', 'XZQDM': 'VARCHAR(255)', 'XZQMC': 'VARCHAR(255)', 'GHLX': 'VARCHAR(255)', 'GHCM': 'VARCHAR(255)', 'GHXMMC': 'VARCHAR(255)', 'GHMJ': 'DECIMAL(10,4)', 'GHFW': 'VARCHAR(255)', 'GHBZDW': 'VARCHAR(255)', 'GHZZBM': 'VARCHAR(255)', 'PZBM': 'VARCHAR(255)', 'PZSJ': 'DATETIME', 'PZWH': 'VARCHAR(255)', 'BZ': 'VARCHAR(255)'}
2025-07-15 11:36:38,461 - INFO - 表 2_ghfw 创建成功
2025-07-15 11:36:38,461 - INFO - 开始读取图层: GHFW
2025-07-15 11:36:38,462 - INFO - 图层 GHFW 包含 1 个要素
2025-07-15 11:36:38,462 - INFO - 图层字段: ['SHAPE_Length', 'SHAPE_Area', 'BSM', 'YSDM', 'XZQDM', 'XZQMC', 'GHLX', 'GHCM', 'GHXMMC', 'GHMJ', 'GHFW', 'GHBZDW', 'GHZZBM', 'PZBM', 'PZSJ', 'PZWH', 'BZ', 'geometry']
2025-07-15 11:36:38,514 - INFO - 已导入 1/1 个要素
2025-07-15 11:36:38,514 - INFO - 图层 GHFW 导入完成，共导入 1 个要素
2025-07-15 11:36:38,514 - INFO - ✓ 图层 'GHFW' 成功导入到表 '2_ghfw'
2025-07-15 11:36:38,514 - INFO - 正在为图层 'DYHF' 创建表: 2_dyhf
2025-07-15 11:36:38,517 - INFO - 图层 DYHF 字段分析完成: {'SHAPE_Length': 'DECIMAL(18,6)', 'SHAPE_Area': 'DECIMAL(20,6)', 'BSM': 'VARCHAR(255)', 'YSDM': 'VARCHAR(255)', 'XZQDM': 'VARCHAR(255)', 'XZQMC': 'VARCHAR(255)', 'DYBH': 'VARCHAR(255)', 'DYMC': 'VARCHAR(255)', 'DYLX': 'VARCHAR(255)', 'DYMJ': 'DECIMAL(10,4)', 'ZDGN': 'VARCHAR(255)', 'JSYDGM': 'DECIMAL(10,4)', 'BZ': 'VARCHAR(255)'}
2025-07-15 11:36:38,518 - INFO - 表 2_dyhf 创建成功
2025-07-15 11:36:38,518 - INFO - 开始读取图层: DYHF
2025-07-15 11:36:38,520 - INFO - 图层 DYHF 包含 1 个要素
2025-07-15 11:36:38,520 - INFO - 图层字段: ['SHAPE_Length', 'SHAPE_Area', 'BSM', 'YSDM', 'XZQDM', 'XZQMC', 'DYBH', 'DYMC', 'DYLX', 'DYMJ', 'ZDGN', 'JSYDGM', 'BZ', 'geometry']
2025-07-15 11:36:38,557 - INFO - 已导入 1/1 个要素
2025-07-15 11:36:38,557 - INFO - 图层 DYHF 导入完成，共导入 1 个要素
2025-07-15 11:36:38,557 - INFO - ✓ 图层 'DYHF' 成功导入到表 '2_dyhf'
2025-07-15 11:36:38,557 - INFO - 正在为图层 'DYCMYDBJGH' 创建表: 2_dycmydbjgh
2025-07-15 11:36:38,562 - INFO - 图层 DYCMYDBJGH 字段分析完成: {'SHAPE_Length': 'DECIMAL(18,6)', 'SHAPE_Area': 'DECIMAL(20,6)', 'BSM': 'VARCHAR(255)', 'YSDM': 'VARCHAR(255)', 'XZQDM': 'VARCHAR(255)', 'XZQMC': 'VARCHAR(255)', 'XXGHBZDYBH': 'VARCHAR(255)', 'XXGHBZDYMC': 'VARCHAR(255)', 'DKBH': 'VARCHAR(255)', 'CSLXKZMJ': 'DECIMAL(15,6)', 'CSLVXKZMJ': 'DECIMAL(15,6)', 'CSHXKZMJ': 'DECIMAL(10,4)', 'CSZXKZMJ': 'DECIMAL(10,4)', 'YDYHFLDM': 'VARCHAR(255)', 'YDYHFLMC': 'VARCHAR(255)', 'YDMJ': 'DECIMAL(10,4)', 'JSYDMJ': 'DECIMAL(10,4)', 'JSYDJG': 'VARCHAR(255)', 'DXKJGH': 'VARCHAR(255)', 'BZ': 'VARCHAR(255)'}
2025-07-15 11:36:38,563 - INFO - 表 2_dycmydbjgh 创建成功
2025-07-15 11:36:38,563 - INFO - 开始读取图层: DYCMYDBJGH
2025-07-15 11:36:38,568 - INFO - 图层 DYCMYDBJGH 包含 327 个要素
2025-07-15 11:36:38,568 - INFO - 图层字段: ['SHAPE_Length', 'SHAPE_Area', 'BSM', 'YSDM', 'XZQDM', 'XZQMC', 'XXGHBZDYBH', 'XXGHBZDYMC', 'DKBH', 'CSLXKZMJ', 'CSLVXKZMJ', 'CSHXKZMJ', 'CSZXKZMJ', 'YDYHFLDM', 'YDYHFLMC', 'YDMJ', 'JSYDMJ', 'JSYDJG', 'DXKJGH', 'BZ', 'geometry']
2025-07-15 11:36:38,833 - INFO - 已导入 327/327 个要素
2025-07-15 11:36:38,834 - INFO - 图层 DYCMYDBJGH 导入完成，共导入 327 个要素
2025-07-15 11:36:38,834 - INFO - ✓ 图层 'DYCMYDBJGH' 成功导入到表 '2_dycmydbjgh'
2025-07-15 11:36:38,834 - INFO - 导入完成: 3/3 个图层成功
2025-07-15 11:36:38,834 - INFO - 创建的表: ['2_ghfw', '2_dyhf', '2_dycmydbjgh']
2025-07-15 11:38:27,360 - INFO - 数据库连接成功
2025-07-15 11:38:27,399 - INFO - GDAL signalled an error: err_no=1, msg='PROJ: internal_proj_identify: /Users/<USER>/miniconda3/share/proj/proj.db contains DATABASE.LAYOUT.VERSION.MINOR = 2 whereas a number >= 3 is expected. It comes from another PROJ installation.'
2025-07-15 11:38:27,418 - INFO - 发现 3 个图层: ['GHFW', 'DYHF', 'DYCMYDBJGH']
2025-07-15 11:38:27,418 - INFO - 发现 3 个图层: ['GHFW', 'DYHF', 'DYCMYDBJGH']
2025-07-15 11:38:27,418 - INFO - 正在为图层 'GHFW' 创建表: 2_ghfw
2025-07-15 11:38:27,521 - INFO - 图层 GHFW 字段分析完成: {'SHAPE_Length': 'DECIMAL(18,6)', 'SHAPE_Area': 'DECIMAL(20,6)', 'BSM': 'VARCHAR(255)', 'YSDM': 'VARCHAR(255)', 'XZQDM': 'VARCHAR(255)', 'XZQMC': 'VARCHAR(255)', 'GHLX': 'VARCHAR(255)', 'GHCM': 'VARCHAR(255)', 'GHXMMC': 'VARCHAR(255)', 'GHMJ': 'DECIMAL(10,4)', 'GHFW': 'VARCHAR(255)', 'GHBZDW': 'VARCHAR(255)', 'GHZZBM': 'VARCHAR(255)', 'PZBM': 'VARCHAR(255)', 'PZSJ': 'DATETIME', 'PZWH': 'VARCHAR(255)', 'BZ': 'VARCHAR(255)'}
2025-07-15 11:38:27,527 - INFO - 表 2_ghfw 创建成功
2025-07-15 11:38:27,527 - INFO - 开始读取图层: GHFW
2025-07-15 11:38:27,529 - INFO - 图层 GHFW 包含 1 个要素
2025-07-15 11:38:27,529 - INFO - 图层字段: ['SHAPE_Length', 'SHAPE_Area', 'BSM', 'YSDM', 'XZQDM', 'XZQMC', 'GHLX', 'GHCM', 'GHXMMC', 'GHMJ', 'GHFW', 'GHBZDW', 'GHZZBM', 'PZBM', 'PZSJ', 'PZWH', 'BZ', 'geometry']
2025-07-15 11:38:27,587 - INFO - 已导入 1/1 个要素
2025-07-15 11:38:27,587 - INFO - 图层 GHFW 导入完成，共导入 1 个要素
2025-07-15 11:38:27,587 - INFO - ✓ 图层 'GHFW' 成功导入到表 '2_ghfw'
2025-07-15 11:38:27,587 - INFO - 正在为图层 'DYHF' 创建表: 2_dyhf
2025-07-15 11:38:27,590 - INFO - 图层 DYHF 字段分析完成: {'SHAPE_Length': 'DECIMAL(18,6)', 'SHAPE_Area': 'DECIMAL(20,6)', 'BSM': 'VARCHAR(255)', 'YSDM': 'VARCHAR(255)', 'XZQDM': 'VARCHAR(255)', 'XZQMC': 'VARCHAR(255)', 'DYBH': 'VARCHAR(255)', 'DYMC': 'VARCHAR(255)', 'DYLX': 'VARCHAR(255)', 'DYMJ': 'DECIMAL(10,4)', 'ZDGN': 'VARCHAR(255)', 'JSYDGM': 'DECIMAL(10,4)', 'BZ': 'VARCHAR(255)'}
2025-07-15 11:38:27,591 - INFO - 表 2_dyhf 创建成功
2025-07-15 11:38:27,591 - INFO - 开始读取图层: DYHF
2025-07-15 11:38:27,593 - INFO - 图层 DYHF 包含 1 个要素
2025-07-15 11:38:27,593 - INFO - 图层字段: ['SHAPE_Length', 'SHAPE_Area', 'BSM', 'YSDM', 'XZQDM', 'XZQMC', 'DYBH', 'DYMC', 'DYLX', 'DYMJ', 'ZDGN', 'JSYDGM', 'BZ', 'geometry']
2025-07-15 11:38:27,622 - INFO - 已导入 1/1 个要素
2025-07-15 11:38:27,622 - INFO - 图层 DYHF 导入完成，共导入 1 个要素
2025-07-15 11:38:27,622 - INFO - ✓ 图层 'DYHF' 成功导入到表 '2_dyhf'
2025-07-15 11:38:27,622 - INFO - 正在为图层 'DYCMYDBJGH' 创建表: 2_dycmydbjgh
2025-07-15 11:38:27,627 - INFO - 图层 DYCMYDBJGH 字段分析完成: {'SHAPE_Length': 'DECIMAL(18,6)', 'SHAPE_Area': 'DECIMAL(20,6)', 'BSM': 'VARCHAR(255)', 'YSDM': 'VARCHAR(255)', 'XZQDM': 'VARCHAR(255)', 'XZQMC': 'VARCHAR(255)', 'XXGHBZDYBH': 'VARCHAR(255)', 'XXGHBZDYMC': 'VARCHAR(255)', 'DKBH': 'VARCHAR(255)', 'CSLXKZMJ': 'DECIMAL(15,6)', 'CSLVXKZMJ': 'DECIMAL(15,6)', 'CSHXKZMJ': 'DECIMAL(10,4)', 'CSZXKZMJ': 'DECIMAL(10,4)', 'YDYHFLDM': 'VARCHAR(255)', 'YDYHFLMC': 'VARCHAR(255)', 'YDMJ': 'DECIMAL(10,4)', 'JSYDMJ': 'DECIMAL(10,4)', 'JSYDJG': 'VARCHAR(255)', 'DXKJGH': 'VARCHAR(255)', 'BZ': 'VARCHAR(255)'}
2025-07-15 11:38:27,628 - INFO - 表 2_dycmydbjgh 创建成功
2025-07-15 11:38:27,628 - INFO - 开始读取图层: DYCMYDBJGH
2025-07-15 11:38:27,634 - INFO - 图层 DYCMYDBJGH 包含 327 个要素
2025-07-15 11:38:27,634 - INFO - 图层字段: ['SHAPE_Length', 'SHAPE_Area', 'BSM', 'YSDM', 'XZQDM', 'XZQMC', 'XXGHBZDYBH', 'XXGHBZDYMC', 'DKBH', 'CSLXKZMJ', 'CSLVXKZMJ', 'CSHXKZMJ', 'CSZXKZMJ', 'YDYHFLDM', 'YDYHFLMC', 'YDMJ', 'JSYDMJ', 'JSYDJG', 'DXKJGH', 'BZ', 'geometry']
2025-07-15 11:38:27,907 - INFO - 已导入 327/327 个要素
2025-07-15 11:38:27,907 - INFO - 图层 DYCMYDBJGH 导入完成，共导入 327 个要素
2025-07-15 11:38:27,908 - INFO - ✓ 图层 'DYCMYDBJGH' 成功导入到表 '2_dycmydbjgh'
2025-07-15 11:38:27,908 - INFO - 导入完成: 3/3 个图层成功
2025-07-15 11:38:27,908 - INFO - 创建的表: ['2_ghfw', '2_dyhf', '2_dycmydbjgh']
2025-07-29 18:03:08,865 - ERROR - 数据库连接失败: (2003, "Can't connect to MySQL server on 'localhost' ([Errno 61] Connection refused)")
2025-07-29 18:04:51,938 - ERROR - 数据库连接失败: (1045, "Access denied for user 'root'@'***********' (using password: YES)")
2025-07-29 18:05:42,712 - INFO - 数据库连接成功
2025-07-29 18:05:42,758 - INFO - GDAL signalled an error: err_no=4, msg="`/Users/<USER>/Downloads/部下发更新永久基本农田数据库/510903船山区YJJBNTGX.gdb' not recognized as being in a supported file format."
2025-07-29 18:05:42,758 - ERROR - 读取GDB图层失败: Failed to open dataset (flags=68): /Users/<USER>/Downloads/部下发更新永久基本农田数据库/510903船山区YJJBNTGX.gdb
2025-07-29 18:05:42,758 - WARNING - 未发现任何图层
2025-07-29 18:06:14,135 - INFO - 数据库连接成功
2025-07-29 18:06:14,179 - INFO - GDAL signalled an error: err_no=4, msg="`/Users/<USER>/Downloads/部下发更新永久基本农田数据库/510921蓬溪县YJJBNTGX.gdb' not recognized as being in a supported file format."
2025-07-29 18:06:14,180 - ERROR - 读取GDB图层失败: Failed to open dataset (flags=68): /Users/<USER>/Downloads/部下发更新永久基本农田数据库/510921蓬溪县YJJBNTGX.gdb
2025-07-29 18:06:14,180 - WARNING - 未发现任何图层
2025-07-29 18:06:27,744 - INFO - 数据库连接成功
2025-07-29 18:06:27,788 - INFO - GDAL signalled an error: err_no=4, msg="`/Users/<USER>/Downloads/部下发更新永久基本农田数据库/510981射洪市YJJBNTGX.gdb' not recognized as being in a supported file format."
2025-07-29 18:06:27,788 - ERROR - 读取GDB图层失败: Failed to open dataset (flags=68): /Users/<USER>/Downloads/部下发更新永久基本农田数据库/510981射洪市YJJBNTGX.gdb
2025-07-29 18:06:27,788 - WARNING - 未发现任何图层
2025-07-29 18:06:40,063 - INFO - 数据库连接成功
2025-07-29 18:06:40,105 - INFO - GDAL signalled an error: err_no=4, msg="`/Users/<USER>/Downloads/部下发更新永久基本农田数据库/510904安居区YJJBNTGX.gdb' not recognized as being in a supported file format."
2025-07-29 18:06:40,105 - ERROR - 读取GDB图层失败: Failed to open dataset (flags=68): /Users/<USER>/Downloads/部下发更新永久基本农田数据库/510904安居区YJJBNTGX.gdb
2025-07-29 18:06:40,105 - WARNING - 未发现任何图层
2025-07-29 18:06:49,660 - INFO - 数据库连接成功
2025-07-29 18:06:49,700 - INFO - GDAL signalled an error: err_no=4, msg="`/Users/<USER>/Downloads/部下发更新永久基本农田数据库/510923大英县YJJBNTGX.gdb' not recognized as being in a supported file format."
2025-07-29 18:06:49,700 - ERROR - 读取GDB图层失败: Failed to open dataset (flags=68): /Users/<USER>/Downloads/部下发更新永久基本农田数据库/510923大英县YJJBNTGX.gdb
2025-07-29 18:06:49,700 - WARNING - 未发现任何图层
2025-08-04 16:51:13,815 - INFO - 数据库连接成功
2025-08-04 16:51:13,859 - INFO - GDAL signalled an error: err_no=1, msg='PROJ: internal_proj_identify: /Users/<USER>/miniconda3/share/proj/proj.db contains DATABASE.LAYOUT.VERSION.MINOR = 2 whereas a number >= 3 is expected. It comes from another PROJ installation.'
2025-08-04 16:51:13,911 - INFO - GDAL signalled an error: err_no=1, msg='PROJ: internal_proj_identify: /Users/<USER>/miniconda3/share/proj/proj.db contains DATABASE.LAYOUT.VERSION.MINOR = 2 whereas a number >= 3 is expected. It comes from another PROJ installation.'
2025-08-04 16:51:13,932 - INFO - GDAL signalled an error: err_no=1, msg='PROJ: internal_proj_identify: /Users/<USER>/miniconda3/share/proj/proj.db contains DATABASE.LAYOUT.VERSION.MINOR = 2 whereas a number >= 3 is expected. It comes from another PROJ installation.'
2025-08-04 16:51:13,945 - INFO - 发现 9 个图层: ['县级行政区_FeatureToLine', '市级行政区_FeatureToLine', '县级行政区_FeatureToLine1', '县级行政区_FeatureToLine2', '经开区管辖范围线Polyli_FeatureToLine', 'ShorelineContr_FeatureToPoly', 'ShorelineContr_FeatureToPoly1', 'SSCMYDBJGH_ExportTable', 'SSCMYDBJGH_ExportTable3']
2025-08-04 16:51:13,945 - INFO - 发现 9 个图层: ['县级行政区_FeatureToLine', '市级行政区_FeatureToLine', '县级行政区_FeatureToLine1', '县级行政区_FeatureToLine2', '经开区管辖范围线Polyli_FeatureToLine', 'ShorelineContr_FeatureToPoly', 'ShorelineContr_FeatureToPoly1', 'SSCMYDBJGH_ExportTable', 'SSCMYDBJGH_ExportTable3']
2025-08-04 16:51:13,945 - INFO - 正在为图层 '县级行政区_FeatureToLine' 创建表: 510903xx_featuretoline
2025-08-04 16:51:14,075 - INFO - 图层 县级行政区_FeatureToLine 字段分析完成: {'FID_XZQDS': 'TINYINT', 'BSM': 'VARCHAR(255)', 'YSDM': 'VARCHAR(255)', 'XZQDM': 'VARCHAR(255)', 'XZQMC': 'VARCHAR(255)', 'JSMJ': 'DECIMAL(20,2)', 'BZ': 'VARCHAR(255)', 'Shape_Length': 'DECIMAL(18,6)'}
2025-08-04 16:51:14,289 - INFO - 表 510903xx_featuretoline 创建成功
2025-08-04 16:51:14,289 - INFO - 开始读取图层: 县级行政区_FeatureToLine
2025-08-04 16:51:14,295 - INFO - 图层 县级行政区_FeatureToLine 包含 26 个要素
2025-08-04 16:51:14,295 - INFO - 图层字段: ['FID_XZQDS', 'BSM', 'YSDM', 'XZQDM', 'XZQMC', 'JSMJ', 'BZ', 'Shape_Length', 'geometry']
2025-08-04 16:53:42,596 - INFO - 已导入 26/26 个要素
2025-08-04 16:53:42,597 - INFO - 图层 县级行政区_FeatureToLine 导入完成，共导入 26 个要素
2025-08-04 16:53:42,600 - INFO - ✓ 图层 '县级行政区_FeatureToLine' 成功导入到表 '510903xx_featuretoline'
2025-08-04 16:53:42,600 - INFO - 正在为图层 '市级行政区_FeatureToLine' 创建表: 510903xx_featuretoline
2025-08-04 16:53:42,615 - INFO - 图层 市级行政区_FeatureToLine 字段分析完成: {'FID_XZQDS': 'TINYINT', 'BSM': 'VARCHAR(255)', 'YSDM': 'VARCHAR(255)', 'XZQDM': 'VARCHAR(255)', 'XZQMC': 'VARCHAR(255)', 'JSMJ': 'DECIMAL(20,2)', 'BZ': 'VARCHAR(255)', 'Shape_Length': 'DECIMAL(18,6)'}
2025-08-04 16:53:43,732 - INFO - 表 510903xx_featuretoline 创建成功
2025-08-04 16:53:43,732 - INFO - 开始读取图层: 市级行政区_FeatureToLine
2025-08-04 16:53:43,741 - INFO - 图层 市级行政区_FeatureToLine 包含 26 个要素
2025-08-04 16:53:43,741 - INFO - 图层字段: ['FID_XZQDS', 'BSM', 'YSDM', 'XZQDM', 'XZQMC', 'JSMJ', 'BZ', 'Shape_Length', 'geometry']
2025-08-04 16:56:04,314 - INFO - 已导入 26/26 个要素
2025-08-04 16:56:04,315 - INFO - 图层 市级行政区_FeatureToLine 导入完成，共导入 26 个要素
2025-08-04 16:56:04,317 - INFO - ✓ 图层 '市级行政区_FeatureToLine' 成功导入到表 '510903xx_featuretoline'
2025-08-04 16:56:04,318 - INFO - 正在为图层 '县级行政区_FeatureToLine1' 创建表: 510903xx_featuretoline1
2025-08-04 16:56:04,327 - INFO - 图层 县级行政区_FeatureToLine1 字段分析完成: {'FID_XZQXS': 'BIGINT', 'BSM': 'VARCHAR(255)', 'YSDM': 'VARCHAR(255)', 'XZQDM': 'VARCHAR(255)', 'XZQMC': 'VARCHAR(255)', 'JSMJ': 'DECIMAL(15,6)', 'BZ': 'VARCHAR(255)', 'Shape_Length': 'DECIMAL(15,6)'}
2025-08-04 16:56:04,582 - INFO - 表 510903xx_featuretoline1 创建成功
2025-08-04 16:56:04,583 - INFO - 开始读取图层: 县级行政区_FeatureToLine1
2025-08-04 16:56:04,585 - WARNING - 图层 县级行政区_FeatureToLine1 为空
2025-08-04 16:56:04,585 - INFO - ✓ 图层 '县级行政区_FeatureToLine1' 成功导入到表 '510903xx_featuretoline1'
2025-08-04 16:56:04,585 - INFO - 正在为图层 '县级行政区_FeatureToLine2' 创建表: 510903xx_featuretoline2
2025-08-04 16:56:04,592 - INFO - 图层 县级行政区_FeatureToLine2 字段分析完成: {'FID_XZQXS': 'TINYINT', 'BSM': 'VARCHAR(255)', 'YSDM': 'VARCHAR(255)', 'XZQDM': 'VARCHAR(255)', 'XZQMC': 'VARCHAR(255)', 'JSMJ': 'DECIMAL(20,1)', 'BZ': 'VARCHAR(255)', 'Shape_Length': 'DECIMAL(18,6)'}
2025-08-04 16:56:04,827 - INFO - 表 510903xx_featuretoline2 创建成功
2025-08-04 16:56:04,827 - INFO - 开始读取图层: 县级行政区_FeatureToLine2
2025-08-04 16:56:04,830 - INFO - 图层 县级行政区_FeatureToLine2 包含 1 个要素
2025-08-04 16:56:04,830 - INFO - 图层字段: ['FID_XZQXS', 'BSM', 'YSDM', 'XZQDM', 'XZQMC', 'JSMJ', 'BZ', 'Shape_Length', 'geometry']
2025-08-04 16:56:35,856 - INFO - 已导入 1/1 个要素
2025-08-04 16:56:35,856 - INFO - 图层 县级行政区_FeatureToLine2 导入完成，共导入 1 个要素
2025-08-04 16:56:35,858 - INFO - ✓ 图层 '县级行政区_FeatureToLine2' 成功导入到表 '510903xx_featuretoline2'
2025-08-04 16:56:35,858 - INFO - 正在为图层 '经开区管辖范围线Polyli_FeatureToLine' 创建表: 510903xx_polyli_featuretoline
2025-08-04 16:56:35,886 - INFO - 图层 经开区管辖范围线Polyli_FeatureToLine 字段分析完成: {'FID_Polyline': 'TINYINT', 'Entity': 'VARCHAR(255)', 'Handle': 'VARCHAR(255)', 'Layer': 'VARCHAR(255)', 'LyrFrzn': 'TINYINT', 'LyrLock': 'TINYINT', 'LyrOn': 'TINYINT', 'LyrVPFrzn': 'TINYINT', 'LyrHandle': 'VARCHAR(255)', 'Color': 'TINYINT', 'EntColor': 'TINYINT', 'LyrColor': 'TINYINT', 'BlkColor': 'TINYINT', 'Linetype': 'VARCHAR(255)', 'EntLinetype': 'VARCHAR(255)', 'LyrLnType': 'VARCHAR(255)', 'BlkLinetype': 'VARCHAR(255)', 'Elevation': 'DECIMAL(10,1)', 'Thickness': 'DECIMAL(10,1)', 'LineWt': 'TINYINT', 'EntLineWt': 'TINYINT', 'LyrLineWt': 'TINYINT', 'BlkLineWt': 'TINYINT', 'RefName': 'VARCHAR(255)', 'LTScale': 'DECIMAL(10,1)', 'ExtX': 'DECIMAL(10,1)', 'ExtY': 'DECIMAL(10,1)', 'ExtZ': 'DECIMAL(10,1)', 'DocName': 'VARCHAR(255)', 'DocPath': 'VARCHAR(255)', 'DocType': 'VARCHAR(255)', 'DocVer': 'VARCHAR(255)', 'DocUpdate': 'DATETIME', 'DocId': 'DECIMAL(20,0)', 'Shape_Length': 'DECIMAL(18,6)'}
2025-08-04 16:56:36,369 - INFO - 表 510903xx_polyli_featuretoline 创建成功
2025-08-04 16:56:36,370 - INFO - 开始读取图层: 经开区管辖范围线Polyli_FeatureToLine
2025-08-04 16:56:36,373 - INFO - 图层 经开区管辖范围线Polyli_FeatureToLine 包含 12 个要素
2025-08-04 16:56:36,373 - INFO - 图层字段: ['FID_Polyline', 'Entity', 'Handle', 'Layer', 'LyrFrzn', 'LyrLock', 'LyrOn', 'LyrVPFrzn', 'LyrHandle', 'Color', 'EntColor', 'LyrColor', 'BlkColor', 'Linetype', 'EntLinetype', 'LyrLnType', 'BlkLinetype', 'Elevation', 'Thickness', 'LineWt', 'EntLineWt', 'LyrLineWt', 'BlkLineWt', 'RefName', 'LTScale', 'ExtX', 'ExtY', 'ExtZ', 'DocName', 'DocPath', 'DocType', 'DocVer', 'DocUpdate', 'DocId', 'Shape_Length', 'geometry']
2025-08-04 16:56:38,385 - INFO - 已导入 12/12 个要素
2025-08-04 16:56:38,386 - INFO - 图层 经开区管辖范围线Polyli_FeatureToLine 导入完成，共导入 12 个要素
2025-08-04 16:56:38,386 - INFO - ✓ 图层 '经开区管辖范围线Polyli_FeatureToLine' 成功导入到表 '510903xx_polyli_featuretoline'
2025-08-04 16:56:38,386 - INFO - 正在为图层 'ShorelineContr_FeatureToPoly' 创建表: 510903xx_shorelinecontr_featuretopoly
2025-08-04 16:56:38,390 - INFO - 图层 ShorelineContr_FeatureToPoly 字段分析完成: {'Shape_Length': 'DECIMAL(15,6)', 'Shape_Area': 'DECIMAL(15,6)'}
2025-08-04 16:56:38,939 - INFO - 表 510903xx_shorelinecontr_featuretopoly 创建成功
2025-08-04 16:56:38,939 - INFO - 开始读取图层: ShorelineContr_FeatureToPoly
2025-08-04 16:56:38,944 - WARNING - 图层 ShorelineContr_FeatureToPoly 为空
2025-08-04 16:56:38,944 - INFO - ✓ 图层 'ShorelineContr_FeatureToPoly' 成功导入到表 '510903xx_shorelinecontr_featuretopoly'
2025-08-04 16:56:38,944 - INFO - 正在为图层 'ShorelineContr_FeatureToPoly1' 创建表: 510903xx_shorelinecontr_featuretopoly1
2025-08-04 16:56:38,950 - INFO - 图层 ShorelineContr_FeatureToPoly1 字段分析完成: {'Shape_Length': 'DECIMAL(18,6)', 'Shape_Area': 'DECIMAL(20,6)'}
2025-08-04 16:56:39,176 - INFO - 表 510903xx_shorelinecontr_featuretopoly1 创建成功
2025-08-04 16:56:39,176 - INFO - 开始读取图层: ShorelineContr_FeatureToPoly1
2025-08-04 16:56:39,179 - INFO - 图层 ShorelineContr_FeatureToPoly1 包含 1 个要素
2025-08-04 16:56:39,179 - INFO - 图层字段: ['Shape_Length', 'Shape_Area', 'geometry']
2025-08-04 16:56:49,177 - INFO - 已导入 1/1 个要素
2025-08-04 16:56:49,177 - INFO - 图层 ShorelineContr_FeatureToPoly1 导入完成，共导入 1 个要素
2025-08-04 16:56:49,177 - INFO - ✓ 图层 'ShorelineContr_FeatureToPoly1' 成功导入到表 '510903xx_shorelinecontr_featuretopoly1'
2025-08-04 16:56:49,177 - INFO - 正在为图层 'SSCMYDBJGH_ExportTable' 创建表: 510903xx_sscmydbjgh_exporttable
2025-08-04 16:56:49,186 - INFO - 图层 SSCMYDBJGH_ExportTable 字段分析完成: {'name': 'VARCHAR(255)', 'BSM': 'VARCHAR(255)', 'YSDM': 'VARCHAR(255)', 'XZQDM': 'VARCHAR(255)', 'XZQMC': 'VARCHAR(255)', 'XXGHBZDYBH': 'VARCHAR(255)', 'XXGHBZDYMC': 'VARCHAR(255)', 'DKBH': 'VARCHAR(255)', 'CSLXKZMJ': 'DECIMAL(15,6)', 'CSLVXKZMJ': 'DECIMAL(15,6)', 'CSHXKZMJ': 'DECIMAL(10,4)', 'CSZXKZMJ': 'DECIMAL(15,6)', 'YDYHFLDM': 'VARCHAR(255)', 'YDYHFLMC': 'VARCHAR(255)', 'YDMJ': 'DECIMAL(10,4)', 'DXKJGH': 'VARCHAR(255)', 'BZ': 'VARCHAR(255)', 'Shape_Length': 'DECIMAL(18,6)', 'Shape_Area': 'DECIMAL(20,6)', 'JQBH': 'VARCHAR(255)', 'JQMC': 'VARCHAR(255)', 'ZJZMJ': 'DECIMAL(10,4)', 'RJL': 'VARCHAR(255)', 'JZXG': 'VARCHAR(255)', 'LVDL': 'VARCHAR(255)', 'JZMD': 'VARCHAR(255)', 'ZZNJLZLKZL': 'VARCHAR(255)', 'XCSLDL': 'VARCHAR(255)', 'TSPZL': 'VARCHAR(255)', 'LSWDL': 'VARCHAR(255)', 'TCWPB': 'VARCHAR(255)', 'YDKBH': 'VARCHAR(255)', 'JDCCRK': 'VARCHAR(255)', 'JZKK': 'VARCHAR(255)', 'TJSFYCY': 'VARCHAR(255)', 'QTPTSFYCY': 'VARCHAR(255)', 'OBJECTID': 'TINYINT', 'Join_Count': 'TINYINT', 'TARGET_FID': 'SMALLINT', 'BSM_1': 'VARCHAR(255)', 'Shape_Length_1': 'DECIMAL(18,6)', 'Shape_Area_1': 'DECIMAL(20,6)'}
2025-08-04 16:56:49,408 - INFO - 表 510903xx_sscmydbjgh_exporttable 创建成功
2025-08-04 16:56:49,408 - INFO - 开始读取图层: SSCMYDBJGH_ExportTable
2025-08-04 16:56:49,420 - INFO - 图层 SSCMYDBJGH_ExportTable 包含 783 个要素
2025-08-04 16:56:49,420 - INFO - 图层字段: ['name', 'BSM', 'YSDM', 'XZQDM', 'XZQMC', 'XXGHBZDYBH', 'XXGHBZDYMC', 'DKBH', 'CSLXKZMJ', 'CSLVXKZMJ', 'CSHXKZMJ', 'CSZXKZMJ', 'YDYHFLDM', 'YDYHFLMC', 'YDMJ', 'DXKJGH', 'BZ', 'Shape_Length', 'Shape_Area', 'JQBH', 'JQMC', 'ZJZMJ', 'RJL', 'JZXG', 'LVDL', 'JZMD', 'ZZNJLZLKZL', 'XCSLDL', 'TSPZL', 'LSWDL', 'TCWPB', 'YDKBH', 'JDCCRK', 'JZKK', 'TJSFYCY', 'QTPTSFYCY', 'OBJECTID', 'Join_Count', 'TARGET_FID', 'BSM_1', 'Shape_Length_1', 'Shape_Area_1']
2025-08-04 16:56:49,420 - ERROR - 处理要素 0 失败: 'Series' object has no attribute 'geometry'
2025-08-04 16:56:49,420 - ERROR - 处理要素 1 失败: 'Series' object has no attribute 'geometry'
2025-08-04 16:56:49,420 - ERROR - 处理要素 2 失败: 'Series' object has no attribute 'geometry'
2025-08-04 16:56:49,421 - ERROR - 处理要素 3 失败: 'Series' object has no attribute 'geometry'
2025-08-04 16:56:49,421 - ERROR - 处理要素 4 失败: 'Series' object has no attribute 'geometry'
2025-08-04 16:56:49,421 - ERROR - 处理要素 5 失败: 'Series' object has no attribute 'geometry'
2025-08-04 16:56:49,421 - ERROR - 处理要素 6 失败: 'Series' object has no attribute 'geometry'
2025-08-04 16:56:49,421 - ERROR - 处理要素 7 失败: 'Series' object has no attribute 'geometry'
2025-08-04 16:56:49,421 - ERROR - 处理要素 8 失败: 'Series' object has no attribute 'geometry'
2025-08-04 16:56:49,421 - ERROR - 处理要素 9 失败: 'Series' object has no attribute 'geometry'
2025-08-04 16:56:49,421 - ERROR - 处理要素 10 失败: 'Series' object has no attribute 'geometry'
2025-08-04 16:56:49,421 - ERROR - 处理要素 11 失败: 'Series' object has no attribute 'geometry'
2025-08-04 16:56:49,421 - ERROR - 处理要素 12 失败: 'Series' object has no attribute 'geometry'
2025-08-04 16:56:49,421 - ERROR - 处理要素 13 失败: 'Series' object has no attribute 'geometry'
2025-08-04 16:56:49,421 - ERROR - 处理要素 14 失败: 'Series' object has no attribute 'geometry'
2025-08-04 16:56:49,421 - ERROR - 处理要素 15 失败: 'Series' object has no attribute 'geometry'
2025-08-04 16:56:49,421 - ERROR - 处理要素 16 失败: 'Series' object has no attribute 'geometry'
2025-08-04 16:56:49,421 - ERROR - 处理要素 17 失败: 'Series' object has no attribute 'geometry'
2025-08-04 16:56:49,421 - ERROR - 处理要素 18 失败: 'Series' object has no attribute 'geometry'
2025-08-04 16:56:49,421 - ERROR - 处理要素 19 失败: 'Series' object has no attribute 'geometry'
2025-08-04 16:56:49,421 - ERROR - 处理要素 20 失败: 'Series' object has no attribute 'geometry'
2025-08-04 16:56:49,421 - ERROR - 处理要素 21 失败: 'Series' object has no attribute 'geometry'
2025-08-04 16:56:49,422 - ERROR - 处理要素 22 失败: 'Series' object has no attribute 'geometry'
2025-08-04 16:56:49,422 - ERROR - 处理要素 23 失败: 'Series' object has no attribute 'geometry'
2025-08-04 16:56:49,422 - ERROR - 处理要素 24 失败: 'Series' object has no attribute 'geometry'
2025-08-04 16:56:49,422 - ERROR - 处理要素 25 失败: 'Series' object has no attribute 'geometry'
2025-08-04 16:56:49,422 - ERROR - 处理要素 26 失败: 'Series' object has no attribute 'geometry'
2025-08-04 16:56:49,422 - ERROR - 处理要素 27 失败: 'Series' object has no attribute 'geometry'
2025-08-04 16:56:49,422 - ERROR - 处理要素 28 失败: 'Series' object has no attribute 'geometry'
2025-08-04 16:56:49,422 - ERROR - 处理要素 29 失败: 'Series' object has no attribute 'geometry'
2025-08-04 16:56:49,422 - ERROR - 处理要素 30 失败: 'Series' object has no attribute 'geometry'
2025-08-04 16:56:49,422 - ERROR - 处理要素 31 失败: 'Series' object has no attribute 'geometry'
2025-08-04 16:56:49,422 - ERROR - 处理要素 32 失败: 'Series' object has no attribute 'geometry'
2025-08-04 16:56:49,422 - ERROR - 处理要素 33 失败: 'Series' object has no attribute 'geometry'
2025-08-04 16:56:49,422 - ERROR - 处理要素 34 失败: 'Series' object has no attribute 'geometry'
2025-08-04 16:56:49,422 - ERROR - 处理要素 35 失败: 'Series' object has no attribute 'geometry'
2025-08-04 16:56:49,422 - ERROR - 处理要素 36 失败: 'Series' object has no attribute 'geometry'
2025-08-04 16:56:49,422 - ERROR - 处理要素 37 失败: 'Series' object has no attribute 'geometry'
2025-08-04 16:56:49,422 - ERROR - 处理要素 38 失败: 'Series' object has no attribute 'geometry'
2025-08-04 16:56:49,422 - ERROR - 处理要素 39 失败: 'Series' object has no attribute 'geometry'
2025-08-04 16:56:49,422 - ERROR - 处理要素 40 失败: 'Series' object has no attribute 'geometry'
2025-08-04 16:56:49,422 - ERROR - 处理要素 41 失败: 'Series' object has no attribute 'geometry'
2025-08-04 16:56:49,422 - ERROR - 处理要素 42 失败: 'Series' object has no attribute 'geometry'
2025-08-04 16:56:49,422 - ERROR - 处理要素 43 失败: 'Series' object has no attribute 'geometry'
2025-08-04 16:56:49,422 - ERROR - 处理要素 44 失败: 'Series' object has no attribute 'geometry'
2025-08-04 16:56:49,422 - ERROR - 处理要素 45 失败: 'Series' object has no attribute 'geometry'
2025-08-04 16:56:49,422 - ERROR - 处理要素 46 失败: 'Series' object has no attribute 'geometry'
2025-08-04 16:56:49,422 - ERROR - 处理要素 47 失败: 'Series' object has no attribute 'geometry'
2025-08-04 16:56:49,422 - ERROR - 处理要素 48 失败: 'Series' object has no attribute 'geometry'
2025-08-04 16:56:49,423 - ERROR - 处理要素 49 失败: 'Series' object has no attribute 'geometry'
2025-08-04 16:56:49,423 - ERROR - 处理要素 50 失败: 'Series' object has no attribute 'geometry'
2025-08-04 16:56:49,423 - ERROR - 处理要素 51 失败: 'Series' object has no attribute 'geometry'
2025-08-04 16:56:49,423 - ERROR - 处理要素 52 失败: 'Series' object has no attribute 'geometry'
2025-08-04 16:56:49,423 - ERROR - 处理要素 53 失败: 'Series' object has no attribute 'geometry'
2025-08-04 16:56:49,423 - ERROR - 处理要素 54 失败: 'Series' object has no attribute 'geometry'
2025-08-04 16:56:49,423 - ERROR - 处理要素 55 失败: 'Series' object has no attribute 'geometry'
2025-08-04 16:56:49,423 - ERROR - 处理要素 56 失败: 'Series' object has no attribute 'geometry'
2025-08-04 16:56:49,423 - ERROR - 处理要素 57 失败: 'Series' object has no attribute 'geometry'
2025-08-04 16:56:49,423 - ERROR - 处理要素 58 失败: 'Series' object has no attribute 'geometry'
2025-08-04 16:56:49,423 - ERROR - 处理要素 59 失败: 'Series' object has no attribute 'geometry'
2025-08-04 16:56:49,423 - ERROR - 处理要素 60 失败: 'Series' object has no attribute 'geometry'
2025-08-04 16:56:49,423 - ERROR - 处理要素 61 失败: 'Series' object has no attribute 'geometry'
2025-08-04 16:56:49,423 - ERROR - 处理要素 62 失败: 'Series' object has no attribute 'geometry'
2025-08-04 16:56:49,423 - ERROR - 处理要素 63 失败: 'Series' object has no attribute 'geometry'
2025-08-04 16:56:49,423 - ERROR - 处理要素 64 失败: 'Series' object has no attribute 'geometry'
2025-08-04 16:56:49,423 - ERROR - 处理要素 65 失败: 'Series' object has no attribute 'geometry'
2025-08-04 16:56:49,423 - ERROR - 处理要素 66 失败: 'Series' object has no attribute 'geometry'
2025-08-04 16:56:49,423 - ERROR - 处理要素 67 失败: 'Series' object has no attribute 'geometry'
2025-08-04 16:56:49,423 - ERROR - 处理要素 68 失败: 'Series' object has no attribute 'geometry'
2025-08-04 16:56:49,423 - ERROR - 处理要素 69 失败: 'Series' object has no attribute 'geometry'
2025-08-04 16:56:49,423 - ERROR - 处理要素 70 失败: 'Series' object has no attribute 'geometry'
2025-08-04 16:56:49,423 - ERROR - 处理要素 71 失败: 'Series' object has no attribute 'geometry'
2025-08-04 16:56:49,423 - ERROR - 处理要素 72 失败: 'Series' object has no attribute 'geometry'
2025-08-04 16:56:49,423 - ERROR - 处理要素 73 失败: 'Series' object has no attribute 'geometry'
2025-08-04 16:56:49,423 - ERROR - 处理要素 74 失败: 'Series' object has no attribute 'geometry'
2025-08-04 16:56:49,423 - ERROR - 处理要素 75 失败: 'Series' object has no attribute 'geometry'
2025-08-04 16:56:49,423 - ERROR - 处理要素 76 失败: 'Series' object has no attribute 'geometry'
2025-08-04 16:56:49,423 - ERROR - 处理要素 77 失败: 'Series' object has no attribute 'geometry'
2025-08-04 16:56:49,423 - ERROR - 处理要素 78 失败: 'Series' object has no attribute 'geometry'
2025-08-04 16:56:49,423 - ERROR - 处理要素 79 失败: 'Series' object has no attribute 'geometry'
2025-08-04 16:56:49,423 - ERROR - 处理要素 80 失败: 'Series' object has no attribute 'geometry'
2025-08-04 16:56:49,423 - ERROR - 处理要素 81 失败: 'Series' object has no attribute 'geometry'
2025-08-04 16:56:49,423 - ERROR - 处理要素 82 失败: 'Series' object has no attribute 'geometry'
2025-08-04 16:56:49,423 - ERROR - 处理要素 83 失败: 'Series' object has no attribute 'geometry'
2025-08-04 16:56:49,423 - ERROR - 处理要素 84 失败: 'Series' object has no attribute 'geometry'
2025-08-04 16:56:49,423 - ERROR - 处理要素 85 失败: 'Series' object has no attribute 'geometry'
2025-08-04 16:56:49,423 - ERROR - 处理要素 86 失败: 'Series' object has no attribute 'geometry'
2025-08-04 16:56:49,424 - ERROR - 处理要素 87 失败: 'Series' object has no attribute 'geometry'
2025-08-04 16:56:49,424 - ERROR - 处理要素 88 失败: 'Series' object has no attribute 'geometry'
2025-08-04 16:56:49,424 - ERROR - 处理要素 89 失败: 'Series' object has no attribute 'geometry'
2025-08-04 16:56:49,424 - ERROR - 处理要素 90 失败: 'Series' object has no attribute 'geometry'
2025-08-04 16:56:49,424 - ERROR - 处理要素 91 失败: 'Series' object has no attribute 'geometry'
2025-08-04 16:56:49,424 - ERROR - 处理要素 92 失败: 'Series' object has no attribute 'geometry'
2025-08-04 16:56:49,424 - ERROR - 处理要素 93 失败: 'Series' object has no attribute 'geometry'
2025-08-04 16:56:49,424 - ERROR - 处理要素 94 失败: 'Series' object has no attribute 'geometry'
2025-08-04 16:56:49,424 - ERROR - 处理要素 95 失败: 'Series' object has no attribute 'geometry'
2025-08-04 16:56:49,424 - ERROR - 处理要素 96 失败: 'Series' object has no attribute 'geometry'
2025-08-04 16:56:49,424 - ERROR - 处理要素 97 失败: 'Series' object has no attribute 'geometry'
2025-08-04 16:56:49,424 - ERROR - 处理要素 98 失败: 'Series' object has no attribute 'geometry'
2025-08-04 16:56:49,424 - ERROR - 处理要素 99 失败: 'Series' object has no attribute 'geometry'
2025-08-04 16:56:49,424 - ERROR - 处理要素 100 失败: 'Series' object has no attribute 'geometry'
2025-08-04 16:56:49,424 - ERROR - 处理要素 101 失败: 'Series' object has no attribute 'geometry'
2025-08-04 16:56:49,424 - ERROR - 处理要素 102 失败: 'Series' object has no attribute 'geometry'
2025-08-04 16:56:49,424 - ERROR - 处理要素 103 失败: 'Series' object has no attribute 'geometry'
2025-08-04 16:56:49,424 - ERROR - 处理要素 104 失败: 'Series' object has no attribute 'geometry'
2025-08-04 16:56:49,424 - ERROR - 处理要素 105 失败: 'Series' object has no attribute 'geometry'
2025-08-04 16:56:49,424 - ERROR - 处理要素 106 失败: 'Series' object has no attribute 'geometry'
2025-08-04 16:56:49,424 - ERROR - 处理要素 107 失败: 'Series' object has no attribute 'geometry'
2025-08-04 16:56:49,424 - ERROR - 处理要素 108 失败: 'Series' object has no attribute 'geometry'
2025-08-04 16:56:49,424 - ERROR - 处理要素 109 失败: 'Series' object has no attribute 'geometry'
2025-08-04 16:56:49,424 - ERROR - 处理要素 110 失败: 'Series' object has no attribute 'geometry'
2025-08-04 16:56:49,424 - ERROR - 处理要素 111 失败: 'Series' object has no attribute 'geometry'
2025-08-04 16:56:49,424 - ERROR - 处理要素 112 失败: 'Series' object has no attribute 'geometry'
2025-08-04 16:56:49,424 - ERROR - 处理要素 113 失败: 'Series' object has no attribute 'geometry'
2025-08-04 16:56:49,424 - ERROR - 处理要素 114 失败: 'Series' object has no attribute 'geometry'
2025-08-04 16:56:49,424 - ERROR - 处理要素 115 失败: 'Series' object has no attribute 'geometry'
2025-08-04 16:56:49,424 - ERROR - 处理要素 116 失败: 'Series' object has no attribute 'geometry'
2025-08-04 16:56:49,425 - ERROR - 处理要素 117 失败: 'Series' object has no attribute 'geometry'
2025-08-04 16:56:49,425 - ERROR - 处理要素 118 失败: 'Series' object has no attribute 'geometry'
2025-08-04 16:56:49,425 - ERROR - 处理要素 119 失败: 'Series' object has no attribute 'geometry'
2025-08-04 16:56:49,425 - ERROR - 处理要素 120 失败: 'Series' object has no attribute 'geometry'
2025-08-04 16:56:49,425 - ERROR - 处理要素 121 失败: 'Series' object has no attribute 'geometry'
2025-08-04 16:56:49,425 - ERROR - 处理要素 122 失败: 'Series' object has no attribute 'geometry'
2025-08-04 16:56:49,425 - ERROR - 处理要素 123 失败: 'Series' object has no attribute 'geometry'
2025-08-04 16:56:49,425 - ERROR - 处理要素 124 失败: 'Series' object has no attribute 'geometry'
2025-08-04 16:56:49,425 - ERROR - 处理要素 125 失败: 'Series' object has no attribute 'geometry'
2025-08-04 16:56:49,425 - ERROR - 处理要素 126 失败: 'Series' object has no attribute 'geometry'
2025-08-04 16:56:49,425 - ERROR - 处理要素 127 失败: 'Series' object has no attribute 'geometry'
2025-08-04 16:56:49,425 - ERROR - 处理要素 128 失败: 'Series' object has no attribute 'geometry'
2025-08-04 16:56:49,425 - ERROR - 处理要素 129 失败: 'Series' object has no attribute 'geometry'
2025-08-04 16:56:49,425 - ERROR - 处理要素 130 失败: 'Series' object has no attribute 'geometry'
2025-08-04 16:56:49,425 - ERROR - 处理要素 131 失败: 'Series' object has no attribute 'geometry'
2025-08-04 16:56:49,425 - ERROR - 处理要素 132 失败: 'Series' object has no attribute 'geometry'
2025-08-04 16:56:49,425 - ERROR - 处理要素 133 失败: 'Series' object has no attribute 'geometry'
2025-08-04 16:56:49,425 - ERROR - 处理要素 134 失败: 'Series' object has no attribute 'geometry'
2025-08-04 16:56:49,425 - ERROR - 处理要素 135 失败: 'Series' object has no attribute 'geometry'
2025-08-04 16:56:49,425 - ERROR - 处理要素 136 失败: 'Series' object has no attribute 'geometry'
2025-08-04 16:56:49,425 - ERROR - 处理要素 137 失败: 'Series' object has no attribute 'geometry'
2025-08-04 16:56:49,425 - ERROR - 处理要素 138 失败: 'Series' object has no attribute 'geometry'
2025-08-04 16:56:49,425 - ERROR - 处理要素 139 失败: 'Series' object has no attribute 'geometry'
2025-08-04 16:56:49,426 - ERROR - 处理要素 140 失败: 'Series' object has no attribute 'geometry'
2025-08-04 16:56:49,426 - ERROR - 处理要素 141 失败: 'Series' object has no attribute 'geometry'
2025-08-04 16:56:49,426 - ERROR - 处理要素 142 失败: 'Series' object has no attribute 'geometry'
2025-08-04 16:56:49,426 - ERROR - 处理要素 143 失败: 'Series' object has no attribute 'geometry'
2025-08-04 16:56:49,426 - ERROR - 处理要素 144 失败: 'Series' object has no attribute 'geometry'
2025-08-04 16:56:49,426 - ERROR - 处理要素 145 失败: 'Series' object has no attribute 'geometry'
2025-08-04 16:56:49,426 - ERROR - 处理要素 146 失败: 'Series' object has no attribute 'geometry'
2025-08-04 16:56:49,426 - ERROR - 处理要素 147 失败: 'Series' object has no attribute 'geometry'
2025-08-04 16:56:49,426 - ERROR - 处理要素 148 失败: 'Series' object has no attribute 'geometry'
2025-08-04 16:56:49,426 - ERROR - 处理要素 149 失败: 'Series' object has no attribute 'geometry'
2025-08-04 16:56:49,426 - ERROR - 处理要素 150 失败: 'Series' object has no attribute 'geometry'
2025-08-04 16:56:49,426 - ERROR - 处理要素 151 失败: 'Series' object has no attribute 'geometry'
2025-08-04 16:56:49,427 - ERROR - 处理要素 152 失败: 'Series' object has no attribute 'geometry'
2025-08-04 16:56:49,427 - ERROR - 处理要素 153 失败: 'Series' object has no attribute 'geometry'
2025-08-04 16:56:49,427 - ERROR - 处理要素 154 失败: 'Series' object has no attribute 'geometry'
2025-08-04 16:56:49,427 - ERROR - 处理要素 155 失败: 'Series' object has no attribute 'geometry'
2025-08-04 16:56:49,427 - ERROR - 处理要素 156 失败: 'Series' object has no attribute 'geometry'
2025-08-04 16:56:49,427 - ERROR - 处理要素 157 失败: 'Series' object has no attribute 'geometry'
2025-08-04 16:56:49,427 - ERROR - 处理要素 158 失败: 'Series' object has no attribute 'geometry'
2025-08-04 16:56:49,427 - ERROR - 处理要素 159 失败: 'Series' object has no attribute 'geometry'
2025-08-04 16:56:49,427 - ERROR - 处理要素 160 失败: 'Series' object has no attribute 'geometry'
2025-08-04 16:56:49,427 - ERROR - 处理要素 161 失败: 'Series' object has no attribute 'geometry'
2025-08-04 16:56:49,427 - ERROR - 处理要素 162 失败: 'Series' object has no attribute 'geometry'
2025-08-04 16:56:49,427 - ERROR - 处理要素 163 失败: 'Series' object has no attribute 'geometry'
2025-08-04 16:56:49,427 - ERROR - 处理要素 164 失败: 'Series' object has no attribute 'geometry'
2025-08-04 16:56:49,427 - ERROR - 处理要素 165 失败: 'Series' object has no attribute 'geometry'
2025-08-04 16:56:49,427 - ERROR - 处理要素 166 失败: 'Series' object has no attribute 'geometry'
2025-08-04 16:56:49,427 - ERROR - 处理要素 167 失败: 'Series' object has no attribute 'geometry'
2025-08-04 16:56:49,427 - ERROR - 处理要素 168 失败: 'Series' object has no attribute 'geometry'
2025-08-04 16:56:49,427 - ERROR - 处理要素 169 失败: 'Series' object has no attribute 'geometry'
2025-08-04 16:56:49,427 - ERROR - 处理要素 170 失败: 'Series' object has no attribute 'geometry'
2025-08-04 16:56:49,427 - ERROR - 处理要素 171 失败: 'Series' object has no attribute 'geometry'
2025-08-04 16:56:49,427 - ERROR - 处理要素 172 失败: 'Series' object has no attribute 'geometry'
2025-08-04 16:56:49,427 - ERROR - 处理要素 173 失败: 'Series' object has no attribute 'geometry'
2025-08-04 16:56:49,427 - ERROR - 处理要素 174 失败: 'Series' object has no attribute 'geometry'
2025-08-04 16:56:49,427 - ERROR - 处理要素 175 失败: 'Series' object has no attribute 'geometry'
2025-08-04 16:56:49,427 - ERROR - 处理要素 176 失败: 'Series' object has no attribute 'geometry'
2025-08-04 16:56:49,428 - ERROR - 处理要素 177 失败: 'Series' object has no attribute 'geometry'
2025-08-04 16:56:49,428 - ERROR - 处理要素 178 失败: 'Series' object has no attribute 'geometry'
2025-08-04 16:56:49,428 - ERROR - 处理要素 179 失败: 'Series' object has no attribute 'geometry'
2025-08-04 16:56:49,428 - ERROR - 处理要素 180 失败: 'Series' object has no attribute 'geometry'
2025-08-04 16:56:49,428 - ERROR - 处理要素 181 失败: 'Series' object has no attribute 'geometry'
2025-08-04 16:56:49,428 - ERROR - 处理要素 182 失败: 'Series' object has no attribute 'geometry'
2025-08-04 16:56:49,428 - ERROR - 处理要素 183 失败: 'Series' object has no attribute 'geometry'
2025-08-04 16:56:49,428 - ERROR - 处理要素 184 失败: 'Series' object has no attribute 'geometry'
2025-08-04 16:56:49,429 - ERROR - 处理要素 185 失败: 'Series' object has no attribute 'geometry'
2025-08-04 16:56:49,429 - ERROR - 处理要素 186 失败: 'Series' object has no attribute 'geometry'
2025-08-04 16:56:49,429 - ERROR - 处理要素 187 失败: 'Series' object has no attribute 'geometry'
2025-08-04 16:56:49,429 - ERROR - 处理要素 188 失败: 'Series' object has no attribute 'geometry'
2025-08-04 16:56:49,429 - ERROR - 处理要素 189 失败: 'Series' object has no attribute 'geometry'
2025-08-04 16:56:49,429 - ERROR - 处理要素 190 失败: 'Series' object has no attribute 'geometry'
2025-08-04 16:56:49,429 - ERROR - 处理要素 191 失败: 'Series' object has no attribute 'geometry'
2025-08-04 16:56:49,429 - ERROR - 处理要素 192 失败: 'Series' object has no attribute 'geometry'
2025-08-04 16:56:49,429 - ERROR - 处理要素 193 失败: 'Series' object has no attribute 'geometry'
2025-08-04 16:56:49,429 - ERROR - 处理要素 194 失败: 'Series' object has no attribute 'geometry'
2025-08-04 16:56:49,429 - ERROR - 处理要素 195 失败: 'Series' object has no attribute 'geometry'
2025-08-04 16:56:49,429 - ERROR - 处理要素 196 失败: 'Series' object has no attribute 'geometry'
2025-08-04 16:56:49,429 - ERROR - 处理要素 197 失败: 'Series' object has no attribute 'geometry'
2025-08-04 16:56:49,429 - ERROR - 处理要素 198 失败: 'Series' object has no attribute 'geometry'
2025-08-04 16:56:49,429 - ERROR - 处理要素 199 失败: 'Series' object has no attribute 'geometry'
2025-08-04 16:56:49,430 - ERROR - 处理要素 200 失败: 'Series' object has no attribute 'geometry'
2025-08-04 16:56:49,430 - ERROR - 处理要素 201 失败: 'Series' object has no attribute 'geometry'
2025-08-04 16:56:49,430 - ERROR - 处理要素 202 失败: 'Series' object has no attribute 'geometry'
2025-08-04 16:56:49,430 - ERROR - 处理要素 203 失败: 'Series' object has no attribute 'geometry'
2025-08-04 16:56:49,430 - ERROR - 处理要素 204 失败: 'Series' object has no attribute 'geometry'
2025-08-04 16:56:49,430 - ERROR - 处理要素 205 失败: 'Series' object has no attribute 'geometry'
2025-08-04 16:56:49,430 - ERROR - 处理要素 206 失败: 'Series' object has no attribute 'geometry'
2025-08-04 16:56:49,430 - ERROR - 处理要素 207 失败: 'Series' object has no attribute 'geometry'
2025-08-04 16:56:49,430 - ERROR - 处理要素 208 失败: 'Series' object has no attribute 'geometry'
2025-08-04 16:56:49,430 - ERROR - 处理要素 209 失败: 'Series' object has no attribute 'geometry'
2025-08-04 16:56:49,430 - ERROR - 处理要素 210 失败: 'Series' object has no attribute 'geometry'
2025-08-04 16:56:49,430 - ERROR - 处理要素 211 失败: 'Series' object has no attribute 'geometry'
2025-08-04 16:56:49,431 - ERROR - 处理要素 212 失败: 'Series' object has no attribute 'geometry'
2025-08-04 16:56:49,431 - ERROR - 处理要素 213 失败: 'Series' object has no attribute 'geometry'
2025-08-04 16:56:49,431 - ERROR - 处理要素 214 失败: 'Series' object has no attribute 'geometry'
2025-08-04 16:56:49,431 - ERROR - 处理要素 215 失败: 'Series' object has no attribute 'geometry'
2025-08-04 16:56:49,431 - ERROR - 处理要素 216 失败: 'Series' object has no attribute 'geometry'
2025-08-04 16:56:49,431 - ERROR - 处理要素 217 失败: 'Series' object has no attribute 'geometry'
2025-08-04 16:56:49,431 - ERROR - 处理要素 218 失败: 'Series' object has no attribute 'geometry'
2025-08-04 16:56:49,431 - ERROR - 处理要素 219 失败: 'Series' object has no attribute 'geometry'
2025-08-04 16:56:49,431 - ERROR - 处理要素 220 失败: 'Series' object has no attribute 'geometry'
2025-08-04 16:56:49,431 - ERROR - 处理要素 221 失败: 'Series' object has no attribute 'geometry'
2025-08-04 16:56:49,431 - ERROR - 处理要素 222 失败: 'Series' object has no attribute 'geometry'
2025-08-04 16:56:49,431 - ERROR - 处理要素 223 失败: 'Series' object has no attribute 'geometry'
2025-08-04 16:56:49,431 - ERROR - 处理要素 224 失败: 'Series' object has no attribute 'geometry'
2025-08-04 16:56:49,431 - ERROR - 处理要素 225 失败: 'Series' object has no attribute 'geometry'
2025-08-04 16:56:49,431 - ERROR - 处理要素 226 失败: 'Series' object has no attribute 'geometry'
2025-08-04 16:56:49,431 - ERROR - 处理要素 227 失败: 'Series' object has no attribute 'geometry'
2025-08-04 16:56:49,431 - ERROR - 处理要素 228 失败: 'Series' object has no attribute 'geometry'
2025-08-04 16:56:49,431 - ERROR - 处理要素 229 失败: 'Series' object has no attribute 'geometry'
2025-08-04 16:56:49,431 - ERROR - 处理要素 230 失败: 'Series' object has no attribute 'geometry'
2025-08-04 16:56:49,432 - ERROR - 处理要素 231 失败: 'Series' object has no attribute 'geometry'
2025-08-04 16:56:49,432 - ERROR - 处理要素 232 失败: 'Series' object has no attribute 'geometry'
2025-08-04 16:56:49,432 - ERROR - 处理要素 233 失败: 'Series' object has no attribute 'geometry'
2025-08-04 16:56:49,432 - ERROR - 处理要素 234 失败: 'Series' object has no attribute 'geometry'
2025-08-04 16:56:49,432 - ERROR - 处理要素 235 失败: 'Series' object has no attribute 'geometry'
2025-08-04 16:56:49,432 - ERROR - 处理要素 236 失败: 'Series' object has no attribute 'geometry'
2025-08-04 16:56:49,432 - ERROR - 处理要素 237 失败: 'Series' object has no attribute 'geometry'
2025-08-04 16:56:49,432 - ERROR - 处理要素 238 失败: 'Series' object has no attribute 'geometry'
2025-08-04 16:56:49,432 - ERROR - 处理要素 239 失败: 'Series' object has no attribute 'geometry'
2025-08-04 16:56:49,432 - ERROR - 处理要素 240 失败: 'Series' object has no attribute 'geometry'
2025-08-04 16:56:49,432 - ERROR - 处理要素 241 失败: 'Series' object has no attribute 'geometry'
2025-08-04 16:56:49,432 - ERROR - 处理要素 242 失败: 'Series' object has no attribute 'geometry'
2025-08-04 16:56:49,432 - ERROR - 处理要素 243 失败: 'Series' object has no attribute 'geometry'
2025-08-04 16:56:49,432 - ERROR - 处理要素 244 失败: 'Series' object has no attribute 'geometry'
2025-08-04 16:56:49,432 - ERROR - 处理要素 245 失败: 'Series' object has no attribute 'geometry'
2025-08-04 16:56:49,432 - ERROR - 处理要素 246 失败: 'Series' object has no attribute 'geometry'
2025-08-04 16:56:49,432 - ERROR - 处理要素 247 失败: 'Series' object has no attribute 'geometry'
2025-08-04 16:56:49,432 - ERROR - 处理要素 248 失败: 'Series' object has no attribute 'geometry'
2025-08-04 16:56:49,432 - ERROR - 处理要素 249 失败: 'Series' object has no attribute 'geometry'
2025-08-04 16:56:49,433 - ERROR - 处理要素 250 失败: 'Series' object has no attribute 'geometry'
2025-08-04 16:56:49,433 - ERROR - 处理要素 251 失败: 'Series' object has no attribute 'geometry'
2025-08-04 16:56:49,433 - ERROR - 处理要素 252 失败: 'Series' object has no attribute 'geometry'
2025-08-04 16:56:49,433 - ERROR - 处理要素 253 失败: 'Series' object has no attribute 'geometry'
2025-08-04 16:56:49,433 - ERROR - 处理要素 254 失败: 'Series' object has no attribute 'geometry'
2025-08-04 16:56:49,433 - ERROR - 处理要素 255 失败: 'Series' object has no attribute 'geometry'
2025-08-04 16:56:49,433 - ERROR - 处理要素 256 失败: 'Series' object has no attribute 'geometry'
2025-08-04 16:56:49,433 - ERROR - 处理要素 257 失败: 'Series' object has no attribute 'geometry'
2025-08-04 16:56:49,433 - ERROR - 处理要素 258 失败: 'Series' object has no attribute 'geometry'
2025-08-04 16:56:49,433 - ERROR - 处理要素 259 失败: 'Series' object has no attribute 'geometry'
2025-08-04 16:56:49,433 - ERROR - 处理要素 260 失败: 'Series' object has no attribute 'geometry'
2025-08-04 16:56:49,433 - ERROR - 处理要素 261 失败: 'Series' object has no attribute 'geometry'
2025-08-04 16:56:49,433 - ERROR - 处理要素 262 失败: 'Series' object has no attribute 'geometry'
2025-08-04 16:56:49,433 - ERROR - 处理要素 263 失败: 'Series' object has no attribute 'geometry'
2025-08-04 16:56:49,433 - ERROR - 处理要素 264 失败: 'Series' object has no attribute 'geometry'
2025-08-04 16:56:49,434 - ERROR - 处理要素 265 失败: 'Series' object has no attribute 'geometry'
2025-08-04 16:56:49,434 - ERROR - 处理要素 266 失败: 'Series' object has no attribute 'geometry'
2025-08-04 16:56:49,434 - ERROR - 处理要素 267 失败: 'Series' object has no attribute 'geometry'
2025-08-04 16:56:49,434 - ERROR - 处理要素 268 失败: 'Series' object has no attribute 'geometry'
2025-08-04 16:56:49,434 - ERROR - 处理要素 269 失败: 'Series' object has no attribute 'geometry'
2025-08-04 16:56:49,434 - ERROR - 处理要素 270 失败: 'Series' object has no attribute 'geometry'
2025-08-04 16:56:49,434 - ERROR - 处理要素 271 失败: 'Series' object has no attribute 'geometry'
2025-08-04 16:56:49,434 - ERROR - 处理要素 272 失败: 'Series' object has no attribute 'geometry'
2025-08-04 16:56:49,434 - ERROR - 处理要素 273 失败: 'Series' object has no attribute 'geometry'
2025-08-04 16:56:49,434 - ERROR - 处理要素 274 失败: 'Series' object has no attribute 'geometry'
2025-08-04 16:56:49,434 - ERROR - 处理要素 275 失败: 'Series' object has no attribute 'geometry'
2025-08-04 16:56:49,434 - ERROR - 处理要素 276 失败: 'Series' object has no attribute 'geometry'
2025-08-04 16:56:49,434 - ERROR - 处理要素 277 失败: 'Series' object has no attribute 'geometry'
2025-08-04 16:56:49,434 - ERROR - 处理要素 278 失败: 'Series' object has no attribute 'geometry'
2025-08-04 16:56:49,434 - ERROR - 处理要素 279 失败: 'Series' object has no attribute 'geometry'
2025-08-04 16:56:49,434 - ERROR - 处理要素 280 失败: 'Series' object has no attribute 'geometry'
2025-08-04 16:56:49,434 - ERROR - 处理要素 281 失败: 'Series' object has no attribute 'geometry'
2025-08-04 16:56:49,434 - ERROR - 处理要素 282 失败: 'Series' object has no attribute 'geometry'
2025-08-04 16:56:49,434 - ERROR - 处理要素 283 失败: 'Series' object has no attribute 'geometry'
2025-08-04 16:56:49,434 - ERROR - 处理要素 284 失败: 'Series' object has no attribute 'geometry'
2025-08-04 16:56:49,434 - ERROR - 处理要素 285 失败: 'Series' object has no attribute 'geometry'
2025-08-04 16:56:49,435 - ERROR - 处理要素 286 失败: 'Series' object has no attribute 'geometry'
2025-08-04 16:56:49,435 - ERROR - 处理要素 287 失败: 'Series' object has no attribute 'geometry'
2025-08-04 16:56:49,435 - ERROR - 处理要素 288 失败: 'Series' object has no attribute 'geometry'
2025-08-04 16:56:49,435 - ERROR - 处理要素 289 失败: 'Series' object has no attribute 'geometry'
2025-08-04 16:56:49,435 - ERROR - 处理要素 290 失败: 'Series' object has no attribute 'geometry'
2025-08-04 16:56:49,435 - ERROR - 处理要素 291 失败: 'Series' object has no attribute 'geometry'
2025-08-04 16:56:49,435 - ERROR - 处理要素 292 失败: 'Series' object has no attribute 'geometry'
2025-08-04 16:56:49,435 - ERROR - 处理要素 293 失败: 'Series' object has no attribute 'geometry'
2025-08-04 16:56:49,435 - ERROR - 处理要素 294 失败: 'Series' object has no attribute 'geometry'
2025-08-04 16:56:49,435 - ERROR - 处理要素 295 失败: 'Series' object has no attribute 'geometry'
2025-08-04 16:56:49,435 - ERROR - 处理要素 296 失败: 'Series' object has no attribute 'geometry'
2025-08-04 16:56:49,435 - ERROR - 处理要素 297 失败: 'Series' object has no attribute 'geometry'
2025-08-04 16:56:49,435 - ERROR - 处理要素 298 失败: 'Series' object has no attribute 'geometry'
2025-08-04 16:56:49,436 - ERROR - 处理要素 299 失败: 'Series' object has no attribute 'geometry'
2025-08-04 16:56:49,436 - ERROR - 处理要素 300 失败: 'Series' object has no attribute 'geometry'
2025-08-04 16:56:49,436 - ERROR - 处理要素 301 失败: 'Series' object has no attribute 'geometry'
2025-08-04 16:56:49,436 - ERROR - 处理要素 302 失败: 'Series' object has no attribute 'geometry'
2025-08-04 16:56:49,436 - ERROR - 处理要素 303 失败: 'Series' object has no attribute 'geometry'
2025-08-04 16:56:49,436 - ERROR - 处理要素 304 失败: 'Series' object has no attribute 'geometry'
2025-08-04 16:56:49,436 - ERROR - 处理要素 305 失败: 'Series' object has no attribute 'geometry'
2025-08-04 16:56:49,436 - ERROR - 处理要素 306 失败: 'Series' object has no attribute 'geometry'
2025-08-04 16:56:49,436 - ERROR - 处理要素 307 失败: 'Series' object has no attribute 'geometry'
2025-08-04 16:56:49,436 - ERROR - 处理要素 308 失败: 'Series' object has no attribute 'geometry'
2025-08-04 16:56:49,436 - ERROR - 处理要素 309 失败: 'Series' object has no attribute 'geometry'
2025-08-04 16:56:49,436 - ERROR - 处理要素 310 失败: 'Series' object has no attribute 'geometry'
2025-08-04 16:56:49,437 - ERROR - 处理要素 311 失败: 'Series' object has no attribute 'geometry'
2025-08-04 16:56:49,437 - ERROR - 处理要素 312 失败: 'Series' object has no attribute 'geometry'
2025-08-04 16:56:49,437 - ERROR - 处理要素 313 失败: 'Series' object has no attribute 'geometry'
2025-08-04 16:56:49,437 - ERROR - 处理要素 314 失败: 'Series' object has no attribute 'geometry'
2025-08-04 16:56:49,437 - ERROR - 处理要素 315 失败: 'Series' object has no attribute 'geometry'
2025-08-04 16:56:49,437 - ERROR - 处理要素 316 失败: 'Series' object has no attribute 'geometry'
2025-08-04 16:56:49,438 - ERROR - 处理要素 317 失败: 'Series' object has no attribute 'geometry'
2025-08-04 16:56:49,438 - ERROR - 处理要素 318 失败: 'Series' object has no attribute 'geometry'
2025-08-04 16:56:49,438 - ERROR - 处理要素 319 失败: 'Series' object has no attribute 'geometry'
2025-08-04 16:56:49,438 - ERROR - 处理要素 320 失败: 'Series' object has no attribute 'geometry'
2025-08-04 16:56:49,438 - ERROR - 处理要素 321 失败: 'Series' object has no attribute 'geometry'
2025-08-04 16:56:49,438 - ERROR - 处理要素 322 失败: 'Series' object has no attribute 'geometry'
2025-08-04 16:56:49,438 - ERROR - 处理要素 323 失败: 'Series' object has no attribute 'geometry'
2025-08-04 16:56:49,438 - ERROR - 处理要素 324 失败: 'Series' object has no attribute 'geometry'
2025-08-04 16:56:49,438 - ERROR - 处理要素 325 失败: 'Series' object has no attribute 'geometry'
2025-08-04 16:56:49,438 - ERROR - 处理要素 326 失败: 'Series' object has no attribute 'geometry'
2025-08-04 16:56:49,438 - ERROR - 处理要素 327 失败: 'Series' object has no attribute 'geometry'
2025-08-04 16:56:49,438 - ERROR - 处理要素 328 失败: 'Series' object has no attribute 'geometry'
2025-08-04 16:56:49,438 - ERROR - 处理要素 329 失败: 'Series' object has no attribute 'geometry'
2025-08-04 16:56:49,438 - ERROR - 处理要素 330 失败: 'Series' object has no attribute 'geometry'
2025-08-04 16:56:49,438 - ERROR - 处理要素 331 失败: 'Series' object has no attribute 'geometry'
2025-08-04 16:56:49,438 - ERROR - 处理要素 332 失败: 'Series' object has no attribute 'geometry'
2025-08-04 16:56:49,438 - ERROR - 处理要素 333 失败: 'Series' object has no attribute 'geometry'
2025-08-04 16:56:49,438 - ERROR - 处理要素 334 失败: 'Series' object has no attribute 'geometry'
2025-08-04 16:56:49,438 - ERROR - 处理要素 335 失败: 'Series' object has no attribute 'geometry'
2025-08-04 16:56:49,438 - ERROR - 处理要素 336 失败: 'Series' object has no attribute 'geometry'
2025-08-04 16:56:49,438 - ERROR - 处理要素 337 失败: 'Series' object has no attribute 'geometry'
2025-08-04 16:56:49,438 - ERROR - 处理要素 338 失败: 'Series' object has no attribute 'geometry'
2025-08-04 16:56:49,438 - ERROR - 处理要素 339 失败: 'Series' object has no attribute 'geometry'
2025-08-04 16:56:49,438 - ERROR - 处理要素 340 失败: 'Series' object has no attribute 'geometry'
2025-08-04 16:56:49,438 - ERROR - 处理要素 341 失败: 'Series' object has no attribute 'geometry'
2025-08-04 16:56:49,438 - ERROR - 处理要素 342 失败: 'Series' object has no attribute 'geometry'
2025-08-04 16:56:49,438 - ERROR - 处理要素 343 失败: 'Series' object has no attribute 'geometry'
2025-08-04 16:56:49,438 - ERROR - 处理要素 344 失败: 'Series' object has no attribute 'geometry'
2025-08-04 16:56:49,438 - ERROR - 处理要素 345 失败: 'Series' object has no attribute 'geometry'
2025-08-04 16:56:49,438 - ERROR - 处理要素 346 失败: 'Series' object has no attribute 'geometry'
2025-08-04 16:56:49,438 - ERROR - 处理要素 347 失败: 'Series' object has no attribute 'geometry'
2025-08-04 16:56:49,438 - ERROR - 处理要素 348 失败: 'Series' object has no attribute 'geometry'
2025-08-04 16:56:49,439 - ERROR - 处理要素 349 失败: 'Series' object has no attribute 'geometry'
2025-08-04 16:56:49,439 - ERROR - 处理要素 350 失败: 'Series' object has no attribute 'geometry'
2025-08-04 16:56:49,439 - ERROR - 处理要素 351 失败: 'Series' object has no attribute 'geometry'
2025-08-04 16:56:49,439 - ERROR - 处理要素 352 失败: 'Series' object has no attribute 'geometry'
2025-08-04 16:56:49,439 - ERROR - 处理要素 353 失败: 'Series' object has no attribute 'geometry'
2025-08-04 16:56:49,439 - ERROR - 处理要素 354 失败: 'Series' object has no attribute 'geometry'
2025-08-04 16:56:49,439 - ERROR - 处理要素 355 失败: 'Series' object has no attribute 'geometry'
2025-08-04 16:56:49,439 - ERROR - 处理要素 356 失败: 'Series' object has no attribute 'geometry'
2025-08-04 16:56:49,439 - ERROR - 处理要素 357 失败: 'Series' object has no attribute 'geometry'
2025-08-04 16:56:49,439 - ERROR - 处理要素 358 失败: 'Series' object has no attribute 'geometry'
2025-08-04 16:56:49,439 - ERROR - 处理要素 359 失败: 'Series' object has no attribute 'geometry'
2025-08-04 16:56:49,439 - ERROR - 处理要素 360 失败: 'Series' object has no attribute 'geometry'
2025-08-04 16:56:49,439 - ERROR - 处理要素 361 失败: 'Series' object has no attribute 'geometry'
2025-08-04 16:56:49,439 - ERROR - 处理要素 362 失败: 'Series' object has no attribute 'geometry'
2025-08-04 16:56:49,440 - ERROR - 处理要素 363 失败: 'Series' object has no attribute 'geometry'
2025-08-04 16:56:49,440 - ERROR - 处理要素 364 失败: 'Series' object has no attribute 'geometry'
2025-08-04 16:56:49,440 - ERROR - 处理要素 365 失败: 'Series' object has no attribute 'geometry'
2025-08-04 16:56:49,440 - ERROR - 处理要素 366 失败: 'Series' object has no attribute 'geometry'
2025-08-04 16:56:49,440 - ERROR - 处理要素 367 失败: 'Series' object has no attribute 'geometry'
2025-08-04 16:56:49,440 - ERROR - 处理要素 368 失败: 'Series' object has no attribute 'geometry'
2025-08-04 16:56:49,441 - ERROR - 处理要素 369 失败: 'Series' object has no attribute 'geometry'
2025-08-04 16:56:49,441 - ERROR - 处理要素 370 失败: 'Series' object has no attribute 'geometry'
2025-08-04 16:56:49,441 - ERROR - 处理要素 371 失败: 'Series' object has no attribute 'geometry'
2025-08-04 16:56:49,441 - ERROR - 处理要素 372 失败: 'Series' object has no attribute 'geometry'
2025-08-04 16:56:49,441 - ERROR - 处理要素 373 失败: 'Series' object has no attribute 'geometry'
2025-08-04 16:56:49,441 - ERROR - 处理要素 374 失败: 'Series' object has no attribute 'geometry'
2025-08-04 16:56:49,441 - ERROR - 处理要素 375 失败: 'Series' object has no attribute 'geometry'
2025-08-04 16:56:49,441 - ERROR - 处理要素 376 失败: 'Series' object has no attribute 'geometry'
2025-08-04 16:56:49,441 - ERROR - 处理要素 377 失败: 'Series' object has no attribute 'geometry'
2025-08-04 16:56:49,441 - ERROR - 处理要素 378 失败: 'Series' object has no attribute 'geometry'
2025-08-04 16:56:49,441 - ERROR - 处理要素 379 失败: 'Series' object has no attribute 'geometry'
2025-08-04 16:56:49,441 - ERROR - 处理要素 380 失败: 'Series' object has no attribute 'geometry'
2025-08-04 16:56:49,441 - ERROR - 处理要素 381 失败: 'Series' object has no attribute 'geometry'
2025-08-04 16:56:49,441 - ERROR - 处理要素 382 失败: 'Series' object has no attribute 'geometry'
2025-08-04 16:56:49,441 - ERROR - 处理要素 383 失败: 'Series' object has no attribute 'geometry'
2025-08-04 16:56:49,441 - ERROR - 处理要素 384 失败: 'Series' object has no attribute 'geometry'
2025-08-04 16:56:49,441 - ERROR - 处理要素 385 失败: 'Series' object has no attribute 'geometry'
2025-08-04 16:56:49,441 - ERROR - 处理要素 386 失败: 'Series' object has no attribute 'geometry'
2025-08-04 16:56:49,441 - ERROR - 处理要素 387 失败: 'Series' object has no attribute 'geometry'
2025-08-04 16:56:49,441 - ERROR - 处理要素 388 失败: 'Series' object has no attribute 'geometry'
2025-08-04 16:56:49,441 - ERROR - 处理要素 389 失败: 'Series' object has no attribute 'geometry'
2025-08-04 16:56:49,441 - ERROR - 处理要素 390 失败: 'Series' object has no attribute 'geometry'
2025-08-04 16:56:49,441 - ERROR - 处理要素 391 失败: 'Series' object has no attribute 'geometry'
2025-08-04 16:56:49,441 - ERROR - 处理要素 392 失败: 'Series' object has no attribute 'geometry'
2025-08-04 16:56:49,441 - ERROR - 处理要素 393 失败: 'Series' object has no attribute 'geometry'
2025-08-04 16:56:49,441 - ERROR - 处理要素 394 失败: 'Series' object has no attribute 'geometry'
2025-08-04 16:56:49,441 - ERROR - 处理要素 395 失败: 'Series' object has no attribute 'geometry'
2025-08-04 16:56:49,441 - ERROR - 处理要素 396 失败: 'Series' object has no attribute 'geometry'
2025-08-04 16:56:49,441 - ERROR - 处理要素 397 失败: 'Series' object has no attribute 'geometry'
2025-08-04 16:56:49,441 - ERROR - 处理要素 398 失败: 'Series' object has no attribute 'geometry'
2025-08-04 16:56:49,441 - ERROR - 处理要素 399 失败: 'Series' object has no attribute 'geometry'
2025-08-04 16:56:49,441 - ERROR - 处理要素 400 失败: 'Series' object has no attribute 'geometry'
2025-08-04 16:56:49,441 - ERROR - 处理要素 401 失败: 'Series' object has no attribute 'geometry'
2025-08-04 16:56:49,441 - ERROR - 处理要素 402 失败: 'Series' object has no attribute 'geometry'
2025-08-04 16:56:49,441 - ERROR - 处理要素 403 失败: 'Series' object has no attribute 'geometry'
2025-08-04 16:56:49,442 - ERROR - 处理要素 404 失败: 'Series' object has no attribute 'geometry'
2025-08-04 16:56:49,442 - ERROR - 处理要素 405 失败: 'Series' object has no attribute 'geometry'
2025-08-04 16:56:49,442 - ERROR - 处理要素 406 失败: 'Series' object has no attribute 'geometry'
2025-08-04 16:56:49,442 - ERROR - 处理要素 407 失败: 'Series' object has no attribute 'geometry'
2025-08-04 16:56:49,442 - ERROR - 处理要素 408 失败: 'Series' object has no attribute 'geometry'
2025-08-04 16:56:49,442 - ERROR - 处理要素 409 失败: 'Series' object has no attribute 'geometry'
2025-08-04 16:56:49,442 - ERROR - 处理要素 410 失败: 'Series' object has no attribute 'geometry'
2025-08-04 16:56:49,442 - ERROR - 处理要素 411 失败: 'Series' object has no attribute 'geometry'
2025-08-04 16:56:49,442 - ERROR - 处理要素 412 失败: 'Series' object has no attribute 'geometry'
2025-08-04 16:56:49,442 - ERROR - 处理要素 413 失败: 'Series' object has no attribute 'geometry'
2025-08-04 16:56:49,442 - ERROR - 处理要素 414 失败: 'Series' object has no attribute 'geometry'
2025-08-04 16:56:49,442 - ERROR - 处理要素 415 失败: 'Series' object has no attribute 'geometry'
2025-08-04 16:56:49,442 - ERROR - 处理要素 416 失败: 'Series' object has no attribute 'geometry'
2025-08-04 16:56:49,442 - ERROR - 处理要素 417 失败: 'Series' object has no attribute 'geometry'
2025-08-04 16:56:49,442 - ERROR - 处理要素 418 失败: 'Series' object has no attribute 'geometry'
2025-08-04 16:56:49,442 - ERROR - 处理要素 419 失败: 'Series' object has no attribute 'geometry'
2025-08-04 16:56:49,442 - ERROR - 处理要素 420 失败: 'Series' object has no attribute 'geometry'
2025-08-04 16:56:49,442 - ERROR - 处理要素 421 失败: 'Series' object has no attribute 'geometry'
2025-08-04 16:56:49,442 - ERROR - 处理要素 422 失败: 'Series' object has no attribute 'geometry'
2025-08-04 16:56:49,442 - ERROR - 处理要素 423 失败: 'Series' object has no attribute 'geometry'
2025-08-04 16:56:49,442 - ERROR - 处理要素 424 失败: 'Series' object has no attribute 'geometry'
2025-08-04 16:56:49,442 - ERROR - 处理要素 425 失败: 'Series' object has no attribute 'geometry'
2025-08-04 16:56:49,442 - ERROR - 处理要素 426 失败: 'Series' object has no attribute 'geometry'
2025-08-04 16:56:49,442 - ERROR - 处理要素 427 失败: 'Series' object has no attribute 'geometry'
2025-08-04 16:56:49,442 - ERROR - 处理要素 428 失败: 'Series' object has no attribute 'geometry'
2025-08-04 16:56:49,442 - ERROR - 处理要素 429 失败: 'Series' object has no attribute 'geometry'
2025-08-04 16:56:49,442 - ERROR - 处理要素 430 失败: 'Series' object has no attribute 'geometry'
2025-08-04 16:56:49,442 - ERROR - 处理要素 431 失败: 'Series' object has no attribute 'geometry'
2025-08-04 16:56:49,442 - ERROR - 处理要素 432 失败: 'Series' object has no attribute 'geometry'
2025-08-04 16:56:49,442 - ERROR - 处理要素 433 失败: 'Series' object has no attribute 'geometry'
2025-08-04 16:56:49,442 - ERROR - 处理要素 434 失败: 'Series' object has no attribute 'geometry'
2025-08-04 16:56:49,442 - ERROR - 处理要素 435 失败: 'Series' object has no attribute 'geometry'
2025-08-04 16:56:49,442 - ERROR - 处理要素 436 失败: 'Series' object has no attribute 'geometry'
2025-08-04 16:56:49,442 - ERROR - 处理要素 437 失败: 'Series' object has no attribute 'geometry'
2025-08-04 16:56:49,442 - ERROR - 处理要素 438 失败: 'Series' object has no attribute 'geometry'
2025-08-04 16:56:49,442 - ERROR - 处理要素 439 失败: 'Series' object has no attribute 'geometry'
2025-08-04 16:56:49,442 - ERROR - 处理要素 440 失败: 'Series' object has no attribute 'geometry'
2025-08-04 16:56:49,443 - ERROR - 处理要素 441 失败: 'Series' object has no attribute 'geometry'
2025-08-04 16:56:49,443 - ERROR - 处理要素 442 失败: 'Series' object has no attribute 'geometry'
2025-08-04 16:56:49,443 - ERROR - 处理要素 443 失败: 'Series' object has no attribute 'geometry'
2025-08-04 16:56:49,443 - ERROR - 处理要素 444 失败: 'Series' object has no attribute 'geometry'
2025-08-04 16:56:49,443 - ERROR - 处理要素 445 失败: 'Series' object has no attribute 'geometry'
2025-08-04 16:56:49,443 - ERROR - 处理要素 446 失败: 'Series' object has no attribute 'geometry'
2025-08-04 16:56:49,443 - ERROR - 处理要素 447 失败: 'Series' object has no attribute 'geometry'
2025-08-04 16:56:49,443 - ERROR - 处理要素 448 失败: 'Series' object has no attribute 'geometry'
2025-08-04 16:56:49,443 - ERROR - 处理要素 449 失败: 'Series' object has no attribute 'geometry'
2025-08-04 16:56:49,443 - ERROR - 处理要素 450 失败: 'Series' object has no attribute 'geometry'
2025-08-04 16:56:49,443 - ERROR - 处理要素 451 失败: 'Series' object has no attribute 'geometry'
2025-08-04 16:56:49,443 - ERROR - 处理要素 452 失败: 'Series' object has no attribute 'geometry'
2025-08-04 16:56:49,443 - ERROR - 处理要素 453 失败: 'Series' object has no attribute 'geometry'
2025-08-04 16:56:49,443 - ERROR - 处理要素 454 失败: 'Series' object has no attribute 'geometry'
2025-08-04 16:56:49,443 - ERROR - 处理要素 455 失败: 'Series' object has no attribute 'geometry'
2025-08-04 16:56:49,443 - ERROR - 处理要素 456 失败: 'Series' object has no attribute 'geometry'
2025-08-04 16:56:49,443 - ERROR - 处理要素 457 失败: 'Series' object has no attribute 'geometry'
2025-08-04 16:56:49,443 - ERROR - 处理要素 458 失败: 'Series' object has no attribute 'geometry'
2025-08-04 16:56:49,443 - ERROR - 处理要素 459 失败: 'Series' object has no attribute 'geometry'
2025-08-04 16:56:49,443 - ERROR - 处理要素 460 失败: 'Series' object has no attribute 'geometry'
2025-08-04 16:56:49,443 - ERROR - 处理要素 461 失败: 'Series' object has no attribute 'geometry'
2025-08-04 16:56:49,443 - ERROR - 处理要素 462 失败: 'Series' object has no attribute 'geometry'
2025-08-04 16:56:49,443 - ERROR - 处理要素 463 失败: 'Series' object has no attribute 'geometry'
2025-08-04 16:56:49,443 - ERROR - 处理要素 464 失败: 'Series' object has no attribute 'geometry'
2025-08-04 16:56:49,443 - ERROR - 处理要素 465 失败: 'Series' object has no attribute 'geometry'
2025-08-04 16:56:49,443 - ERROR - 处理要素 466 失败: 'Series' object has no attribute 'geometry'
2025-08-04 16:56:49,443 - ERROR - 处理要素 467 失败: 'Series' object has no attribute 'geometry'
2025-08-04 16:56:49,443 - ERROR - 处理要素 468 失败: 'Series' object has no attribute 'geometry'
2025-08-04 16:56:49,443 - ERROR - 处理要素 469 失败: 'Series' object has no attribute 'geometry'
2025-08-04 16:56:49,443 - ERROR - 处理要素 470 失败: 'Series' object has no attribute 'geometry'
2025-08-04 16:56:49,444 - ERROR - 处理要素 471 失败: 'Series' object has no attribute 'geometry'
2025-08-04 16:56:49,444 - ERROR - 处理要素 472 失败: 'Series' object has no attribute 'geometry'
2025-08-04 16:56:49,444 - ERROR - 处理要素 473 失败: 'Series' object has no attribute 'geometry'
2025-08-04 16:56:49,444 - ERROR - 处理要素 474 失败: 'Series' object has no attribute 'geometry'
2025-08-04 16:56:49,444 - ERROR - 处理要素 475 失败: 'Series' object has no attribute 'geometry'
2025-08-04 16:56:49,444 - ERROR - 处理要素 476 失败: 'Series' object has no attribute 'geometry'
2025-08-04 16:56:49,444 - ERROR - 处理要素 477 失败: 'Series' object has no attribute 'geometry'
2025-08-04 16:56:49,444 - ERROR - 处理要素 478 失败: 'Series' object has no attribute 'geometry'
2025-08-04 16:56:49,444 - ERROR - 处理要素 479 失败: 'Series' object has no attribute 'geometry'
2025-08-04 16:56:49,444 - ERROR - 处理要素 480 失败: 'Series' object has no attribute 'geometry'
2025-08-04 16:56:49,444 - ERROR - 处理要素 481 失败: 'Series' object has no attribute 'geometry'
2025-08-04 16:56:49,444 - ERROR - 处理要素 482 失败: 'Series' object has no attribute 'geometry'
2025-08-04 16:56:49,444 - ERROR - 处理要素 483 失败: 'Series' object has no attribute 'geometry'
2025-08-04 16:56:49,444 - ERROR - 处理要素 484 失败: 'Series' object has no attribute 'geometry'
2025-08-04 16:56:49,444 - ERROR - 处理要素 485 失败: 'Series' object has no attribute 'geometry'
2025-08-04 16:56:49,444 - ERROR - 处理要素 486 失败: 'Series' object has no attribute 'geometry'
2025-08-04 16:56:49,444 - ERROR - 处理要素 487 失败: 'Series' object has no attribute 'geometry'
2025-08-04 16:56:49,444 - ERROR - 处理要素 488 失败: 'Series' object has no attribute 'geometry'
2025-08-04 16:56:49,444 - ERROR - 处理要素 489 失败: 'Series' object has no attribute 'geometry'
2025-08-04 16:56:49,444 - ERROR - 处理要素 490 失败: 'Series' object has no attribute 'geometry'
2025-08-04 16:56:49,444 - ERROR - 处理要素 491 失败: 'Series' object has no attribute 'geometry'
2025-08-04 16:56:49,444 - ERROR - 处理要素 492 失败: 'Series' object has no attribute 'geometry'
2025-08-04 16:56:49,444 - ERROR - 处理要素 493 失败: 'Series' object has no attribute 'geometry'
2025-08-04 16:56:49,444 - ERROR - 处理要素 494 失败: 'Series' object has no attribute 'geometry'
2025-08-04 16:56:49,444 - ERROR - 处理要素 495 失败: 'Series' object has no attribute 'geometry'
2025-08-04 16:56:49,444 - ERROR - 处理要素 496 失败: 'Series' object has no attribute 'geometry'
2025-08-04 16:56:49,444 - ERROR - 处理要素 497 失败: 'Series' object has no attribute 'geometry'
2025-08-04 16:56:49,444 - ERROR - 处理要素 498 失败: 'Series' object has no attribute 'geometry'
2025-08-04 16:56:49,444 - ERROR - 处理要素 499 失败: 'Series' object has no attribute 'geometry'
2025-08-04 16:56:49,445 - ERROR - 处理要素 500 失败: 'Series' object has no attribute 'geometry'
2025-08-04 16:56:49,445 - ERROR - 处理要素 501 失败: 'Series' object has no attribute 'geometry'
2025-08-04 16:56:49,445 - ERROR - 处理要素 502 失败: 'Series' object has no attribute 'geometry'
2025-08-04 16:56:49,445 - ERROR - 处理要素 503 失败: 'Series' object has no attribute 'geometry'
2025-08-04 16:56:49,445 - ERROR - 处理要素 504 失败: 'Series' object has no attribute 'geometry'
2025-08-04 16:56:49,445 - ERROR - 处理要素 505 失败: 'Series' object has no attribute 'geometry'
2025-08-04 16:56:49,445 - ERROR - 处理要素 506 失败: 'Series' object has no attribute 'geometry'
2025-08-04 16:56:49,445 - ERROR - 处理要素 507 失败: 'Series' object has no attribute 'geometry'
2025-08-04 16:56:49,445 - ERROR - 处理要素 508 失败: 'Series' object has no attribute 'geometry'
2025-08-04 16:56:49,445 - ERROR - 处理要素 509 失败: 'Series' object has no attribute 'geometry'
2025-08-04 16:56:49,445 - ERROR - 处理要素 510 失败: 'Series' object has no attribute 'geometry'
2025-08-04 16:56:49,445 - ERROR - 处理要素 511 失败: 'Series' object has no attribute 'geometry'
2025-08-04 16:56:49,445 - ERROR - 处理要素 512 失败: 'Series' object has no attribute 'geometry'
2025-08-04 16:56:49,445 - ERROR - 处理要素 513 失败: 'Series' object has no attribute 'geometry'
2025-08-04 16:56:49,445 - ERROR - 处理要素 514 失败: 'Series' object has no attribute 'geometry'
2025-08-04 16:56:49,445 - ERROR - 处理要素 515 失败: 'Series' object has no attribute 'geometry'
2025-08-04 16:56:49,445 - ERROR - 处理要素 516 失败: 'Series' object has no attribute 'geometry'
2025-08-04 16:56:49,445 - ERROR - 处理要素 517 失败: 'Series' object has no attribute 'geometry'
2025-08-04 16:56:49,445 - ERROR - 处理要素 518 失败: 'Series' object has no attribute 'geometry'
2025-08-04 16:56:49,445 - ERROR - 处理要素 519 失败: 'Series' object has no attribute 'geometry'
2025-08-04 16:56:49,445 - ERROR - 处理要素 520 失败: 'Series' object has no attribute 'geometry'
2025-08-04 16:56:49,445 - ERROR - 处理要素 521 失败: 'Series' object has no attribute 'geometry'
2025-08-04 16:56:49,445 - ERROR - 处理要素 522 失败: 'Series' object has no attribute 'geometry'
2025-08-04 16:56:49,445 - ERROR - 处理要素 523 失败: 'Series' object has no attribute 'geometry'
2025-08-04 16:56:49,445 - ERROR - 处理要素 524 失败: 'Series' object has no attribute 'geometry'
2025-08-04 16:56:49,445 - ERROR - 处理要素 525 失败: 'Series' object has no attribute 'geometry'
2025-08-04 16:56:49,446 - ERROR - 处理要素 526 失败: 'Series' object has no attribute 'geometry'
2025-08-04 16:56:49,446 - ERROR - 处理要素 527 失败: 'Series' object has no attribute 'geometry'
2025-08-04 16:56:49,446 - ERROR - 处理要素 528 失败: 'Series' object has no attribute 'geometry'
2025-08-04 16:56:49,446 - ERROR - 处理要素 529 失败: 'Series' object has no attribute 'geometry'
2025-08-04 16:56:49,446 - ERROR - 处理要素 530 失败: 'Series' object has no attribute 'geometry'
2025-08-04 16:56:49,446 - ERROR - 处理要素 531 失败: 'Series' object has no attribute 'geometry'
2025-08-04 16:56:49,446 - ERROR - 处理要素 532 失败: 'Series' object has no attribute 'geometry'
2025-08-04 16:56:49,446 - ERROR - 处理要素 533 失败: 'Series' object has no attribute 'geometry'
2025-08-04 16:56:49,446 - ERROR - 处理要素 534 失败: 'Series' object has no attribute 'geometry'
2025-08-04 16:56:49,446 - ERROR - 处理要素 535 失败: 'Series' object has no attribute 'geometry'
2025-08-04 16:56:49,446 - ERROR - 处理要素 536 失败: 'Series' object has no attribute 'geometry'
2025-08-04 16:56:49,446 - ERROR - 处理要素 537 失败: 'Series' object has no attribute 'geometry'
2025-08-04 16:56:49,446 - ERROR - 处理要素 538 失败: 'Series' object has no attribute 'geometry'
2025-08-04 16:56:49,446 - ERROR - 处理要素 539 失败: 'Series' object has no attribute 'geometry'
2025-08-04 16:56:49,447 - ERROR - 处理要素 540 失败: 'Series' object has no attribute 'geometry'
2025-08-04 16:56:49,447 - ERROR - 处理要素 541 失败: 'Series' object has no attribute 'geometry'
2025-08-04 16:56:49,447 - ERROR - 处理要素 542 失败: 'Series' object has no attribute 'geometry'
2025-08-04 16:56:49,447 - ERROR - 处理要素 543 失败: 'Series' object has no attribute 'geometry'
2025-08-04 16:56:49,447 - ERROR - 处理要素 544 失败: 'Series' object has no attribute 'geometry'
2025-08-04 16:56:49,447 - ERROR - 处理要素 545 失败: 'Series' object has no attribute 'geometry'
2025-08-04 16:56:49,447 - ERROR - 处理要素 546 失败: 'Series' object has no attribute 'geometry'
2025-08-04 16:56:49,447 - ERROR - 处理要素 547 失败: 'Series' object has no attribute 'geometry'
2025-08-04 16:56:49,447 - ERROR - 处理要素 548 失败: 'Series' object has no attribute 'geometry'
2025-08-04 16:56:49,447 - ERROR - 处理要素 549 失败: 'Series' object has no attribute 'geometry'
2025-08-04 16:56:49,447 - ERROR - 处理要素 550 失败: 'Series' object has no attribute 'geometry'
2025-08-04 16:56:49,447 - ERROR - 处理要素 551 失败: 'Series' object has no attribute 'geometry'
2025-08-04 16:56:49,447 - ERROR - 处理要素 552 失败: 'Series' object has no attribute 'geometry'
2025-08-04 16:56:49,447 - ERROR - 处理要素 553 失败: 'Series' object has no attribute 'geometry'
2025-08-04 16:56:49,447 - ERROR - 处理要素 554 失败: 'Series' object has no attribute 'geometry'
2025-08-04 16:56:49,447 - ERROR - 处理要素 555 失败: 'Series' object has no attribute 'geometry'
2025-08-04 16:56:49,447 - ERROR - 处理要素 556 失败: 'Series' object has no attribute 'geometry'
2025-08-04 16:56:49,447 - ERROR - 处理要素 557 失败: 'Series' object has no attribute 'geometry'
2025-08-04 16:56:49,447 - ERROR - 处理要素 558 失败: 'Series' object has no attribute 'geometry'
2025-08-04 16:56:49,447 - ERROR - 处理要素 559 失败: 'Series' object has no attribute 'geometry'
2025-08-04 16:56:49,447 - ERROR - 处理要素 560 失败: 'Series' object has no attribute 'geometry'
2025-08-04 16:56:49,447 - ERROR - 处理要素 561 失败: 'Series' object has no attribute 'geometry'
2025-08-04 16:56:49,447 - ERROR - 处理要素 562 失败: 'Series' object has no attribute 'geometry'
2025-08-04 16:56:49,447 - ERROR - 处理要素 563 失败: 'Series' object has no attribute 'geometry'
2025-08-04 16:56:49,447 - ERROR - 处理要素 564 失败: 'Series' object has no attribute 'geometry'
2025-08-04 16:56:49,447 - ERROR - 处理要素 565 失败: 'Series' object has no attribute 'geometry'
2025-08-04 16:56:49,447 - ERROR - 处理要素 566 失败: 'Series' object has no attribute 'geometry'
2025-08-04 16:56:49,447 - ERROR - 处理要素 567 失败: 'Series' object has no attribute 'geometry'
2025-08-04 16:56:49,447 - ERROR - 处理要素 568 失败: 'Series' object has no attribute 'geometry'
2025-08-04 16:56:49,447 - ERROR - 处理要素 569 失败: 'Series' object has no attribute 'geometry'
2025-08-04 16:56:49,447 - ERROR - 处理要素 570 失败: 'Series' object has no attribute 'geometry'
2025-08-04 16:56:49,447 - ERROR - 处理要素 571 失败: 'Series' object has no attribute 'geometry'
2025-08-04 16:56:49,447 - ERROR - 处理要素 572 失败: 'Series' object has no attribute 'geometry'
2025-08-04 16:56:49,447 - ERROR - 处理要素 573 失败: 'Series' object has no attribute 'geometry'
2025-08-04 16:56:49,447 - ERROR - 处理要素 574 失败: 'Series' object has no attribute 'geometry'
2025-08-04 16:56:49,448 - ERROR - 处理要素 575 失败: 'Series' object has no attribute 'geometry'
2025-08-04 16:56:49,448 - ERROR - 处理要素 576 失败: 'Series' object has no attribute 'geometry'
2025-08-04 16:56:49,448 - ERROR - 处理要素 577 失败: 'Series' object has no attribute 'geometry'
2025-08-04 16:56:49,448 - ERROR - 处理要素 578 失败: 'Series' object has no attribute 'geometry'
2025-08-04 16:56:49,448 - ERROR - 处理要素 579 失败: 'Series' object has no attribute 'geometry'
2025-08-04 16:56:49,448 - ERROR - 处理要素 580 失败: 'Series' object has no attribute 'geometry'
2025-08-04 16:56:49,448 - ERROR - 处理要素 581 失败: 'Series' object has no attribute 'geometry'
2025-08-04 16:56:49,448 - ERROR - 处理要素 582 失败: 'Series' object has no attribute 'geometry'
2025-08-04 16:56:49,448 - ERROR - 处理要素 583 失败: 'Series' object has no attribute 'geometry'
2025-08-04 16:56:49,448 - ERROR - 处理要素 584 失败: 'Series' object has no attribute 'geometry'
2025-08-04 16:56:49,448 - ERROR - 处理要素 585 失败: 'Series' object has no attribute 'geometry'
2025-08-04 16:56:49,448 - ERROR - 处理要素 586 失败: 'Series' object has no attribute 'geometry'
2025-08-04 16:56:49,448 - ERROR - 处理要素 587 失败: 'Series' object has no attribute 'geometry'
2025-08-04 16:56:49,448 - ERROR - 处理要素 588 失败: 'Series' object has no attribute 'geometry'
2025-08-04 16:56:49,448 - ERROR - 处理要素 589 失败: 'Series' object has no attribute 'geometry'
2025-08-04 16:56:49,448 - ERROR - 处理要素 590 失败: 'Series' object has no attribute 'geometry'
2025-08-04 16:56:49,448 - ERROR - 处理要素 591 失败: 'Series' object has no attribute 'geometry'
2025-08-04 16:56:49,448 - ERROR - 处理要素 592 失败: 'Series' object has no attribute 'geometry'
2025-08-04 16:56:49,448 - ERROR - 处理要素 593 失败: 'Series' object has no attribute 'geometry'
2025-08-04 16:56:49,448 - ERROR - 处理要素 594 失败: 'Series' object has no attribute 'geometry'
2025-08-04 16:56:49,448 - ERROR - 处理要素 595 失败: 'Series' object has no attribute 'geometry'
2025-08-04 16:56:49,448 - ERROR - 处理要素 596 失败: 'Series' object has no attribute 'geometry'
2025-08-04 16:56:49,448 - ERROR - 处理要素 597 失败: 'Series' object has no attribute 'geometry'
2025-08-04 16:56:49,448 - ERROR - 处理要素 598 失败: 'Series' object has no attribute 'geometry'
2025-08-04 16:56:49,448 - ERROR - 处理要素 599 失败: 'Series' object has no attribute 'geometry'
2025-08-04 16:56:49,448 - ERROR - 处理要素 600 失败: 'Series' object has no attribute 'geometry'
2025-08-04 16:56:49,449 - ERROR - 处理要素 601 失败: 'Series' object has no attribute 'geometry'
2025-08-04 16:56:49,449 - ERROR - 处理要素 602 失败: 'Series' object has no attribute 'geometry'
2025-08-04 16:56:49,449 - ERROR - 处理要素 603 失败: 'Series' object has no attribute 'geometry'
2025-08-04 16:56:49,449 - ERROR - 处理要素 604 失败: 'Series' object has no attribute 'geometry'
2025-08-04 16:56:49,449 - ERROR - 处理要素 605 失败: 'Series' object has no attribute 'geometry'
2025-08-04 16:56:49,449 - ERROR - 处理要素 606 失败: 'Series' object has no attribute 'geometry'
2025-08-04 16:56:49,449 - ERROR - 处理要素 607 失败: 'Series' object has no attribute 'geometry'
2025-08-04 16:56:49,449 - ERROR - 处理要素 608 失败: 'Series' object has no attribute 'geometry'
2025-08-04 16:56:49,449 - ERROR - 处理要素 609 失败: 'Series' object has no attribute 'geometry'
2025-08-04 16:56:49,449 - ERROR - 处理要素 610 失败: 'Series' object has no attribute 'geometry'
2025-08-04 16:56:49,449 - ERROR - 处理要素 611 失败: 'Series' object has no attribute 'geometry'
2025-08-04 16:56:49,449 - ERROR - 处理要素 612 失败: 'Series' object has no attribute 'geometry'
2025-08-04 16:56:49,450 - ERROR - 处理要素 613 失败: 'Series' object has no attribute 'geometry'
2025-08-04 16:56:49,450 - ERROR - 处理要素 614 失败: 'Series' object has no attribute 'geometry'
2025-08-04 16:56:49,450 - ERROR - 处理要素 615 失败: 'Series' object has no attribute 'geometry'
2025-08-04 16:56:49,450 - ERROR - 处理要素 616 失败: 'Series' object has no attribute 'geometry'
2025-08-04 16:56:49,450 - ERROR - 处理要素 617 失败: 'Series' object has no attribute 'geometry'
2025-08-04 16:56:49,450 - ERROR - 处理要素 618 失败: 'Series' object has no attribute 'geometry'
2025-08-04 16:56:49,450 - ERROR - 处理要素 619 失败: 'Series' object has no attribute 'geometry'
2025-08-04 16:56:49,450 - ERROR - 处理要素 620 失败: 'Series' object has no attribute 'geometry'
2025-08-04 16:56:49,450 - ERROR - 处理要素 621 失败: 'Series' object has no attribute 'geometry'
2025-08-04 16:56:49,450 - ERROR - 处理要素 622 失败: 'Series' object has no attribute 'geometry'
2025-08-04 16:56:49,450 - ERROR - 处理要素 623 失败: 'Series' object has no attribute 'geometry'
2025-08-04 16:56:49,450 - ERROR - 处理要素 624 失败: 'Series' object has no attribute 'geometry'
2025-08-04 16:56:49,450 - ERROR - 处理要素 625 失败: 'Series' object has no attribute 'geometry'
2025-08-04 16:56:49,450 - ERROR - 处理要素 626 失败: 'Series' object has no attribute 'geometry'
2025-08-04 16:56:49,450 - ERROR - 处理要素 627 失败: 'Series' object has no attribute 'geometry'
2025-08-04 16:56:49,450 - ERROR - 处理要素 628 失败: 'Series' object has no attribute 'geometry'
2025-08-04 16:56:49,450 - ERROR - 处理要素 629 失败: 'Series' object has no attribute 'geometry'
2025-08-04 16:56:49,450 - ERROR - 处理要素 630 失败: 'Series' object has no attribute 'geometry'
2025-08-04 16:56:49,450 - ERROR - 处理要素 631 失败: 'Series' object has no attribute 'geometry'
2025-08-04 16:56:49,451 - ERROR - 处理要素 632 失败: 'Series' object has no attribute 'geometry'
2025-08-04 16:56:49,451 - ERROR - 处理要素 633 失败: 'Series' object has no attribute 'geometry'
2025-08-04 16:56:49,451 - ERROR - 处理要素 634 失败: 'Series' object has no attribute 'geometry'
2025-08-04 16:56:49,451 - ERROR - 处理要素 635 失败: 'Series' object has no attribute 'geometry'
2025-08-04 16:56:49,451 - ERROR - 处理要素 636 失败: 'Series' object has no attribute 'geometry'
2025-08-04 16:56:49,451 - ERROR - 处理要素 637 失败: 'Series' object has no attribute 'geometry'
2025-08-04 16:56:49,451 - ERROR - 处理要素 638 失败: 'Series' object has no attribute 'geometry'
2025-08-04 16:56:49,451 - ERROR - 处理要素 639 失败: 'Series' object has no attribute 'geometry'
2025-08-04 16:56:49,451 - ERROR - 处理要素 640 失败: 'Series' object has no attribute 'geometry'
2025-08-04 16:56:49,451 - ERROR - 处理要素 641 失败: 'Series' object has no attribute 'geometry'
2025-08-04 16:56:49,451 - ERROR - 处理要素 642 失败: 'Series' object has no attribute 'geometry'
2025-08-04 16:56:49,451 - ERROR - 处理要素 643 失败: 'Series' object has no attribute 'geometry'
2025-08-04 16:56:49,451 - ERROR - 处理要素 644 失败: 'Series' object has no attribute 'geometry'
2025-08-04 16:56:49,451 - ERROR - 处理要素 645 失败: 'Series' object has no attribute 'geometry'
2025-08-04 16:56:49,451 - ERROR - 处理要素 646 失败: 'Series' object has no attribute 'geometry'
2025-08-04 16:56:49,451 - ERROR - 处理要素 647 失败: 'Series' object has no attribute 'geometry'
2025-08-04 16:56:49,451 - ERROR - 处理要素 648 失败: 'Series' object has no attribute 'geometry'
2025-08-04 16:56:49,451 - ERROR - 处理要素 649 失败: 'Series' object has no attribute 'geometry'
2025-08-04 16:56:49,451 - ERROR - 处理要素 650 失败: 'Series' object has no attribute 'geometry'
2025-08-04 16:56:49,451 - ERROR - 处理要素 651 失败: 'Series' object has no attribute 'geometry'
2025-08-04 16:56:49,451 - ERROR - 处理要素 652 失败: 'Series' object has no attribute 'geometry'
2025-08-04 16:56:49,451 - ERROR - 处理要素 653 失败: 'Series' object has no attribute 'geometry'
2025-08-04 16:56:49,451 - ERROR - 处理要素 654 失败: 'Series' object has no attribute 'geometry'
2025-08-04 16:56:49,451 - ERROR - 处理要素 655 失败: 'Series' object has no attribute 'geometry'
2025-08-04 16:56:49,451 - ERROR - 处理要素 656 失败: 'Series' object has no attribute 'geometry'
2025-08-04 16:56:49,451 - ERROR - 处理要素 657 失败: 'Series' object has no attribute 'geometry'
2025-08-04 16:56:49,451 - ERROR - 处理要素 658 失败: 'Series' object has no attribute 'geometry'
2025-08-04 16:56:49,451 - ERROR - 处理要素 659 失败: 'Series' object has no attribute 'geometry'
2025-08-04 16:56:49,452 - ERROR - 处理要素 660 失败: 'Series' object has no attribute 'geometry'
2025-08-04 16:56:49,452 - ERROR - 处理要素 661 失败: 'Series' object has no attribute 'geometry'
2025-08-04 16:56:49,452 - ERROR - 处理要素 662 失败: 'Series' object has no attribute 'geometry'
2025-08-04 16:56:49,452 - ERROR - 处理要素 663 失败: 'Series' object has no attribute 'geometry'
2025-08-04 16:56:49,452 - ERROR - 处理要素 664 失败: 'Series' object has no attribute 'geometry'
2025-08-04 16:56:49,452 - ERROR - 处理要素 665 失败: 'Series' object has no attribute 'geometry'
2025-08-04 16:56:49,452 - ERROR - 处理要素 666 失败: 'Series' object has no attribute 'geometry'
2025-08-04 16:56:49,452 - ERROR - 处理要素 667 失败: 'Series' object has no attribute 'geometry'
2025-08-04 16:56:49,452 - ERROR - 处理要素 668 失败: 'Series' object has no attribute 'geometry'
2025-08-04 16:56:49,452 - ERROR - 处理要素 669 失败: 'Series' object has no attribute 'geometry'
2025-08-04 16:56:49,452 - ERROR - 处理要素 670 失败: 'Series' object has no attribute 'geometry'
2025-08-04 16:56:49,452 - ERROR - 处理要素 671 失败: 'Series' object has no attribute 'geometry'
2025-08-04 16:56:49,452 - ERROR - 处理要素 672 失败: 'Series' object has no attribute 'geometry'
2025-08-04 16:56:49,452 - ERROR - 处理要素 673 失败: 'Series' object has no attribute 'geometry'
2025-08-04 16:56:49,452 - ERROR - 处理要素 674 失败: 'Series' object has no attribute 'geometry'
2025-08-04 16:56:49,452 - ERROR - 处理要素 675 失败: 'Series' object has no attribute 'geometry'
2025-08-04 16:56:49,452 - ERROR - 处理要素 676 失败: 'Series' object has no attribute 'geometry'
2025-08-04 16:56:49,452 - ERROR - 处理要素 677 失败: 'Series' object has no attribute 'geometry'
2025-08-04 16:56:49,452 - ERROR - 处理要素 678 失败: 'Series' object has no attribute 'geometry'
2025-08-04 16:56:49,452 - ERROR - 处理要素 679 失败: 'Series' object has no attribute 'geometry'
2025-08-04 16:56:49,452 - ERROR - 处理要素 680 失败: 'Series' object has no attribute 'geometry'
2025-08-04 16:56:49,452 - ERROR - 处理要素 681 失败: 'Series' object has no attribute 'geometry'
2025-08-04 16:56:49,453 - ERROR - 处理要素 682 失败: 'Series' object has no attribute 'geometry'
2025-08-04 16:56:49,453 - ERROR - 处理要素 683 失败: 'Series' object has no attribute 'geometry'
2025-08-04 16:56:49,453 - ERROR - 处理要素 684 失败: 'Series' object has no attribute 'geometry'
2025-08-04 16:56:49,453 - ERROR - 处理要素 685 失败: 'Series' object has no attribute 'geometry'
2025-08-04 16:56:49,453 - ERROR - 处理要素 686 失败: 'Series' object has no attribute 'geometry'
2025-08-04 16:56:49,453 - ERROR - 处理要素 687 失败: 'Series' object has no attribute 'geometry'
2025-08-04 16:56:49,453 - ERROR - 处理要素 688 失败: 'Series' object has no attribute 'geometry'
2025-08-04 16:56:49,453 - ERROR - 处理要素 689 失败: 'Series' object has no attribute 'geometry'
2025-08-04 16:56:49,453 - ERROR - 处理要素 690 失败: 'Series' object has no attribute 'geometry'
2025-08-04 16:56:49,453 - ERROR - 处理要素 691 失败: 'Series' object has no attribute 'geometry'
2025-08-04 16:56:49,453 - ERROR - 处理要素 692 失败: 'Series' object has no attribute 'geometry'
2025-08-04 16:56:49,453 - ERROR - 处理要素 693 失败: 'Series' object has no attribute 'geometry'
2025-08-04 16:56:49,453 - ERROR - 处理要素 694 失败: 'Series' object has no attribute 'geometry'
2025-08-04 16:56:49,453 - ERROR - 处理要素 695 失败: 'Series' object has no attribute 'geometry'
2025-08-04 16:56:49,453 - ERROR - 处理要素 696 失败: 'Series' object has no attribute 'geometry'
2025-08-04 16:56:49,453 - ERROR - 处理要素 697 失败: 'Series' object has no attribute 'geometry'
2025-08-04 16:56:49,453 - ERROR - 处理要素 698 失败: 'Series' object has no attribute 'geometry'
2025-08-04 16:56:49,453 - ERROR - 处理要素 699 失败: 'Series' object has no attribute 'geometry'
2025-08-04 16:56:49,454 - ERROR - 处理要素 700 失败: 'Series' object has no attribute 'geometry'
2025-08-04 16:56:49,454 - ERROR - 处理要素 701 失败: 'Series' object has no attribute 'geometry'
2025-08-04 16:56:49,454 - ERROR - 处理要素 702 失败: 'Series' object has no attribute 'geometry'
2025-08-04 16:56:49,454 - ERROR - 处理要素 703 失败: 'Series' object has no attribute 'geometry'
2025-08-04 16:56:49,454 - ERROR - 处理要素 704 失败: 'Series' object has no attribute 'geometry'
2025-08-04 16:56:49,454 - ERROR - 处理要素 705 失败: 'Series' object has no attribute 'geometry'
2025-08-04 16:56:49,454 - ERROR - 处理要素 706 失败: 'Series' object has no attribute 'geometry'
2025-08-04 16:56:49,454 - ERROR - 处理要素 707 失败: 'Series' object has no attribute 'geometry'
2025-08-04 16:56:49,454 - ERROR - 处理要素 708 失败: 'Series' object has no attribute 'geometry'
2025-08-04 16:56:49,454 - ERROR - 处理要素 709 失败: 'Series' object has no attribute 'geometry'
2025-08-04 16:56:49,454 - ERROR - 处理要素 710 失败: 'Series' object has no attribute 'geometry'
2025-08-04 16:56:49,454 - ERROR - 处理要素 711 失败: 'Series' object has no attribute 'geometry'
2025-08-04 16:56:49,454 - ERROR - 处理要素 712 失败: 'Series' object has no attribute 'geometry'
2025-08-04 16:56:49,454 - ERROR - 处理要素 713 失败: 'Series' object has no attribute 'geometry'
2025-08-04 16:56:49,454 - ERROR - 处理要素 714 失败: 'Series' object has no attribute 'geometry'
2025-08-04 16:56:49,454 - ERROR - 处理要素 715 失败: 'Series' object has no attribute 'geometry'
2025-08-04 16:56:49,455 - ERROR - 处理要素 716 失败: 'Series' object has no attribute 'geometry'
2025-08-04 16:56:49,455 - ERROR - 处理要素 717 失败: 'Series' object has no attribute 'geometry'
2025-08-04 16:56:49,455 - ERROR - 处理要素 718 失败: 'Series' object has no attribute 'geometry'
2025-08-04 16:56:49,455 - ERROR - 处理要素 719 失败: 'Series' object has no attribute 'geometry'
2025-08-04 16:56:49,455 - ERROR - 处理要素 720 失败: 'Series' object has no attribute 'geometry'
2025-08-04 16:56:49,455 - ERROR - 处理要素 721 失败: 'Series' object has no attribute 'geometry'
2025-08-04 16:56:49,455 - ERROR - 处理要素 722 失败: 'Series' object has no attribute 'geometry'
2025-08-04 16:56:49,455 - ERROR - 处理要素 723 失败: 'Series' object has no attribute 'geometry'
2025-08-04 16:56:49,455 - ERROR - 处理要素 724 失败: 'Series' object has no attribute 'geometry'
2025-08-04 16:56:49,455 - ERROR - 处理要素 725 失败: 'Series' object has no attribute 'geometry'
2025-08-04 16:56:49,455 - ERROR - 处理要素 726 失败: 'Series' object has no attribute 'geometry'
2025-08-04 16:56:49,455 - ERROR - 处理要素 727 失败: 'Series' object has no attribute 'geometry'
2025-08-04 16:56:49,455 - ERROR - 处理要素 728 失败: 'Series' object has no attribute 'geometry'
2025-08-04 16:56:49,455 - ERROR - 处理要素 729 失败: 'Series' object has no attribute 'geometry'
2025-08-04 16:56:49,455 - ERROR - 处理要素 730 失败: 'Series' object has no attribute 'geometry'
2025-08-04 16:56:49,455 - ERROR - 处理要素 731 失败: 'Series' object has no attribute 'geometry'
2025-08-04 16:56:49,455 - ERROR - 处理要素 732 失败: 'Series' object has no attribute 'geometry'
2025-08-04 16:56:49,455 - ERROR - 处理要素 733 失败: 'Series' object has no attribute 'geometry'
2025-08-04 16:56:49,455 - ERROR - 处理要素 734 失败: 'Series' object has no attribute 'geometry'
2025-08-04 16:56:49,455 - ERROR - 处理要素 735 失败: 'Series' object has no attribute 'geometry'
2025-08-04 16:56:49,455 - ERROR - 处理要素 736 失败: 'Series' object has no attribute 'geometry'
2025-08-04 16:56:49,455 - ERROR - 处理要素 737 失败: 'Series' object has no attribute 'geometry'
2025-08-04 16:56:49,455 - ERROR - 处理要素 738 失败: 'Series' object has no attribute 'geometry'
2025-08-04 16:56:49,455 - ERROR - 处理要素 739 失败: 'Series' object has no attribute 'geometry'
2025-08-04 16:56:49,455 - ERROR - 处理要素 740 失败: 'Series' object has no attribute 'geometry'
2025-08-04 16:56:49,455 - ERROR - 处理要素 741 失败: 'Series' object has no attribute 'geometry'
2025-08-04 16:56:49,455 - ERROR - 处理要素 742 失败: 'Series' object has no attribute 'geometry'
2025-08-04 16:56:49,455 - ERROR - 处理要素 743 失败: 'Series' object has no attribute 'geometry'
2025-08-04 16:56:49,455 - ERROR - 处理要素 744 失败: 'Series' object has no attribute 'geometry'
2025-08-04 16:56:49,455 - ERROR - 处理要素 745 失败: 'Series' object has no attribute 'geometry'
2025-08-04 16:56:49,455 - ERROR - 处理要素 746 失败: 'Series' object has no attribute 'geometry'
2025-08-04 16:56:49,455 - ERROR - 处理要素 747 失败: 'Series' object has no attribute 'geometry'
2025-08-04 16:56:49,455 - ERROR - 处理要素 748 失败: 'Series' object has no attribute 'geometry'
2025-08-04 16:56:49,455 - ERROR - 处理要素 749 失败: 'Series' object has no attribute 'geometry'
2025-08-04 16:56:49,455 - ERROR - 处理要素 750 失败: 'Series' object has no attribute 'geometry'
2025-08-04 16:56:49,456 - ERROR - 处理要素 751 失败: 'Series' object has no attribute 'geometry'
2025-08-04 16:56:49,456 - ERROR - 处理要素 752 失败: 'Series' object has no attribute 'geometry'
2025-08-04 16:56:49,456 - ERROR - 处理要素 753 失败: 'Series' object has no attribute 'geometry'
2025-08-04 16:56:49,456 - ERROR - 处理要素 754 失败: 'Series' object has no attribute 'geometry'
2025-08-04 16:56:49,456 - ERROR - 处理要素 755 失败: 'Series' object has no attribute 'geometry'
2025-08-04 16:56:49,456 - ERROR - 处理要素 756 失败: 'Series' object has no attribute 'geometry'
2025-08-04 16:56:49,456 - ERROR - 处理要素 757 失败: 'Series' object has no attribute 'geometry'
2025-08-04 16:56:49,456 - ERROR - 处理要素 758 失败: 'Series' object has no attribute 'geometry'
2025-08-04 16:56:49,456 - ERROR - 处理要素 759 失败: 'Series' object has no attribute 'geometry'
2025-08-04 16:56:49,456 - ERROR - 处理要素 760 失败: 'Series' object has no attribute 'geometry'
2025-08-04 16:56:49,456 - ERROR - 处理要素 761 失败: 'Series' object has no attribute 'geometry'
2025-08-04 16:56:49,456 - ERROR - 处理要素 762 失败: 'Series' object has no attribute 'geometry'
2025-08-04 16:56:49,456 - ERROR - 处理要素 763 失败: 'Series' object has no attribute 'geometry'
2025-08-04 16:56:49,456 - ERROR - 处理要素 764 失败: 'Series' object has no attribute 'geometry'
2025-08-04 16:56:49,456 - ERROR - 处理要素 765 失败: 'Series' object has no attribute 'geometry'
2025-08-04 16:56:49,456 - ERROR - 处理要素 766 失败: 'Series' object has no attribute 'geometry'
2025-08-04 16:56:49,456 - ERROR - 处理要素 767 失败: 'Series' object has no attribute 'geometry'
2025-08-04 16:56:49,456 - ERROR - 处理要素 768 失败: 'Series' object has no attribute 'geometry'
2025-08-04 16:56:49,456 - ERROR - 处理要素 769 失败: 'Series' object has no attribute 'geometry'
2025-08-04 16:56:49,456 - ERROR - 处理要素 770 失败: 'Series' object has no attribute 'geometry'
2025-08-04 16:56:49,456 - ERROR - 处理要素 771 失败: 'Series' object has no attribute 'geometry'
2025-08-04 16:56:49,456 - ERROR - 处理要素 772 失败: 'Series' object has no attribute 'geometry'
2025-08-04 16:56:49,456 - ERROR - 处理要素 773 失败: 'Series' object has no attribute 'geometry'
2025-08-04 16:56:49,457 - ERROR - 处理要素 774 失败: 'Series' object has no attribute 'geometry'
2025-08-04 16:56:49,457 - ERROR - 处理要素 775 失败: 'Series' object has no attribute 'geometry'
2025-08-04 16:56:49,457 - ERROR - 处理要素 776 失败: 'Series' object has no attribute 'geometry'
2025-08-04 16:56:49,457 - ERROR - 处理要素 777 失败: 'Series' object has no attribute 'geometry'
2025-08-04 16:56:49,457 - ERROR - 处理要素 778 失败: 'Series' object has no attribute 'geometry'
2025-08-04 16:56:49,457 - ERROR - 处理要素 779 失败: 'Series' object has no attribute 'geometry'
2025-08-04 16:56:49,457 - ERROR - 处理要素 780 失败: 'Series' object has no attribute 'geometry'
2025-08-04 16:56:49,457 - ERROR - 处理要素 781 失败: 'Series' object has no attribute 'geometry'
2025-08-04 16:56:49,457 - ERROR - 处理要素 782 失败: 'Series' object has no attribute 'geometry'
2025-08-04 16:56:49,457 - INFO - 图层 SSCMYDBJGH_ExportTable 导入完成，共导入 0 个要素
2025-08-04 16:56:49,458 - INFO - ✓ 图层 'SSCMYDBJGH_ExportTable' 成功导入到表 '510903xx_sscmydbjgh_exporttable'
2025-08-04 16:56:49,458 - INFO - 正在为图层 'SSCMYDBJGH_ExportTable3' 创建表: 510903xx_sscmydbjgh_exporttable3
2025-08-04 16:56:49,464 - INFO - 图层 SSCMYDBJGH_ExportTable3 字段分析完成: {'BSM': 'VARCHAR(255)', 'YSDM': 'VARCHAR(255)', 'XZQDM': 'VARCHAR(255)', 'XZQMC': 'VARCHAR(255)', 'DKBH': 'VARCHAR(255)', 'CSLXKZMJ': 'DECIMAL(15,6)', 'CSLVXKZMJ': 'DECIMAL(15,6)', 'CSHXKZMJ': 'DECIMAL(10,4)', 'CSZXKZMJ': 'DECIMAL(10,4)', 'YDMJ': 'DECIMAL(10,4)', 'DXKJGH': 'VARCHAR(255)', 'BZ': 'VARCHAR(255)', 'DYBH': 'VARCHAR(255)', 'DYMC': 'VARCHAR(255)', 'JQBH': 'VARCHAR(255)', 'JQMC': 'VARCHAR(255)', 'YDDM': 'VARCHAR(255)', 'YDMC': 'VARCHAR(255)', 'HHYD': 'VARCHAR(255)', 'ZJZMJ': 'DECIMAL(10,4)', 'SHAPE_Length': 'DECIMAL(18,6)', 'SHAPE_Area': 'DECIMAL(20,6)', 'RJLSX': 'DECIMAL(10,1)', 'JZMDSX': 'DECIMAL(10,1)', 'JZXGSX': 'DECIMAL(10,1)', 'LDLXX': 'DECIMAL(10,1)', 'NJLZL': 'DECIMAL(10,1)', 'PTSS': 'VARCHAR(255)', 'QTKZ': 'VARCHAR(258)', 'TCBWPB': 'VARCHAR(255)', 'OBJECTID': 'TINYINT', 'Join_Count': 'TINYINT', 'TARGET_FID': 'TINYINT', 'BSM_1': 'VARCHAR(255)', 'SHAPE_Length_1': 'DECIMAL(18,6)', 'SHAPE_Area_1': 'DECIMAL(20,6)'}
2025-08-04 16:56:49,740 - INFO - 表 510903xx_sscmydbjgh_exporttable3 创建成功
2025-08-04 16:56:49,740 - INFO - 开始读取图层: SSCMYDBJGH_ExportTable3
2025-08-04 16:56:49,745 - INFO - 图层 SSCMYDBJGH_ExportTable3 包含 327 个要素
2025-08-04 16:56:49,745 - INFO - 图层字段: ['BSM', 'YSDM', 'XZQDM', 'XZQMC', 'DKBH', 'CSLXKZMJ', 'CSLVXKZMJ', 'CSHXKZMJ', 'CSZXKZMJ', 'YDMJ', 'DXKJGH', 'BZ', 'DYBH', 'DYMC', 'JQBH', 'JQMC', 'YDDM', 'YDMC', 'HHYD', 'ZJZMJ', 'SHAPE_Length', 'SHAPE_Area', 'RJLSX', 'JZMDSX', 'JZXGSX', 'LDLXX', 'NJLZL', 'PTSS', 'QTKZ', 'TCBWPB', 'OBJECTID', 'Join_Count', 'TARGET_FID', 'BSM_1', 'SHAPE_Length_1', 'SHAPE_Area_1']
2025-08-04 16:56:49,745 - ERROR - 处理要素 0 失败: 'Series' object has no attribute 'geometry'
2025-08-04 16:56:49,745 - ERROR - 处理要素 1 失败: 'Series' object has no attribute 'geometry'
2025-08-04 16:56:49,745 - ERROR - 处理要素 2 失败: 'Series' object has no attribute 'geometry'
2025-08-04 16:56:49,745 - ERROR - 处理要素 3 失败: 'Series' object has no attribute 'geometry'
2025-08-04 16:56:49,745 - ERROR - 处理要素 4 失败: 'Series' object has no attribute 'geometry'
2025-08-04 16:56:49,745 - ERROR - 处理要素 5 失败: 'Series' object has no attribute 'geometry'
2025-08-04 16:56:49,745 - ERROR - 处理要素 6 失败: 'Series' object has no attribute 'geometry'
2025-08-04 16:56:49,745 - ERROR - 处理要素 7 失败: 'Series' object has no attribute 'geometry'
2025-08-04 16:56:49,745 - ERROR - 处理要素 8 失败: 'Series' object has no attribute 'geometry'
2025-08-04 16:56:49,745 - ERROR - 处理要素 9 失败: 'Series' object has no attribute 'geometry'
2025-08-04 16:56:49,746 - ERROR - 处理要素 10 失败: 'Series' object has no attribute 'geometry'
2025-08-04 16:56:49,746 - ERROR - 处理要素 11 失败: 'Series' object has no attribute 'geometry'
2025-08-04 16:56:49,746 - ERROR - 处理要素 12 失败: 'Series' object has no attribute 'geometry'
2025-08-04 16:56:49,746 - ERROR - 处理要素 13 失败: 'Series' object has no attribute 'geometry'
2025-08-04 16:56:49,746 - ERROR - 处理要素 14 失败: 'Series' object has no attribute 'geometry'
2025-08-04 16:56:49,746 - ERROR - 处理要素 15 失败: 'Series' object has no attribute 'geometry'
2025-08-04 16:56:49,746 - ERROR - 处理要素 16 失败: 'Series' object has no attribute 'geometry'
2025-08-04 16:56:49,746 - ERROR - 处理要素 17 失败: 'Series' object has no attribute 'geometry'
2025-08-04 16:56:49,746 - ERROR - 处理要素 18 失败: 'Series' object has no attribute 'geometry'
2025-08-04 16:56:49,746 - ERROR - 处理要素 19 失败: 'Series' object has no attribute 'geometry'
2025-08-04 16:56:49,746 - ERROR - 处理要素 20 失败: 'Series' object has no attribute 'geometry'
2025-08-04 16:56:49,746 - ERROR - 处理要素 21 失败: 'Series' object has no attribute 'geometry'
2025-08-04 16:56:49,746 - ERROR - 处理要素 22 失败: 'Series' object has no attribute 'geometry'
2025-08-04 16:56:49,746 - ERROR - 处理要素 23 失败: 'Series' object has no attribute 'geometry'
2025-08-04 16:56:49,746 - ERROR - 处理要素 24 失败: 'Series' object has no attribute 'geometry'
2025-08-04 16:56:49,746 - ERROR - 处理要素 25 失败: 'Series' object has no attribute 'geometry'
2025-08-04 16:56:49,746 - ERROR - 处理要素 26 失败: 'Series' object has no attribute 'geometry'
2025-08-04 16:56:49,746 - ERROR - 处理要素 27 失败: 'Series' object has no attribute 'geometry'
2025-08-04 16:56:49,746 - ERROR - 处理要素 28 失败: 'Series' object has no attribute 'geometry'
2025-08-04 16:56:49,746 - ERROR - 处理要素 29 失败: 'Series' object has no attribute 'geometry'
2025-08-04 16:56:49,746 - ERROR - 处理要素 30 失败: 'Series' object has no attribute 'geometry'
2025-08-04 16:56:49,746 - ERROR - 处理要素 31 失败: 'Series' object has no attribute 'geometry'
2025-08-04 16:56:49,746 - ERROR - 处理要素 32 失败: 'Series' object has no attribute 'geometry'
2025-08-04 16:56:49,746 - ERROR - 处理要素 33 失败: 'Series' object has no attribute 'geometry'
2025-08-04 16:56:49,746 - ERROR - 处理要素 34 失败: 'Series' object has no attribute 'geometry'
2025-08-04 16:56:49,746 - ERROR - 处理要素 35 失败: 'Series' object has no attribute 'geometry'
2025-08-04 16:56:49,746 - ERROR - 处理要素 36 失败: 'Series' object has no attribute 'geometry'
2025-08-04 16:56:49,746 - ERROR - 处理要素 37 失败: 'Series' object has no attribute 'geometry'
2025-08-04 16:56:49,746 - ERROR - 处理要素 38 失败: 'Series' object has no attribute 'geometry'
2025-08-04 16:56:49,746 - ERROR - 处理要素 39 失败: 'Series' object has no attribute 'geometry'
2025-08-04 16:56:49,746 - ERROR - 处理要素 40 失败: 'Series' object has no attribute 'geometry'
2025-08-04 16:56:49,746 - ERROR - 处理要素 41 失败: 'Series' object has no attribute 'geometry'
2025-08-04 16:56:49,746 - ERROR - 处理要素 42 失败: 'Series' object has no attribute 'geometry'
2025-08-04 16:56:49,746 - ERROR - 处理要素 43 失败: 'Series' object has no attribute 'geometry'
2025-08-04 16:56:49,746 - ERROR - 处理要素 44 失败: 'Series' object has no attribute 'geometry'
2025-08-04 16:56:49,746 - ERROR - 处理要素 45 失败: 'Series' object has no attribute 'geometry'
2025-08-04 16:56:49,746 - ERROR - 处理要素 46 失败: 'Series' object has no attribute 'geometry'
2025-08-04 16:56:49,747 - ERROR - 处理要素 47 失败: 'Series' object has no attribute 'geometry'
2025-08-04 16:56:49,747 - ERROR - 处理要素 48 失败: 'Series' object has no attribute 'geometry'
2025-08-04 16:56:49,747 - ERROR - 处理要素 49 失败: 'Series' object has no attribute 'geometry'
2025-08-04 16:56:49,747 - ERROR - 处理要素 50 失败: 'Series' object has no attribute 'geometry'
2025-08-04 16:56:49,747 - ERROR - 处理要素 51 失败: 'Series' object has no attribute 'geometry'
2025-08-04 16:56:49,747 - ERROR - 处理要素 52 失败: 'Series' object has no attribute 'geometry'
2025-08-04 16:56:49,747 - ERROR - 处理要素 53 失败: 'Series' object has no attribute 'geometry'
2025-08-04 16:56:49,747 - ERROR - 处理要素 54 失败: 'Series' object has no attribute 'geometry'
2025-08-04 16:56:49,747 - ERROR - 处理要素 55 失败: 'Series' object has no attribute 'geometry'
2025-08-04 16:56:49,747 - ERROR - 处理要素 56 失败: 'Series' object has no attribute 'geometry'
2025-08-04 16:56:49,747 - ERROR - 处理要素 57 失败: 'Series' object has no attribute 'geometry'
2025-08-04 16:56:49,747 - ERROR - 处理要素 58 失败: 'Series' object has no attribute 'geometry'
2025-08-04 16:56:49,747 - ERROR - 处理要素 59 失败: 'Series' object has no attribute 'geometry'
2025-08-04 16:56:49,747 - ERROR - 处理要素 60 失败: 'Series' object has no attribute 'geometry'
2025-08-04 16:56:49,747 - ERROR - 处理要素 61 失败: 'Series' object has no attribute 'geometry'
2025-08-04 16:56:49,747 - ERROR - 处理要素 62 失败: 'Series' object has no attribute 'geometry'
2025-08-04 16:56:49,747 - ERROR - 处理要素 63 失败: 'Series' object has no attribute 'geometry'
2025-08-04 16:56:49,747 - ERROR - 处理要素 64 失败: 'Series' object has no attribute 'geometry'
2025-08-04 16:56:49,747 - ERROR - 处理要素 65 失败: 'Series' object has no attribute 'geometry'
2025-08-04 16:56:49,748 - ERROR - 处理要素 66 失败: 'Series' object has no attribute 'geometry'
2025-08-04 16:56:49,748 - ERROR - 处理要素 67 失败: 'Series' object has no attribute 'geometry'
2025-08-04 16:56:49,748 - ERROR - 处理要素 68 失败: 'Series' object has no attribute 'geometry'
2025-08-04 16:56:49,748 - ERROR - 处理要素 69 失败: 'Series' object has no attribute 'geometry'
2025-08-04 16:56:49,748 - ERROR - 处理要素 70 失败: 'Series' object has no attribute 'geometry'
2025-08-04 16:56:49,748 - ERROR - 处理要素 71 失败: 'Series' object has no attribute 'geometry'
2025-08-04 16:56:49,748 - ERROR - 处理要素 72 失败: 'Series' object has no attribute 'geometry'
2025-08-04 16:56:49,748 - ERROR - 处理要素 73 失败: 'Series' object has no attribute 'geometry'
2025-08-04 16:56:49,748 - ERROR - 处理要素 74 失败: 'Series' object has no attribute 'geometry'
2025-08-04 16:56:49,748 - ERROR - 处理要素 75 失败: 'Series' object has no attribute 'geometry'
2025-08-04 16:56:49,748 - ERROR - 处理要素 76 失败: 'Series' object has no attribute 'geometry'
2025-08-04 16:56:49,748 - ERROR - 处理要素 77 失败: 'Series' object has no attribute 'geometry'
2025-08-04 16:56:49,748 - ERROR - 处理要素 78 失败: 'Series' object has no attribute 'geometry'
2025-08-04 16:56:49,748 - ERROR - 处理要素 79 失败: 'Series' object has no attribute 'geometry'
2025-08-04 16:56:49,748 - ERROR - 处理要素 80 失败: 'Series' object has no attribute 'geometry'
2025-08-04 16:56:49,748 - ERROR - 处理要素 81 失败: 'Series' object has no attribute 'geometry'
2025-08-04 16:56:49,748 - ERROR - 处理要素 82 失败: 'Series' object has no attribute 'geometry'
2025-08-04 16:56:49,748 - ERROR - 处理要素 83 失败: 'Series' object has no attribute 'geometry'
2025-08-04 16:56:49,748 - ERROR - 处理要素 84 失败: 'Series' object has no attribute 'geometry'
2025-08-04 16:56:49,748 - ERROR - 处理要素 85 失败: 'Series' object has no attribute 'geometry'
2025-08-04 16:56:49,748 - ERROR - 处理要素 86 失败: 'Series' object has no attribute 'geometry'
2025-08-04 16:56:49,748 - ERROR - 处理要素 87 失败: 'Series' object has no attribute 'geometry'
2025-08-04 16:56:49,748 - ERROR - 处理要素 88 失败: 'Series' object has no attribute 'geometry'
2025-08-04 16:56:49,748 - ERROR - 处理要素 89 失败: 'Series' object has no attribute 'geometry'
2025-08-04 16:56:49,748 - ERROR - 处理要素 90 失败: 'Series' object has no attribute 'geometry'
2025-08-04 16:56:49,749 - ERROR - 处理要素 91 失败: 'Series' object has no attribute 'geometry'
2025-08-04 16:56:49,749 - ERROR - 处理要素 92 失败: 'Series' object has no attribute 'geometry'
2025-08-04 16:56:49,749 - ERROR - 处理要素 93 失败: 'Series' object has no attribute 'geometry'
2025-08-04 16:56:49,749 - ERROR - 处理要素 94 失败: 'Series' object has no attribute 'geometry'
2025-08-04 16:56:49,749 - ERROR - 处理要素 95 失败: 'Series' object has no attribute 'geometry'
2025-08-04 16:56:49,749 - ERROR - 处理要素 96 失败: 'Series' object has no attribute 'geometry'
2025-08-04 16:56:49,749 - ERROR - 处理要素 97 失败: 'Series' object has no attribute 'geometry'
2025-08-04 16:56:49,749 - ERROR - 处理要素 98 失败: 'Series' object has no attribute 'geometry'
2025-08-04 16:56:49,749 - ERROR - 处理要素 99 失败: 'Series' object has no attribute 'geometry'
2025-08-04 16:56:49,749 - ERROR - 处理要素 100 失败: 'Series' object has no attribute 'geometry'
2025-08-04 16:56:49,749 - ERROR - 处理要素 101 失败: 'Series' object has no attribute 'geometry'
2025-08-04 16:56:49,749 - ERROR - 处理要素 102 失败: 'Series' object has no attribute 'geometry'
2025-08-04 16:56:49,749 - ERROR - 处理要素 103 失败: 'Series' object has no attribute 'geometry'
2025-08-04 16:56:49,749 - ERROR - 处理要素 104 失败: 'Series' object has no attribute 'geometry'
2025-08-04 16:56:49,749 - ERROR - 处理要素 105 失败: 'Series' object has no attribute 'geometry'
2025-08-04 16:56:49,749 - ERROR - 处理要素 106 失败: 'Series' object has no attribute 'geometry'
2025-08-04 16:56:49,749 - ERROR - 处理要素 107 失败: 'Series' object has no attribute 'geometry'
2025-08-04 16:56:49,749 - ERROR - 处理要素 108 失败: 'Series' object has no attribute 'geometry'
2025-08-04 16:56:49,749 - ERROR - 处理要素 109 失败: 'Series' object has no attribute 'geometry'
2025-08-04 16:56:49,749 - ERROR - 处理要素 110 失败: 'Series' object has no attribute 'geometry'
2025-08-04 16:56:49,749 - ERROR - 处理要素 111 失败: 'Series' object has no attribute 'geometry'
2025-08-04 16:56:49,750 - ERROR - 处理要素 112 失败: 'Series' object has no attribute 'geometry'
2025-08-04 16:56:49,750 - ERROR - 处理要素 113 失败: 'Series' object has no attribute 'geometry'
2025-08-04 16:56:49,750 - ERROR - 处理要素 114 失败: 'Series' object has no attribute 'geometry'
2025-08-04 16:56:49,750 - ERROR - 处理要素 115 失败: 'Series' object has no attribute 'geometry'
2025-08-04 16:56:49,750 - ERROR - 处理要素 116 失败: 'Series' object has no attribute 'geometry'
2025-08-04 16:56:49,750 - ERROR - 处理要素 117 失败: 'Series' object has no attribute 'geometry'
2025-08-04 16:56:49,750 - ERROR - 处理要素 118 失败: 'Series' object has no attribute 'geometry'
2025-08-04 16:56:49,750 - ERROR - 处理要素 119 失败: 'Series' object has no attribute 'geometry'
2025-08-04 16:56:49,750 - ERROR - 处理要素 120 失败: 'Series' object has no attribute 'geometry'
2025-08-04 16:56:49,750 - ERROR - 处理要素 121 失败: 'Series' object has no attribute 'geometry'
2025-08-04 16:56:49,750 - ERROR - 处理要素 122 失败: 'Series' object has no attribute 'geometry'
2025-08-04 16:56:49,750 - ERROR - 处理要素 123 失败: 'Series' object has no attribute 'geometry'
2025-08-04 16:56:49,750 - ERROR - 处理要素 124 失败: 'Series' object has no attribute 'geometry'
2025-08-04 16:56:49,750 - ERROR - 处理要素 125 失败: 'Series' object has no attribute 'geometry'
2025-08-04 16:56:49,750 - ERROR - 处理要素 126 失败: 'Series' object has no attribute 'geometry'
2025-08-04 16:56:49,750 - ERROR - 处理要素 127 失败: 'Series' object has no attribute 'geometry'
2025-08-04 16:56:49,750 - ERROR - 处理要素 128 失败: 'Series' object has no attribute 'geometry'
2025-08-04 16:56:49,750 - ERROR - 处理要素 129 失败: 'Series' object has no attribute 'geometry'
2025-08-04 16:56:49,750 - ERROR - 处理要素 130 失败: 'Series' object has no attribute 'geometry'
2025-08-04 16:56:49,750 - ERROR - 处理要素 131 失败: 'Series' object has no attribute 'geometry'
2025-08-04 16:56:49,750 - ERROR - 处理要素 132 失败: 'Series' object has no attribute 'geometry'
2025-08-04 16:56:49,750 - ERROR - 处理要素 133 失败: 'Series' object has no attribute 'geometry'
2025-08-04 16:56:49,750 - ERROR - 处理要素 134 失败: 'Series' object has no attribute 'geometry'
2025-08-04 16:56:49,750 - ERROR - 处理要素 135 失败: 'Series' object has no attribute 'geometry'
2025-08-04 16:56:49,750 - ERROR - 处理要素 136 失败: 'Series' object has no attribute 'geometry'
2025-08-04 16:56:49,750 - ERROR - 处理要素 137 失败: 'Series' object has no attribute 'geometry'
2025-08-04 16:56:49,751 - ERROR - 处理要素 138 失败: 'Series' object has no attribute 'geometry'
2025-08-04 16:56:49,751 - ERROR - 处理要素 139 失败: 'Series' object has no attribute 'geometry'
2025-08-04 16:56:49,751 - ERROR - 处理要素 140 失败: 'Series' object has no attribute 'geometry'
2025-08-04 16:56:49,751 - ERROR - 处理要素 141 失败: 'Series' object has no attribute 'geometry'
2025-08-04 16:56:49,751 - ERROR - 处理要素 142 失败: 'Series' object has no attribute 'geometry'
2025-08-04 16:56:49,751 - ERROR - 处理要素 143 失败: 'Series' object has no attribute 'geometry'
2025-08-04 16:56:49,751 - ERROR - 处理要素 144 失败: 'Series' object has no attribute 'geometry'
2025-08-04 16:56:49,751 - ERROR - 处理要素 145 失败: 'Series' object has no attribute 'geometry'
2025-08-04 16:56:49,751 - ERROR - 处理要素 146 失败: 'Series' object has no attribute 'geometry'
2025-08-04 16:56:49,751 - ERROR - 处理要素 147 失败: 'Series' object has no attribute 'geometry'
2025-08-04 16:56:49,751 - ERROR - 处理要素 148 失败: 'Series' object has no attribute 'geometry'
2025-08-04 16:56:49,751 - ERROR - 处理要素 149 失败: 'Series' object has no attribute 'geometry'
2025-08-04 16:56:49,751 - ERROR - 处理要素 150 失败: 'Series' object has no attribute 'geometry'
2025-08-04 16:56:49,751 - ERROR - 处理要素 151 失败: 'Series' object has no attribute 'geometry'
2025-08-04 16:56:49,752 - ERROR - 处理要素 152 失败: 'Series' object has no attribute 'geometry'
2025-08-04 16:56:49,752 - ERROR - 处理要素 153 失败: 'Series' object has no attribute 'geometry'
2025-08-04 16:56:49,752 - ERROR - 处理要素 154 失败: 'Series' object has no attribute 'geometry'
2025-08-04 16:56:49,752 - ERROR - 处理要素 155 失败: 'Series' object has no attribute 'geometry'
2025-08-04 16:56:49,752 - ERROR - 处理要素 156 失败: 'Series' object has no attribute 'geometry'
2025-08-04 16:56:49,752 - ERROR - 处理要素 157 失败: 'Series' object has no attribute 'geometry'
2025-08-04 16:56:49,752 - ERROR - 处理要素 158 失败: 'Series' object has no attribute 'geometry'
2025-08-04 16:56:49,752 - ERROR - 处理要素 159 失败: 'Series' object has no attribute 'geometry'
2025-08-04 16:56:49,752 - ERROR - 处理要素 160 失败: 'Series' object has no attribute 'geometry'
2025-08-04 16:56:49,752 - ERROR - 处理要素 161 失败: 'Series' object has no attribute 'geometry'
2025-08-04 16:56:49,752 - ERROR - 处理要素 162 失败: 'Series' object has no attribute 'geometry'
2025-08-04 16:56:49,752 - ERROR - 处理要素 163 失败: 'Series' object has no attribute 'geometry'
2025-08-04 16:56:49,752 - ERROR - 处理要素 164 失败: 'Series' object has no attribute 'geometry'
2025-08-04 16:56:49,752 - ERROR - 处理要素 165 失败: 'Series' object has no attribute 'geometry'
2025-08-04 16:56:49,752 - ERROR - 处理要素 166 失败: 'Series' object has no attribute 'geometry'
2025-08-04 16:56:49,752 - ERROR - 处理要素 167 失败: 'Series' object has no attribute 'geometry'
2025-08-04 16:56:49,752 - ERROR - 处理要素 168 失败: 'Series' object has no attribute 'geometry'
2025-08-04 16:56:49,752 - ERROR - 处理要素 169 失败: 'Series' object has no attribute 'geometry'
2025-08-04 16:56:49,753 - ERROR - 处理要素 170 失败: 'Series' object has no attribute 'geometry'
2025-08-04 16:56:49,753 - ERROR - 处理要素 171 失败: 'Series' object has no attribute 'geometry'
2025-08-04 16:56:49,753 - ERROR - 处理要素 172 失败: 'Series' object has no attribute 'geometry'
2025-08-04 16:56:49,753 - ERROR - 处理要素 173 失败: 'Series' object has no attribute 'geometry'
2025-08-04 16:56:49,753 - ERROR - 处理要素 174 失败: 'Series' object has no attribute 'geometry'
2025-08-04 16:56:49,753 - ERROR - 处理要素 175 失败: 'Series' object has no attribute 'geometry'
2025-08-04 16:56:49,753 - ERROR - 处理要素 176 失败: 'Series' object has no attribute 'geometry'
2025-08-04 16:56:49,753 - ERROR - 处理要素 177 失败: 'Series' object has no attribute 'geometry'
2025-08-04 16:56:49,753 - ERROR - 处理要素 178 失败: 'Series' object has no attribute 'geometry'
2025-08-04 16:56:49,753 - ERROR - 处理要素 179 失败: 'Series' object has no attribute 'geometry'
2025-08-04 16:56:49,753 - ERROR - 处理要素 180 失败: 'Series' object has no attribute 'geometry'
2025-08-04 16:56:49,753 - ERROR - 处理要素 181 失败: 'Series' object has no attribute 'geometry'
2025-08-04 16:56:49,753 - ERROR - 处理要素 182 失败: 'Series' object has no attribute 'geometry'
2025-08-04 16:56:49,753 - ERROR - 处理要素 183 失败: 'Series' object has no attribute 'geometry'
2025-08-04 16:56:49,753 - ERROR - 处理要素 184 失败: 'Series' object has no attribute 'geometry'
2025-08-04 16:56:49,753 - ERROR - 处理要素 185 失败: 'Series' object has no attribute 'geometry'
2025-08-04 16:56:49,753 - ERROR - 处理要素 186 失败: 'Series' object has no attribute 'geometry'
2025-08-04 16:56:49,753 - ERROR - 处理要素 187 失败: 'Series' object has no attribute 'geometry'
2025-08-04 16:56:49,753 - ERROR - 处理要素 188 失败: 'Series' object has no attribute 'geometry'
2025-08-04 16:56:49,754 - ERROR - 处理要素 189 失败: 'Series' object has no attribute 'geometry'
2025-08-04 16:56:49,754 - ERROR - 处理要素 190 失败: 'Series' object has no attribute 'geometry'
2025-08-04 16:56:49,754 - ERROR - 处理要素 191 失败: 'Series' object has no attribute 'geometry'
2025-08-04 16:56:49,754 - ERROR - 处理要素 192 失败: 'Series' object has no attribute 'geometry'
2025-08-04 16:56:49,754 - ERROR - 处理要素 193 失败: 'Series' object has no attribute 'geometry'
2025-08-04 16:56:49,754 - ERROR - 处理要素 194 失败: 'Series' object has no attribute 'geometry'
2025-08-04 16:56:49,754 - ERROR - 处理要素 195 失败: 'Series' object has no attribute 'geometry'
2025-08-04 16:56:49,754 - ERROR - 处理要素 196 失败: 'Series' object has no attribute 'geometry'
2025-08-04 16:56:49,754 - ERROR - 处理要素 197 失败: 'Series' object has no attribute 'geometry'
2025-08-04 16:56:49,754 - ERROR - 处理要素 198 失败: 'Series' object has no attribute 'geometry'
2025-08-04 16:56:49,754 - ERROR - 处理要素 199 失败: 'Series' object has no attribute 'geometry'
2025-08-04 16:56:49,754 - ERROR - 处理要素 200 失败: 'Series' object has no attribute 'geometry'
2025-08-04 16:56:49,754 - ERROR - 处理要素 201 失败: 'Series' object has no attribute 'geometry'
2025-08-04 16:56:49,754 - ERROR - 处理要素 202 失败: 'Series' object has no attribute 'geometry'
2025-08-04 16:56:49,754 - ERROR - 处理要素 203 失败: 'Series' object has no attribute 'geometry'
2025-08-04 16:56:49,754 - ERROR - 处理要素 204 失败: 'Series' object has no attribute 'geometry'
2025-08-04 16:56:49,754 - ERROR - 处理要素 205 失败: 'Series' object has no attribute 'geometry'
2025-08-04 16:56:49,754 - ERROR - 处理要素 206 失败: 'Series' object has no attribute 'geometry'
2025-08-04 16:56:49,754 - ERROR - 处理要素 207 失败: 'Series' object has no attribute 'geometry'
2025-08-04 16:56:49,754 - ERROR - 处理要素 208 失败: 'Series' object has no attribute 'geometry'
2025-08-04 16:56:49,754 - ERROR - 处理要素 209 失败: 'Series' object has no attribute 'geometry'
2025-08-04 16:56:49,754 - ERROR - 处理要素 210 失败: 'Series' object has no attribute 'geometry'
2025-08-04 16:56:49,754 - ERROR - 处理要素 211 失败: 'Series' object has no attribute 'geometry'
2025-08-04 16:56:49,754 - ERROR - 处理要素 212 失败: 'Series' object has no attribute 'geometry'
2025-08-04 16:56:49,754 - ERROR - 处理要素 213 失败: 'Series' object has no attribute 'geometry'
2025-08-04 16:56:49,754 - ERROR - 处理要素 214 失败: 'Series' object has no attribute 'geometry'
2025-08-04 16:56:49,754 - ERROR - 处理要素 215 失败: 'Series' object has no attribute 'geometry'
2025-08-04 16:56:49,754 - ERROR - 处理要素 216 失败: 'Series' object has no attribute 'geometry'
2025-08-04 16:56:49,754 - ERROR - 处理要素 217 失败: 'Series' object has no attribute 'geometry'
2025-08-04 16:56:49,754 - ERROR - 处理要素 218 失败: 'Series' object has no attribute 'geometry'
2025-08-04 16:56:49,754 - ERROR - 处理要素 219 失败: 'Series' object has no attribute 'geometry'
2025-08-04 16:56:49,755 - ERROR - 处理要素 220 失败: 'Series' object has no attribute 'geometry'
2025-08-04 16:56:49,755 - ERROR - 处理要素 221 失败: 'Series' object has no attribute 'geometry'
2025-08-04 16:56:49,755 - ERROR - 处理要素 222 失败: 'Series' object has no attribute 'geometry'
2025-08-04 16:56:49,755 - ERROR - 处理要素 223 失败: 'Series' object has no attribute 'geometry'
2025-08-04 16:56:49,755 - ERROR - 处理要素 224 失败: 'Series' object has no attribute 'geometry'
2025-08-04 16:56:49,755 - ERROR - 处理要素 225 失败: 'Series' object has no attribute 'geometry'
2025-08-04 16:56:49,755 - ERROR - 处理要素 226 失败: 'Series' object has no attribute 'geometry'
2025-08-04 16:56:49,755 - ERROR - 处理要素 227 失败: 'Series' object has no attribute 'geometry'
2025-08-04 16:56:49,755 - ERROR - 处理要素 228 失败: 'Series' object has no attribute 'geometry'
2025-08-04 16:56:49,755 - ERROR - 处理要素 229 失败: 'Series' object has no attribute 'geometry'
2025-08-04 16:56:49,755 - ERROR - 处理要素 230 失败: 'Series' object has no attribute 'geometry'
2025-08-04 16:56:49,755 - ERROR - 处理要素 231 失败: 'Series' object has no attribute 'geometry'
2025-08-04 16:56:49,755 - ERROR - 处理要素 232 失败: 'Series' object has no attribute 'geometry'
2025-08-04 16:56:49,755 - ERROR - 处理要素 233 失败: 'Series' object has no attribute 'geometry'
2025-08-04 16:56:49,755 - ERROR - 处理要素 234 失败: 'Series' object has no attribute 'geometry'
2025-08-04 16:56:49,755 - ERROR - 处理要素 235 失败: 'Series' object has no attribute 'geometry'
2025-08-04 16:56:49,755 - ERROR - 处理要素 236 失败: 'Series' object has no attribute 'geometry'
2025-08-04 16:56:49,755 - ERROR - 处理要素 237 失败: 'Series' object has no attribute 'geometry'
2025-08-04 16:56:49,755 - ERROR - 处理要素 238 失败: 'Series' object has no attribute 'geometry'
2025-08-04 16:56:49,755 - ERROR - 处理要素 239 失败: 'Series' object has no attribute 'geometry'
2025-08-04 16:56:49,755 - ERROR - 处理要素 240 失败: 'Series' object has no attribute 'geometry'
2025-08-04 16:56:49,755 - ERROR - 处理要素 241 失败: 'Series' object has no attribute 'geometry'
2025-08-04 16:56:49,755 - ERROR - 处理要素 242 失败: 'Series' object has no attribute 'geometry'
2025-08-04 16:56:49,755 - ERROR - 处理要素 243 失败: 'Series' object has no attribute 'geometry'
2025-08-04 16:56:49,755 - ERROR - 处理要素 244 失败: 'Series' object has no attribute 'geometry'
2025-08-04 16:56:49,755 - ERROR - 处理要素 245 失败: 'Series' object has no attribute 'geometry'
2025-08-04 16:56:49,755 - ERROR - 处理要素 246 失败: 'Series' object has no attribute 'geometry'
2025-08-04 16:56:49,755 - ERROR - 处理要素 247 失败: 'Series' object has no attribute 'geometry'
2025-08-04 16:56:49,755 - ERROR - 处理要素 248 失败: 'Series' object has no attribute 'geometry'
2025-08-04 16:56:49,755 - ERROR - 处理要素 249 失败: 'Series' object has no attribute 'geometry'
2025-08-04 16:56:49,755 - ERROR - 处理要素 250 失败: 'Series' object has no attribute 'geometry'
2025-08-04 16:56:49,755 - ERROR - 处理要素 251 失败: 'Series' object has no attribute 'geometry'
2025-08-04 16:56:49,755 - ERROR - 处理要素 252 失败: 'Series' object has no attribute 'geometry'
2025-08-04 16:56:49,755 - ERROR - 处理要素 253 失败: 'Series' object has no attribute 'geometry'
2025-08-04 16:56:49,755 - ERROR - 处理要素 254 失败: 'Series' object has no attribute 'geometry'
2025-08-04 16:56:49,755 - ERROR - 处理要素 255 失败: 'Series' object has no attribute 'geometry'
2025-08-04 16:56:49,755 - ERROR - 处理要素 256 失败: 'Series' object has no attribute 'geometry'
2025-08-04 16:56:49,756 - ERROR - 处理要素 257 失败: 'Series' object has no attribute 'geometry'
2025-08-04 16:56:49,756 - ERROR - 处理要素 258 失败: 'Series' object has no attribute 'geometry'
2025-08-04 16:56:49,756 - ERROR - 处理要素 259 失败: 'Series' object has no attribute 'geometry'
2025-08-04 16:56:49,756 - ERROR - 处理要素 260 失败: 'Series' object has no attribute 'geometry'
2025-08-04 16:56:49,756 - ERROR - 处理要素 261 失败: 'Series' object has no attribute 'geometry'
2025-08-04 16:56:49,756 - ERROR - 处理要素 262 失败: 'Series' object has no attribute 'geometry'
2025-08-04 16:56:49,756 - ERROR - 处理要素 263 失败: 'Series' object has no attribute 'geometry'
2025-08-04 16:56:49,756 - ERROR - 处理要素 264 失败: 'Series' object has no attribute 'geometry'
2025-08-04 16:56:49,756 - ERROR - 处理要素 265 失败: 'Series' object has no attribute 'geometry'
2025-08-04 16:56:49,756 - ERROR - 处理要素 266 失败: 'Series' object has no attribute 'geometry'
2025-08-04 16:56:49,756 - ERROR - 处理要素 267 失败: 'Series' object has no attribute 'geometry'
2025-08-04 16:56:49,756 - ERROR - 处理要素 268 失败: 'Series' object has no attribute 'geometry'
2025-08-04 16:56:49,756 - ERROR - 处理要素 269 失败: 'Series' object has no attribute 'geometry'
2025-08-04 16:56:49,756 - ERROR - 处理要素 270 失败: 'Series' object has no attribute 'geometry'
2025-08-04 16:56:49,756 - ERROR - 处理要素 271 失败: 'Series' object has no attribute 'geometry'
2025-08-04 16:56:49,756 - ERROR - 处理要素 272 失败: 'Series' object has no attribute 'geometry'
2025-08-04 16:56:49,756 - ERROR - 处理要素 273 失败: 'Series' object has no attribute 'geometry'
2025-08-04 16:56:49,756 - ERROR - 处理要素 274 失败: 'Series' object has no attribute 'geometry'
2025-08-04 16:56:49,756 - ERROR - 处理要素 275 失败: 'Series' object has no attribute 'geometry'
2025-08-04 16:56:49,756 - ERROR - 处理要素 276 失败: 'Series' object has no attribute 'geometry'
2025-08-04 16:56:49,756 - ERROR - 处理要素 277 失败: 'Series' object has no attribute 'geometry'
2025-08-04 16:56:49,756 - ERROR - 处理要素 278 失败: 'Series' object has no attribute 'geometry'
2025-08-04 16:56:49,756 - ERROR - 处理要素 279 失败: 'Series' object has no attribute 'geometry'
2025-08-04 16:56:49,756 - ERROR - 处理要素 280 失败: 'Series' object has no attribute 'geometry'
2025-08-04 16:56:49,756 - ERROR - 处理要素 281 失败: 'Series' object has no attribute 'geometry'
2025-08-04 16:56:49,756 - ERROR - 处理要素 282 失败: 'Series' object has no attribute 'geometry'
2025-08-04 16:56:49,756 - ERROR - 处理要素 283 失败: 'Series' object has no attribute 'geometry'
2025-08-04 16:56:49,756 - ERROR - 处理要素 284 失败: 'Series' object has no attribute 'geometry'
2025-08-04 16:56:49,756 - ERROR - 处理要素 285 失败: 'Series' object has no attribute 'geometry'
2025-08-04 16:56:49,756 - ERROR - 处理要素 286 失败: 'Series' object has no attribute 'geometry'
2025-08-04 16:56:49,756 - ERROR - 处理要素 287 失败: 'Series' object has no attribute 'geometry'
2025-08-04 16:56:49,756 - ERROR - 处理要素 288 失败: 'Series' object has no attribute 'geometry'
2025-08-04 16:56:49,756 - ERROR - 处理要素 289 失败: 'Series' object has no attribute 'geometry'
2025-08-04 16:56:49,756 - ERROR - 处理要素 290 失败: 'Series' object has no attribute 'geometry'
2025-08-04 16:56:49,756 - ERROR - 处理要素 291 失败: 'Series' object has no attribute 'geometry'
2025-08-04 16:56:49,756 - ERROR - 处理要素 292 失败: 'Series' object has no attribute 'geometry'
2025-08-04 16:56:49,756 - ERROR - 处理要素 293 失败: 'Series' object has no attribute 'geometry'
2025-08-04 16:56:49,756 - ERROR - 处理要素 294 失败: 'Series' object has no attribute 'geometry'
2025-08-04 16:56:49,756 - ERROR - 处理要素 295 失败: 'Series' object has no attribute 'geometry'
2025-08-04 16:56:49,756 - ERROR - 处理要素 296 失败: 'Series' object has no attribute 'geometry'
2025-08-04 16:56:49,756 - ERROR - 处理要素 297 失败: 'Series' object has no attribute 'geometry'
2025-08-04 16:56:49,756 - ERROR - 处理要素 298 失败: 'Series' object has no attribute 'geometry'
2025-08-04 16:56:49,756 - ERROR - 处理要素 299 失败: 'Series' object has no attribute 'geometry'
2025-08-04 16:56:49,756 - ERROR - 处理要素 300 失败: 'Series' object has no attribute 'geometry'
2025-08-04 16:56:49,756 - ERROR - 处理要素 301 失败: 'Series' object has no attribute 'geometry'
2025-08-04 16:56:49,757 - ERROR - 处理要素 302 失败: 'Series' object has no attribute 'geometry'
2025-08-04 16:56:49,757 - ERROR - 处理要素 303 失败: 'Series' object has no attribute 'geometry'
2025-08-04 16:56:49,757 - ERROR - 处理要素 304 失败: 'Series' object has no attribute 'geometry'
2025-08-04 16:56:49,757 - ERROR - 处理要素 305 失败: 'Series' object has no attribute 'geometry'
2025-08-04 16:56:49,757 - ERROR - 处理要素 306 失败: 'Series' object has no attribute 'geometry'
2025-08-04 16:56:49,757 - ERROR - 处理要素 307 失败: 'Series' object has no attribute 'geometry'
2025-08-04 16:56:49,757 - ERROR - 处理要素 308 失败: 'Series' object has no attribute 'geometry'
2025-08-04 16:56:49,757 - ERROR - 处理要素 309 失败: 'Series' object has no attribute 'geometry'
2025-08-04 16:56:49,757 - ERROR - 处理要素 310 失败: 'Series' object has no attribute 'geometry'
2025-08-04 16:56:49,757 - ERROR - 处理要素 311 失败: 'Series' object has no attribute 'geometry'
2025-08-04 16:56:49,757 - ERROR - 处理要素 312 失败: 'Series' object has no attribute 'geometry'
2025-08-04 16:56:49,757 - ERROR - 处理要素 313 失败: 'Series' object has no attribute 'geometry'
2025-08-04 16:56:49,757 - ERROR - 处理要素 314 失败: 'Series' object has no attribute 'geometry'
2025-08-04 16:56:49,757 - ERROR - 处理要素 315 失败: 'Series' object has no attribute 'geometry'
2025-08-04 16:56:49,757 - ERROR - 处理要素 316 失败: 'Series' object has no attribute 'geometry'
2025-08-04 16:56:49,757 - ERROR - 处理要素 317 失败: 'Series' object has no attribute 'geometry'
2025-08-04 16:56:49,757 - ERROR - 处理要素 318 失败: 'Series' object has no attribute 'geometry'
2025-08-04 16:56:49,757 - ERROR - 处理要素 319 失败: 'Series' object has no attribute 'geometry'
2025-08-04 16:56:49,757 - ERROR - 处理要素 320 失败: 'Series' object has no attribute 'geometry'
2025-08-04 16:56:49,757 - ERROR - 处理要素 321 失败: 'Series' object has no attribute 'geometry'
2025-08-04 16:56:49,757 - ERROR - 处理要素 322 失败: 'Series' object has no attribute 'geometry'
2025-08-04 16:56:49,757 - ERROR - 处理要素 323 失败: 'Series' object has no attribute 'geometry'
2025-08-04 16:56:49,757 - ERROR - 处理要素 324 失败: 'Series' object has no attribute 'geometry'
2025-08-04 16:56:49,757 - ERROR - 处理要素 325 失败: 'Series' object has no attribute 'geometry'
2025-08-04 16:56:49,757 - ERROR - 处理要素 326 失败: 'Series' object has no attribute 'geometry'
2025-08-04 16:56:49,757 - INFO - 图层 SSCMYDBJGH_ExportTable3 导入完成，共导入 0 个要素
2025-08-04 16:56:49,757 - INFO - ✓ 图层 'SSCMYDBJGH_ExportTable3' 成功导入到表 '510903xx_sscmydbjgh_exporttable3'
2025-08-04 16:56:49,757 - INFO - 导入完成: 9/9 个图层成功
2025-08-04 16:56:49,757 - INFO - 创建的表: ['510903xx_featuretoline', '510903xx_featuretoline', '510903xx_featuretoline1', '510903xx_featuretoline2', '510903xx_polyli_featuretoline', '510903xx_shorelinecontr_featuretopoly', '510903xx_shorelinecontr_featuretopoly1', '510903xx_sscmydbjgh_exporttable', '510903xx_sscmydbjgh_exporttable3']
2025-08-04 17:16:30,818 - INFO - 正在读取GDB文件: /Users/<USER>/Downloads/MyProject.gdb
2025-08-04 17:16:31,459 - INFO - 数据库连接成功
2025-08-04 17:16:31,503 - INFO - GDAL signalled an error: err_no=1, msg='PROJ: internal_proj_identify: /Users/<USER>/miniconda3/share/proj/proj.db contains DATABASE.LAYOUT.VERSION.MINOR = 2 whereas a number >= 3 is expected. It comes from another PROJ installation.'
2025-08-04 17:16:31,555 - INFO - GDAL signalled an error: err_no=1, msg='PROJ: internal_proj_identify: /Users/<USER>/miniconda3/share/proj/proj.db contains DATABASE.LAYOUT.VERSION.MINOR = 2 whereas a number >= 3 is expected. It comes from another PROJ installation.'
2025-08-04 17:16:31,576 - INFO - GDAL signalled an error: err_no=1, msg='PROJ: internal_proj_identify: /Users/<USER>/miniconda3/share/proj/proj.db contains DATABASE.LAYOUT.VERSION.MINOR = 2 whereas a number >= 3 is expected. It comes from another PROJ installation.'
2025-08-04 17:16:31,588 - INFO - 发现 9 个图层: ['县级行政区_FeatureToLine', '市级行政区_FeatureToLine', '县级行政区_FeatureToLine1', '县级行政区_FeatureToLine2', '经开区管辖范围线Polyli_FeatureToLine', 'ShorelineContr_FeatureToPoly', 'ShorelineContr_FeatureToPoly1', 'SSCMYDBJGH_ExportTable', 'SSCMYDBJGH_ExportTable3']
2025-08-04 17:16:31,589 - INFO - 发现 9 个图层:
2025-08-04 17:17:01,037 - INFO - 开始GIS数据导入...
2025-08-04 17:17:01,037 - INFO - GDB路径: /Users/<USER>/Downloads/MyProject.gdb
2025-08-04 17:17:01,037 - INFO - 表前缀: 510903xx
2025-08-04 17:17:01,037 - INFO - 指定图层: ['SSCMYDBJGH_ExportTable', 'SSCMYDBJGH_ExportTable3']
2025-08-04 17:17:01,037 - INFO - 导入模式: 分表
2025-08-04 17:17:01,037 - INFO - 批量大小: 1000
2025-08-04 17:17:01,934 - INFO - 数据库连接成功
2025-08-04 17:17:01,978 - INFO - GDAL signalled an error: err_no=1, msg='PROJ: internal_proj_identify: /Users/<USER>/miniconda3/share/proj/proj.db contains DATABASE.LAYOUT.VERSION.MINOR = 2 whereas a number >= 3 is expected. It comes from another PROJ installation.'
2025-08-04 17:17:02,029 - INFO - GDAL signalled an error: err_no=1, msg='PROJ: internal_proj_identify: /Users/<USER>/miniconda3/share/proj/proj.db contains DATABASE.LAYOUT.VERSION.MINOR = 2 whereas a number >= 3 is expected. It comes from another PROJ installation.'
2025-08-04 17:17:02,050 - INFO - GDAL signalled an error: err_no=1, msg='PROJ: internal_proj_identify: /Users/<USER>/miniconda3/share/proj/proj.db contains DATABASE.LAYOUT.VERSION.MINOR = 2 whereas a number >= 3 is expected. It comes from another PROJ installation.'
2025-08-04 17:17:02,064 - INFO - 发现 9 个图层: ['县级行政区_FeatureToLine', '市级行政区_FeatureToLine', '县级行政区_FeatureToLine1', '县级行政区_FeatureToLine2', '经开区管辖范围线Polyli_FeatureToLine', 'ShorelineContr_FeatureToPoly', 'ShorelineContr_FeatureToPoly1', 'SSCMYDBJGH_ExportTable', 'SSCMYDBJGH_ExportTable3']
2025-08-04 17:17:02,064 - INFO - GDB中可用图层: ['县级行政区_FeatureToLine', '市级行政区_FeatureToLine', '县级行政区_FeatureToLine1', '县级行政区_FeatureToLine2', '经开区管辖范围线Polyli_FeatureToLine', 'ShorelineContr_FeatureToPoly', 'ShorelineContr_FeatureToPoly1', 'SSCMYDBJGH_ExportTable', 'SSCMYDBJGH_ExportTable3']
2025-08-04 17:17:02,064 - INFO - ✓ 图层 'SSCMYDBJGH_ExportTable' 已加入导入列表
2025-08-04 17:17:02,064 - INFO - ✓ 图层 'SSCMYDBJGH_ExportTable3' 已加入导入列表
2025-08-04 17:17:02,064 - INFO - 准备导入 2 个图层: ['SSCMYDBJGH_ExportTable', 'SSCMYDBJGH_ExportTable3']
2025-08-04 17:17:02,064 - INFO - 
==================================================
2025-08-04 17:17:02,064 - INFO - 正在处理图层: SSCMYDBJGH_ExportTable
2025-08-04 17:17:02,064 - INFO - ==================================================
2025-08-04 17:17:02,064 - INFO - 目标表名: 510903xx_sscmydbjgh_exporttable
2025-08-04 17:17:02,165 - INFO - 图层 SSCMYDBJGH_ExportTable 字段分析完成: {'name': 'VARCHAR(255)', 'BSM': 'VARCHAR(255)', 'YSDM': 'VARCHAR(255)', 'XZQDM': 'VARCHAR(255)', 'XZQMC': 'VARCHAR(255)', 'XXGHBZDYBH': 'VARCHAR(255)', 'XXGHBZDYMC': 'VARCHAR(255)', 'DKBH': 'VARCHAR(255)', 'CSLXKZMJ': 'DECIMAL(15,6)', 'CSLVXKZMJ': 'DECIMAL(15,6)', 'CSHXKZMJ': 'DECIMAL(10,4)', 'CSZXKZMJ': 'DECIMAL(15,6)', 'YDYHFLDM': 'VARCHAR(255)', 'YDYHFLMC': 'VARCHAR(255)', 'YDMJ': 'DECIMAL(10,4)', 'DXKJGH': 'VARCHAR(255)', 'BZ': 'VARCHAR(255)', 'Shape_Length': 'DECIMAL(18,6)', 'Shape_Area': 'DECIMAL(20,6)', 'JQBH': 'VARCHAR(255)', 'JQMC': 'VARCHAR(255)', 'ZJZMJ': 'DECIMAL(10,4)', 'RJL': 'VARCHAR(255)', 'JZXG': 'VARCHAR(255)', 'LVDL': 'VARCHAR(255)', 'JZMD': 'VARCHAR(255)', 'ZZNJLZLKZL': 'VARCHAR(255)', 'XCSLDL': 'VARCHAR(255)', 'TSPZL': 'VARCHAR(255)', 'LSWDL': 'VARCHAR(255)', 'TCWPB': 'VARCHAR(255)', 'YDKBH': 'VARCHAR(255)', 'JDCCRK': 'VARCHAR(255)', 'JZKK': 'VARCHAR(255)', 'TJSFYCY': 'VARCHAR(255)', 'QTPTSFYCY': 'VARCHAR(255)', 'OBJECTID': 'TINYINT', 'Join_Count': 'TINYINT', 'TARGET_FID': 'SMALLINT', 'BSM_1': 'VARCHAR(255)', 'Shape_Length_1': 'DECIMAL(18,6)', 'Shape_Area_1': 'DECIMAL(20,6)'}
2025-08-04 17:17:02,286 - INFO - 表 510903xx_sscmydbjgh_exporttable 创建成功
2025-08-04 17:17:02,286 - INFO - 开始读取图层: SSCMYDBJGH_ExportTable
2025-08-04 17:17:02,298 - INFO - 图层 SSCMYDBJGH_ExportTable 包含 783 个要素
2025-08-04 17:17:02,299 - INFO - 图层字段: ['name', 'BSM', 'YSDM', 'XZQDM', 'XZQMC', 'XXGHBZDYBH', 'XXGHBZDYMC', 'DKBH', 'CSLXKZMJ', 'CSLVXKZMJ', 'CSHXKZMJ', 'CSZXKZMJ', 'YDYHFLDM', 'YDYHFLMC', 'YDMJ', 'DXKJGH', 'BZ', 'Shape_Length', 'Shape_Area', 'JQBH', 'JQMC', 'ZJZMJ', 'RJL', 'JZXG', 'LVDL', 'JZMD', 'ZZNJLZLKZL', 'XCSLDL', 'TSPZL', 'LSWDL', 'TCWPB', 'YDKBH', 'JDCCRK', 'JZKK', 'TJSFYCY', 'QTPTSFYCY', 'OBJECTID', 'Join_Count', 'TARGET_FID', 'BSM_1', 'Shape_Length_1', 'Shape_Area_1']
2025-08-04 17:17:08,587 - ERROR - 批量插入失败: (1264, "Out of range value for column 'objectid' at row 128")
2025-08-04 17:17:08,588 - ERROR - SQL: INSERT INTO `510903xx_sscmydbjgh_exporttable` (`id`, `layer_name`, `geometry_type`, `geometry_wkt`, `geometry_wkb`, `lng`, `lat`, `area`, `perimeter`, `source_file`, `import_time`, `del_flag`, `create_by`, `create_time`, `update_by`, `update_time`, `remark`, `name`, `bsm`, `ysdm`, `xzqdm`, `xzqmc`, `xxghbzdybh`, `xxghbzdymc`, `dkbh`, `cslxkzmj`, `cslvxkzmj`, `cshxkzmj`, `cszxkzmj`, `ydyhfldm`, `ydyhflmc`, `ydmj`, `dxkjgh`, `bz`, `shape_length`, `shape_area`, `jqbh`, `jqmc`, `zjzmj`, `rjl`, `jzxg`, `lvdl`, `jzmd`, `zznjlzlkzl`, `xcsldl`, `tspzl`, `lswdl`, `tcwpb`, `ydkbh`, `jdccrk`, `jzkk`, `tjsfycy`, `qtptsfycy`, `objectid`, `join_count`, `target_fid`, `bsm_1`, `shape_length_1`, `shape_area_1`) VALUES (%s, %s, %s, %s, %s, %s, %s, %s, %s, %s, %s, %s, %s, %s, %s, %s, %s, %s, %s, %s, %s, %s, %s, %s, %s, %s, %s, %s, %s, %s, %s, %s, %s, %s, %s, %s, %s, %s, %s, %s, %s, %s, %s, %s, %s, %s, %s, %s, %s, %s, %s, %s, %s, %s, %s, %s, %s, %s, %s)
2025-08-04 17:17:08,588 - ERROR - 记录样本: {'id': '33b196a59fdc4a11927bdc7e6fb12d93', 'layer_name': 'SSCMYDBJGH_ExportTable', 'geometry_type': None, 'geometry_wkt': None, 'geometry_wkb': None, 'lng': None, 'lat': None, 'area': None, 'perimeter': None, 'source_file': '/Users/<USER>/Downloads/MyProject.gdb', 'import_time': '2025-08-04 17:17:02', 'del_flag': '0', 'create_by': 'system', 'create_time': '2025-08-04 17:17:02', 'update_by': 'system', 'update_time': '2025-08-04 17:17:02', 'remark': '从MyProject.gdb导入', 'name': '0903娱乐用地', 'bsm': None, 'ysdm': '2090020501', 'xzqdm': '510900', 'xzqmc': '遂宁市', 'xxghbzdybh': '51090000000HD03', 'xxghbzdymc': 'HD03', 'dkbh': 'HD03-03-07', 'cslxkzmj': None, 'cslvxkzmj': None, 'cshxkzmj': None, 'cszxkzmj': None, 'ydyhfldm': '0903', 'ydyhflmc': '娱乐用地', 'ydmj': 24757.83984375, 'dxkjgh': '一般开发区域', 'bz': '已批规划', 'shape_length': 616.1477884887105, 'shape_area': 24759.717620558873, 'jqbh': 'HD03-03', 'jqmc': 'HD03-03', 'zjzmj': 19806.271484375, 'rjl': '0.8', 'jzxg': '24', 'lvdl': '30', 'jzmd': '50', 'zznjlzlkzl': '80', 'xcsldl': '40', 'tspzl': '60', 'lswdl': '50', 'tcwpb': '-', 'ydkbh': 'HD03-03-05', 'jdccrk': '', 'jzkk': '', 'tjsfycy': '', 'qtptsfycy': '原设计条件未配公厕，\n原设计条件标注用途剧院会议', 'objectid': 1, 'join_count': 3, 'target_fid': 1014, 'bsm_1': '510900000000003018', 'shape_length_1': 616.1477884887105, 'shape_area_1': 24759.717620558873}
2025-08-04 17:17:08,651 - ERROR - 导入图层 SSCMYDBJGH_ExportTable 失败: (1264, "Out of range value for column 'objectid' at row 128")
2025-08-04 17:17:08,653 - ERROR - ✗ 图层 'SSCMYDBJGH_ExportTable' 导入失败
2025-08-04 17:17:08,653 - INFO - 
==================================================
2025-08-04 17:17:08,653 - INFO - 正在处理图层: SSCMYDBJGH_ExportTable3
2025-08-04 17:17:08,653 - INFO - ==================================================
2025-08-04 17:17:08,653 - INFO - 目标表名: 510903xx_sscmydbjgh_exporttable3
2025-08-04 17:17:08,660 - INFO - 图层 SSCMYDBJGH_ExportTable3 字段分析完成: {'BSM': 'VARCHAR(255)', 'YSDM': 'VARCHAR(255)', 'XZQDM': 'VARCHAR(255)', 'XZQMC': 'VARCHAR(255)', 'DKBH': 'VARCHAR(255)', 'CSLXKZMJ': 'DECIMAL(15,6)', 'CSLVXKZMJ': 'DECIMAL(15,6)', 'CSHXKZMJ': 'DECIMAL(10,4)', 'CSZXKZMJ': 'DECIMAL(10,4)', 'YDMJ': 'DECIMAL(10,4)', 'DXKJGH': 'VARCHAR(255)', 'BZ': 'VARCHAR(255)', 'DYBH': 'VARCHAR(255)', 'DYMC': 'VARCHAR(255)', 'JQBH': 'VARCHAR(255)', 'JQMC': 'VARCHAR(255)', 'YDDM': 'VARCHAR(255)', 'YDMC': 'VARCHAR(255)', 'HHYD': 'VARCHAR(255)', 'ZJZMJ': 'DECIMAL(10,4)', 'SHAPE_Length': 'DECIMAL(18,6)', 'SHAPE_Area': 'DECIMAL(20,6)', 'RJLSX': 'DECIMAL(10,1)', 'JZMDSX': 'DECIMAL(10,1)', 'JZXGSX': 'DECIMAL(10,1)', 'LDLXX': 'DECIMAL(10,1)', 'NJLZL': 'DECIMAL(10,1)', 'PTSS': 'VARCHAR(255)', 'QTKZ': 'VARCHAR(258)', 'TCBWPB': 'VARCHAR(255)', 'OBJECTID': 'TINYINT', 'Join_Count': 'TINYINT', 'TARGET_FID': 'TINYINT', 'BSM_1': 'VARCHAR(255)', 'SHAPE_Length_1': 'DECIMAL(18,6)', 'SHAPE_Area_1': 'DECIMAL(20,6)'}
2025-08-04 17:17:09,827 - INFO - 表 510903xx_sscmydbjgh_exporttable3 创建成功
2025-08-04 17:17:09,827 - INFO - 开始读取图层: SSCMYDBJGH_ExportTable3
2025-08-04 17:17:09,836 - INFO - 图层 SSCMYDBJGH_ExportTable3 包含 327 个要素
2025-08-04 17:17:09,836 - INFO - 图层字段: ['BSM', 'YSDM', 'XZQDM', 'XZQMC', 'DKBH', 'CSLXKZMJ', 'CSLVXKZMJ', 'CSHXKZMJ', 'CSZXKZMJ', 'YDMJ', 'DXKJGH', 'BZ', 'DYBH', 'DYMC', 'JQBH', 'JQMC', 'YDDM', 'YDMC', 'HHYD', 'ZJZMJ', 'SHAPE_Length', 'SHAPE_Area', 'RJLSX', 'JZMDSX', 'JZXGSX', 'LDLXX', 'NJLZL', 'PTSS', 'QTKZ', 'TCBWPB', 'OBJECTID', 'Join_Count', 'TARGET_FID', 'BSM_1', 'SHAPE_Length_1', 'SHAPE_Area_1']
2025-08-04 17:17:13,190 - ERROR - 批量插入失败: (1264, "Out of range value for column 'objectid' at row 128")
2025-08-04 17:17:13,190 - ERROR - SQL: INSERT INTO `510903xx_sscmydbjgh_exporttable3` (`id`, `layer_name`, `geometry_type`, `geometry_wkt`, `geometry_wkb`, `lng`, `lat`, `area`, `perimeter`, `source_file`, `import_time`, `del_flag`, `create_by`, `create_time`, `update_by`, `update_time`, `remark`, `bsm`, `ysdm`, `xzqdm`, `xzqmc`, `dkbh`, `cslxkzmj`, `cslvxkzmj`, `cshxkzmj`, `cszxkzmj`, `ydmj`, `dxkjgh`, `bz`, `dybh`, `dymc`, `jqbh`, `jqmc`, `yddm`, `ydmc`, `hhyd`, `zjzmj`, `shape_length`, `shape_area`, `rjlsx`, `jzmdsx`, `jzxgsx`, `ldlxx`, `njlzl`, `ptss`, `qtkz`, `tcbwpb`, `objectid`, `join_count`, `target_fid`, `bsm_1`, `shape_length_1`, `shape_area_1`) VALUES (%s, %s, %s, %s, %s, %s, %s, %s, %s, %s, %s, %s, %s, %s, %s, %s, %s, %s, %s, %s, %s, %s, %s, %s, %s, %s, %s, %s, %s, %s, %s, %s, %s, %s, %s, %s, %s, %s, %s, %s, %s, %s, %s, %s, %s, %s, %s, %s, %s, %s, %s, %s, %s)
2025-08-04 17:17:13,190 - ERROR - 记录样本: {'id': 'f30b3b6439d2475d9a1096138a8ce0e6', 'layer_name': 'SSCMYDBJGH_ExportTable3', 'geometry_type': None, 'geometry_wkt': None, 'geometry_wkb': None, 'lng': None, 'lat': None, 'area': None, 'perimeter': None, 'source_file': '/Users/<USER>/Downloads/MyProject.gdb', 'import_time': '2025-08-04 17:17:09', 'del_flag': '0', 'create_by': 'system', 'create_time': '2025-08-04 17:17:09', 'update_by': 'system', 'update_time': '2025-08-04 17:17:09', 'remark': '从MyProject.gdb导入', 'bsm': '', 'ysdm': '2090020503', 'xzqdm': '510900', 'xzqmc': '遂宁市', 'dkbh': 'HD09-05-06', 'cslxkzmj': None, 'cslvxkzmj': None, 'cshxkzmj': None, 'cszxkzmj': None, 'ydmj': 17939.009765625, 'dxkjgh': '一般开发区', 'bz': '', 'dybh': 'HD09', 'dymc': 'HD09', 'jqbh': 'HD09-05', 'jqmc': 'HD09-05', 'yddm': '070102', 'ydmc': '二类城镇住宅用地', 'hhyd': None, 'zjzmj': 35881.09765625, 'shape_length': 942.3894332571749, 'shape_area': 17940.549620506332, 'rjlsx': 2.0, 'jzmdsx': 35.0, 'jzxgsx': 24.0, 'ldlxx': 35.0, 'njlzl': 80.0, 'ptss': '——', 'qtkz': '——', 'tcbwpb': '1车位/户', 'objectid': 1, 'join_count': 2, 'target_fid': 1, 'bsm_1': '510900000000000854', 'shape_length_1': 942.3894332571749, 'shape_area_1': 17940.549620506332}
2025-08-04 17:17:13,256 - ERROR - 导入图层 SSCMYDBJGH_ExportTable3 失败: (1264, "Out of range value for column 'objectid' at row 128")
2025-08-04 17:17:13,257 - ERROR - ✗ 图层 'SSCMYDBJGH_ExportTable3' 导入失败
2025-08-04 17:17:13,257 - INFO - 
==================================================
2025-08-04 17:17:13,257 - INFO - 导入完成: 0/2 个图层成功
2025-08-04 17:17:13,257 - INFO - 创建的表: []
2025-08-04 17:17:13,257 - INFO - 数据导入完成！
2025-08-04 17:19:08,921 - INFO - 开始GIS数据导入...
2025-08-04 17:19:08,921 - INFO - GDB路径: /Users/<USER>/Downloads/MyProject.gdb
2025-08-04 17:19:08,921 - INFO - 表前缀: 510903xx
2025-08-04 17:19:08,921 - INFO - 指定图层: ['SSCMYDBJGH_ExportTable', 'SSCMYDBJGH_ExportTable3']
2025-08-04 17:19:08,921 - INFO - 导入模式: 分表
2025-08-04 17:19:08,921 - INFO - 批量大小: 1000
2025-08-04 17:19:10,632 - INFO - 数据库连接成功
2025-08-04 17:19:10,676 - INFO - GDAL signalled an error: err_no=1, msg='PROJ: internal_proj_identify: /Users/<USER>/miniconda3/share/proj/proj.db contains DATABASE.LAYOUT.VERSION.MINOR = 2 whereas a number >= 3 is expected. It comes from another PROJ installation.'
2025-08-04 17:19:10,730 - INFO - GDAL signalled an error: err_no=1, msg='PROJ: internal_proj_identify: /Users/<USER>/miniconda3/share/proj/proj.db contains DATABASE.LAYOUT.VERSION.MINOR = 2 whereas a number >= 3 is expected. It comes from another PROJ installation.'
2025-08-04 17:19:10,761 - INFO - GDAL signalled an error: err_no=1, msg='PROJ: internal_proj_identify: /Users/<USER>/miniconda3/share/proj/proj.db contains DATABASE.LAYOUT.VERSION.MINOR = 2 whereas a number >= 3 is expected. It comes from another PROJ installation.'
2025-08-04 17:19:10,815 - INFO - 发现 9 个图层: ['县级行政区_FeatureToLine', '市级行政区_FeatureToLine', '县级行政区_FeatureToLine1', '县级行政区_FeatureToLine2', '经开区管辖范围线Polyli_FeatureToLine', 'ShorelineContr_FeatureToPoly', 'ShorelineContr_FeatureToPoly1', 'SSCMYDBJGH_ExportTable', 'SSCMYDBJGH_ExportTable3']
2025-08-04 17:19:10,815 - INFO - GDB中可用图层: ['县级行政区_FeatureToLine', '市级行政区_FeatureToLine', '县级行政区_FeatureToLine1', '县级行政区_FeatureToLine2', '经开区管辖范围线Polyli_FeatureToLine', 'ShorelineContr_FeatureToPoly', 'ShorelineContr_FeatureToPoly1', 'SSCMYDBJGH_ExportTable', 'SSCMYDBJGH_ExportTable3']
2025-08-04 17:19:10,815 - INFO - ✓ 图层 'SSCMYDBJGH_ExportTable' 已加入导入列表
2025-08-04 17:19:10,815 - INFO - ✓ 图层 'SSCMYDBJGH_ExportTable3' 已加入导入列表
2025-08-04 17:19:10,815 - INFO - 准备导入 2 个图层: ['SSCMYDBJGH_ExportTable', 'SSCMYDBJGH_ExportTable3']
2025-08-04 17:19:10,815 - INFO - 
==================================================
2025-08-04 17:19:10,815 - INFO - 正在处理图层: SSCMYDBJGH_ExportTable
2025-08-04 17:19:10,815 - INFO - ==================================================
2025-08-04 17:19:10,815 - INFO - 目标表名: 510903xx_sscmydbjgh_exporttable
2025-08-04 17:19:10,925 - INFO - 图层 SSCMYDBJGH_ExportTable 字段分析完成: {'name': 'VARCHAR(255)', 'BSM': 'VARCHAR(255)', 'YSDM': 'VARCHAR(255)', 'XZQDM': 'VARCHAR(255)', 'XZQMC': 'VARCHAR(255)', 'XXGHBZDYBH': 'VARCHAR(255)', 'XXGHBZDYMC': 'VARCHAR(255)', 'DKBH': 'VARCHAR(255)', 'CSLXKZMJ': 'DECIMAL(15,6)', 'CSLVXKZMJ': 'DECIMAL(15,6)', 'CSHXKZMJ': 'DECIMAL(10,4)', 'CSZXKZMJ': 'DECIMAL(15,6)', 'YDYHFLDM': 'VARCHAR(255)', 'YDYHFLMC': 'VARCHAR(255)', 'YDMJ': 'DECIMAL(10,4)', 'DXKJGH': 'VARCHAR(255)', 'BZ': 'VARCHAR(255)', 'Shape_Length': 'DECIMAL(18,6)', 'Shape_Area': 'DECIMAL(20,6)', 'JQBH': 'VARCHAR(255)', 'JQMC': 'VARCHAR(255)', 'ZJZMJ': 'DECIMAL(10,4)', 'RJL': 'VARCHAR(255)', 'JZXG': 'VARCHAR(255)', 'LVDL': 'VARCHAR(255)', 'JZMD': 'VARCHAR(255)', 'ZZNJLZLKZL': 'VARCHAR(255)', 'XCSLDL': 'VARCHAR(255)', 'TSPZL': 'VARCHAR(255)', 'LSWDL': 'VARCHAR(255)', 'TCWPB': 'VARCHAR(255)', 'YDKBH': 'VARCHAR(255)', 'JDCCRK': 'VARCHAR(255)', 'JZKK': 'VARCHAR(255)', 'TJSFYCY': 'VARCHAR(255)', 'QTPTSFYCY': 'VARCHAR(255)', 'OBJECTID': 'TINYINT', 'Join_Count': 'TINYINT', 'TARGET_FID': 'SMALLINT', 'BSM_1': 'VARCHAR(255)', 'Shape_Length_1': 'DECIMAL(18,6)', 'Shape_Area_1': 'DECIMAL(20,6)'}
2025-08-04 17:19:11,044 - INFO - 表 510903xx_sscmydbjgh_exporttable 创建成功
2025-08-04 17:19:11,045 - INFO - 开始读取图层: SSCMYDBJGH_ExportTable
2025-08-04 17:19:11,058 - INFO - 图层 SSCMYDBJGH_ExportTable 包含 783 个要素
2025-08-04 17:19:11,058 - INFO - 图层字段: ['name', 'BSM', 'YSDM', 'XZQDM', 'XZQMC', 'XXGHBZDYBH', 'XXGHBZDYMC', 'DKBH', 'CSLXKZMJ', 'CSLVXKZMJ', 'CSHXKZMJ', 'CSZXKZMJ', 'YDYHFLDM', 'YDYHFLMC', 'YDMJ', 'DXKJGH', 'BZ', 'Shape_Length', 'Shape_Area', 'JQBH', 'JQMC', 'ZJZMJ', 'RJL', 'JZXG', 'LVDL', 'JZMD', 'ZZNJLZLKZL', 'XCSLDL', 'TSPZL', 'LSWDL', 'TCWPB', 'YDKBH', 'JDCCRK', 'JZKK', 'TJSFYCY', 'QTPTSFYCY', 'OBJECTID', 'Join_Count', 'TARGET_FID', 'BSM_1', 'Shape_Length_1', 'Shape_Area_1']
2025-08-04 17:19:17,574 - ERROR - 批量插入失败: (1264, "Out of range value for column 'ydmj' at row 656")
2025-08-04 17:19:17,575 - ERROR - SQL: INSERT INTO `510903xx_sscmydbjgh_exporttable` (`id`, `layer_name`, `geometry_type`, `geometry_wkt`, `geometry_wkb`, `lng`, `lat`, `area`, `perimeter`, `source_file`, `import_time`, `del_flag`, `create_by`, `create_time`, `update_by`, `update_time`, `remark`, `name`, `bsm`, `ysdm`, `xzqdm`, `xzqmc`, `xxghbzdybh`, `xxghbzdymc`, `dkbh`, `cslxkzmj`, `cslvxkzmj`, `cshxkzmj`, `cszxkzmj`, `ydyhfldm`, `ydyhflmc`, `ydmj`, `dxkjgh`, `bz`, `shape_length`, `shape_area`, `jqbh`, `jqmc`, `zjzmj`, `rjl`, `jzxg`, `lvdl`, `jzmd`, `zznjlzlkzl`, `xcsldl`, `tspzl`, `lswdl`, `tcwpb`, `ydkbh`, `jdccrk`, `jzkk`, `tjsfycy`, `qtptsfycy`, `objectid`, `join_count`, `target_fid`, `bsm_1`, `shape_length_1`, `shape_area_1`) VALUES (%s, %s, %s, %s, %s, %s, %s, %s, %s, %s, %s, %s, %s, %s, %s, %s, %s, %s, %s, %s, %s, %s, %s, %s, %s, %s, %s, %s, %s, %s, %s, %s, %s, %s, %s, %s, %s, %s, %s, %s, %s, %s, %s, %s, %s, %s, %s, %s, %s, %s, %s, %s, %s, %s, %s, %s, %s, %s, %s)
2025-08-04 17:19:17,576 - ERROR - 记录样本: {'id': '1a5044ae10e648afa9fec7bc933894db', 'layer_name': 'SSCMYDBJGH_ExportTable', 'geometry_type': None, 'geometry_wkt': None, 'geometry_wkb': None, 'lng': None, 'lat': None, 'area': None, 'perimeter': None, 'source_file': '/Users/<USER>/Downloads/MyProject.gdb', 'import_time': '2025-08-04 17:19:11', 'del_flag': '0', 'create_by': 'system', 'create_time': '2025-08-04 17:19:11', 'update_by': 'system', 'update_time': '2025-08-04 17:19:11', 'remark': '从MyProject.gdb导入', 'name': '0903娱乐用地', 'bsm': None, 'ysdm': '2090020501', 'xzqdm': '510900', 'xzqmc': '遂宁市', 'xxghbzdybh': '51090000000HD03', 'xxghbzdymc': 'HD03', 'dkbh': 'HD03-03-07', 'cslxkzmj': None, 'cslvxkzmj': None, 'cshxkzmj': None, 'cszxkzmj': None, 'ydyhfldm': '0903', 'ydyhflmc': '娱乐用地', 'ydmj': 24757.83984375, 'dxkjgh': '一般开发区域', 'bz': '已批规划', 'shape_length': 616.1477884887105, 'shape_area': 24759.717620558873, 'jqbh': 'HD03-03', 'jqmc': 'HD03-03', 'zjzmj': 19806.271484375, 'rjl': '0.8', 'jzxg': '24', 'lvdl': '30', 'jzmd': '50', 'zznjlzlkzl': '80', 'xcsldl': '40', 'tspzl': '60', 'lswdl': '50', 'tcwpb': '-', 'ydkbh': 'HD03-03-05', 'jdccrk': '', 'jzkk': '', 'tjsfycy': '', 'qtptsfycy': '原设计条件未配公厕，\n原设计条件标注用途剧院会议', 'objectid': 1, 'join_count': 3, 'target_fid': 1014, 'bsm_1': '510900000000003018', 'shape_length_1': 616.1477884887105, 'shape_area_1': 24759.717620558873}
2025-08-04 17:19:17,639 - ERROR - 导入图层 SSCMYDBJGH_ExportTable 失败: (1264, "Out of range value for column 'ydmj' at row 656")
2025-08-04 17:19:17,642 - ERROR - ✗ 图层 'SSCMYDBJGH_ExportTable' 导入失败
2025-08-04 17:19:17,642 - INFO - 
==================================================
2025-08-04 17:19:17,642 - INFO - 正在处理图层: SSCMYDBJGH_ExportTable3
2025-08-04 17:19:17,642 - INFO - ==================================================
2025-08-04 17:19:17,643 - INFO - 目标表名: 510903xx_sscmydbjgh_exporttable3
2025-08-04 17:19:17,652 - INFO - 图层 SSCMYDBJGH_ExportTable3 字段分析完成: {'BSM': 'VARCHAR(255)', 'YSDM': 'VARCHAR(255)', 'XZQDM': 'VARCHAR(255)', 'XZQMC': 'VARCHAR(255)', 'DKBH': 'VARCHAR(255)', 'CSLXKZMJ': 'DECIMAL(15,6)', 'CSLVXKZMJ': 'DECIMAL(15,6)', 'CSHXKZMJ': 'DECIMAL(10,4)', 'CSZXKZMJ': 'DECIMAL(10,4)', 'YDMJ': 'DECIMAL(10,4)', 'DXKJGH': 'VARCHAR(255)', 'BZ': 'VARCHAR(255)', 'DYBH': 'VARCHAR(255)', 'DYMC': 'VARCHAR(255)', 'JQBH': 'VARCHAR(255)', 'JQMC': 'VARCHAR(255)', 'YDDM': 'VARCHAR(255)', 'YDMC': 'VARCHAR(255)', 'HHYD': 'VARCHAR(255)', 'ZJZMJ': 'DECIMAL(10,4)', 'SHAPE_Length': 'DECIMAL(18,6)', 'SHAPE_Area': 'DECIMAL(20,6)', 'RJLSX': 'DECIMAL(10,1)', 'JZMDSX': 'DECIMAL(10,1)', 'JZXGSX': 'DECIMAL(10,1)', 'LDLXX': 'DECIMAL(10,1)', 'NJLZL': 'DECIMAL(10,1)', 'PTSS': 'VARCHAR(255)', 'QTKZ': 'VARCHAR(258)', 'TCBWPB': 'VARCHAR(255)', 'OBJECTID': 'TINYINT', 'Join_Count': 'TINYINT', 'TARGET_FID': 'TINYINT', 'BSM_1': 'VARCHAR(255)', 'SHAPE_Length_1': 'DECIMAL(18,6)', 'SHAPE_Area_1': 'DECIMAL(20,6)'}
2025-08-04 17:19:18,291 - INFO - 表 510903xx_sscmydbjgh_exporttable3 创建成功
2025-08-04 17:19:18,291 - INFO - 开始读取图层: SSCMYDBJGH_ExportTable3
2025-08-04 17:19:18,297 - INFO - 图层 SSCMYDBJGH_ExportTable3 包含 327 个要素
2025-08-04 17:19:18,297 - INFO - 图层字段: ['BSM', 'YSDM', 'XZQDM', 'XZQMC', 'DKBH', 'CSLXKZMJ', 'CSLVXKZMJ', 'CSHXKZMJ', 'CSZXKZMJ', 'YDMJ', 'DXKJGH', 'BZ', 'DYBH', 'DYMC', 'JQBH', 'JQMC', 'YDDM', 'YDMC', 'HHYD', 'ZJZMJ', 'SHAPE_Length', 'SHAPE_Area', 'RJLSX', 'JZMDSX', 'JZXGSX', 'LDLXX', 'NJLZL', 'PTSS', 'QTKZ', 'TCBWPB', 'OBJECTID', 'Join_Count', 'TARGET_FID', 'BSM_1', 'SHAPE_Length_1', 'SHAPE_Area_1']
2025-08-04 17:19:21,539 - ERROR - 批量插入失败: (1264, "Out of range value for column 'target_fid' at row 128")
2025-08-04 17:19:21,540 - ERROR - SQL: INSERT INTO `510903xx_sscmydbjgh_exporttable3` (`id`, `layer_name`, `geometry_type`, `geometry_wkt`, `geometry_wkb`, `lng`, `lat`, `area`, `perimeter`, `source_file`, `import_time`, `del_flag`, `create_by`, `create_time`, `update_by`, `update_time`, `remark`, `bsm`, `ysdm`, `xzqdm`, `xzqmc`, `dkbh`, `cslxkzmj`, `cslvxkzmj`, `cshxkzmj`, `cszxkzmj`, `ydmj`, `dxkjgh`, `bz`, `dybh`, `dymc`, `jqbh`, `jqmc`, `yddm`, `ydmc`, `hhyd`, `zjzmj`, `shape_length`, `shape_area`, `rjlsx`, `jzmdsx`, `jzxgsx`, `ldlxx`, `njlzl`, `ptss`, `qtkz`, `tcbwpb`, `objectid`, `join_count`, `target_fid`, `bsm_1`, `shape_length_1`, `shape_area_1`) VALUES (%s, %s, %s, %s, %s, %s, %s, %s, %s, %s, %s, %s, %s, %s, %s, %s, %s, %s, %s, %s, %s, %s, %s, %s, %s, %s, %s, %s, %s, %s, %s, %s, %s, %s, %s, %s, %s, %s, %s, %s, %s, %s, %s, %s, %s, %s, %s, %s, %s, %s, %s, %s, %s)
2025-08-04 17:19:21,540 - ERROR - 记录样本: {'id': '95ed55436bd34bc89db02a4b1d077786', 'layer_name': 'SSCMYDBJGH_ExportTable3', 'geometry_type': None, 'geometry_wkt': None, 'geometry_wkb': None, 'lng': None, 'lat': None, 'area': None, 'perimeter': None, 'source_file': '/Users/<USER>/Downloads/MyProject.gdb', 'import_time': '2025-08-04 17:19:18', 'del_flag': '0', 'create_by': 'system', 'create_time': '2025-08-04 17:19:18', 'update_by': 'system', 'update_time': '2025-08-04 17:19:18', 'remark': '从MyProject.gdb导入', 'bsm': '', 'ysdm': '2090020503', 'xzqdm': '510900', 'xzqmc': '遂宁市', 'dkbh': 'HD09-05-06', 'cslxkzmj': None, 'cslvxkzmj': None, 'cshxkzmj': None, 'cszxkzmj': None, 'ydmj': 17939.009765625, 'dxkjgh': '一般开发区', 'bz': '', 'dybh': 'HD09', 'dymc': 'HD09', 'jqbh': 'HD09-05', 'jqmc': 'HD09-05', 'yddm': '070102', 'ydmc': '二类城镇住宅用地', 'hhyd': None, 'zjzmj': 35881.09765625, 'shape_length': 942.3894332571749, 'shape_area': 17940.549620506332, 'rjlsx': 2.0, 'jzmdsx': 35.0, 'jzxgsx': 24.0, 'ldlxx': 35.0, 'njlzl': 80.0, 'ptss': '——', 'qtkz': '——', 'tcbwpb': '1车位/户', 'objectid': 1, 'join_count': 2, 'target_fid': 1, 'bsm_1': '510900000000000854', 'shape_length_1': 942.3894332571749, 'shape_area_1': 17940.549620506332}
2025-08-04 17:19:21,598 - ERROR - 导入图层 SSCMYDBJGH_ExportTable3 失败: (1264, "Out of range value for column 'target_fid' at row 128")
2025-08-04 17:19:21,598 - ERROR - ✗ 图层 'SSCMYDBJGH_ExportTable3' 导入失败
2025-08-04 17:19:21,598 - INFO - 
==================================================
2025-08-04 17:19:21,598 - INFO - 导入完成: 0/2 个图层成功
2025-08-04 17:19:21,598 - INFO - 创建的表: []
2025-08-04 17:19:21,599 - INFO - 数据导入完成！
2025-08-04 17:19:48,007 - INFO - 开始GIS数据导入...
2025-08-04 17:19:48,007 - INFO - GDB路径: /Users/<USER>/Downloads/MyProject.gdb
2025-08-04 17:19:48,007 - INFO - 表前缀: 510903xx
2025-08-04 17:19:48,008 - INFO - 指定图层: ['SSCMYDBJGH_ExportTable', 'SSCMYDBJGH_ExportTable3']
2025-08-04 17:19:48,008 - INFO - 导入模式: 分表
2025-08-04 17:19:48,008 - INFO - 批量大小: 1000
2025-08-04 17:19:50,116 - INFO - 数据库连接成功
2025-08-04 17:19:50,158 - INFO - GDAL signalled an error: err_no=1, msg='PROJ: internal_proj_identify: /Users/<USER>/miniconda3/share/proj/proj.db contains DATABASE.LAYOUT.VERSION.MINOR = 2 whereas a number >= 3 is expected. It comes from another PROJ installation.'
2025-08-04 17:19:50,211 - INFO - GDAL signalled an error: err_no=1, msg='PROJ: internal_proj_identify: /Users/<USER>/miniconda3/share/proj/proj.db contains DATABASE.LAYOUT.VERSION.MINOR = 2 whereas a number >= 3 is expected. It comes from another PROJ installation.'
2025-08-04 17:19:50,232 - INFO - GDAL signalled an error: err_no=1, msg='PROJ: internal_proj_identify: /Users/<USER>/miniconda3/share/proj/proj.db contains DATABASE.LAYOUT.VERSION.MINOR = 2 whereas a number >= 3 is expected. It comes from another PROJ installation.'
2025-08-04 17:19:50,244 - INFO - 发现 9 个图层: ['县级行政区_FeatureToLine', '市级行政区_FeatureToLine', '县级行政区_FeatureToLine1', '县级行政区_FeatureToLine2', '经开区管辖范围线Polyli_FeatureToLine', 'ShorelineContr_FeatureToPoly', 'ShorelineContr_FeatureToPoly1', 'SSCMYDBJGH_ExportTable', 'SSCMYDBJGH_ExportTable3']
2025-08-04 17:19:50,244 - INFO - GDB中可用图层: ['县级行政区_FeatureToLine', '市级行政区_FeatureToLine', '县级行政区_FeatureToLine1', '县级行政区_FeatureToLine2', '经开区管辖范围线Polyli_FeatureToLine', 'ShorelineContr_FeatureToPoly', 'ShorelineContr_FeatureToPoly1', 'SSCMYDBJGH_ExportTable', 'SSCMYDBJGH_ExportTable3']
2025-08-04 17:19:50,244 - INFO - ✓ 图层 'SSCMYDBJGH_ExportTable' 已加入导入列表
2025-08-04 17:19:50,244 - INFO - ✓ 图层 'SSCMYDBJGH_ExportTable3' 已加入导入列表
2025-08-04 17:19:50,244 - INFO - 准备导入 2 个图层: ['SSCMYDBJGH_ExportTable', 'SSCMYDBJGH_ExportTable3']
2025-08-04 17:19:50,244 - INFO - 
==================================================
2025-08-04 17:19:50,244 - INFO - 正在处理图层: SSCMYDBJGH_ExportTable
2025-08-04 17:19:50,244 - INFO - ==================================================
2025-08-04 17:19:50,244 - INFO - 目标表名: 510903xx_sscmydbjgh_exporttable
2025-08-04 17:19:50,369 - INFO - 图层 SSCMYDBJGH_ExportTable 字段分析完成: {'name': 'VARCHAR(255)', 'BSM': 'VARCHAR(255)', 'YSDM': 'VARCHAR(255)', 'XZQDM': 'VARCHAR(255)', 'XZQMC': 'VARCHAR(255)', 'XXGHBZDYBH': 'VARCHAR(255)', 'XXGHBZDYMC': 'VARCHAR(255)', 'DKBH': 'VARCHAR(255)', 'CSLXKZMJ': 'DECIMAL(15,6)', 'CSLVXKZMJ': 'DECIMAL(15,6)', 'CSHXKZMJ': 'DECIMAL(10,4)', 'CSZXKZMJ': 'DECIMAL(15,6)', 'YDYHFLDM': 'VARCHAR(255)', 'YDYHFLMC': 'VARCHAR(255)', 'YDMJ': 'DECIMAL(10,4)', 'DXKJGH': 'VARCHAR(255)', 'BZ': 'VARCHAR(255)', 'Shape_Length': 'DECIMAL(18,6)', 'Shape_Area': 'DECIMAL(20,6)', 'JQBH': 'VARCHAR(255)', 'JQMC': 'VARCHAR(255)', 'ZJZMJ': 'DECIMAL(10,4)', 'RJL': 'VARCHAR(255)', 'JZXG': 'VARCHAR(255)', 'LVDL': 'VARCHAR(255)', 'JZMD': 'VARCHAR(255)', 'ZZNJLZLKZL': 'VARCHAR(255)', 'XCSLDL': 'VARCHAR(255)', 'TSPZL': 'VARCHAR(255)', 'LSWDL': 'VARCHAR(255)', 'TCWPB': 'VARCHAR(255)', 'YDKBH': 'VARCHAR(255)', 'JDCCRK': 'VARCHAR(255)', 'JZKK': 'VARCHAR(255)', 'TJSFYCY': 'VARCHAR(255)', 'QTPTSFYCY': 'VARCHAR(255)', 'OBJECTID': 'TINYINT', 'Join_Count': 'TINYINT', 'TARGET_FID': 'SMALLINT', 'BSM_1': 'VARCHAR(255)', 'Shape_Length_1': 'DECIMAL(18,6)', 'Shape_Area_1': 'DECIMAL(20,6)'}
2025-08-04 17:19:50,753 - INFO - 表 510903xx_sscmydbjgh_exporttable 创建成功
2025-08-04 17:19:50,753 - INFO - 开始读取图层: SSCMYDBJGH_ExportTable
2025-08-04 17:19:50,766 - INFO - 图层 SSCMYDBJGH_ExportTable 包含 783 个要素
2025-08-04 17:19:50,766 - INFO - 图层字段: ['name', 'BSM', 'YSDM', 'XZQDM', 'XZQMC', 'XXGHBZDYBH', 'XXGHBZDYMC', 'DKBH', 'CSLXKZMJ', 'CSLVXKZMJ', 'CSHXKZMJ', 'CSZXKZMJ', 'YDYHFLDM', 'YDYHFLMC', 'YDMJ', 'DXKJGH', 'BZ', 'Shape_Length', 'Shape_Area', 'JQBH', 'JQMC', 'ZJZMJ', 'RJL', 'JZXG', 'LVDL', 'JZMD', 'ZZNJLZLKZL', 'XCSLDL', 'TSPZL', 'LSWDL', 'TCWPB', 'YDKBH', 'JDCCRK', 'JZKK', 'TJSFYCY', 'QTPTSFYCY', 'OBJECTID', 'Join_Count', 'TARGET_FID', 'BSM_1', 'Shape_Length_1', 'Shape_Area_1']
2025-08-04 17:20:00,070 - ERROR - 批量插入失败: (1264, "Out of range value for column 'ydmj' at row 656")
2025-08-04 17:20:00,071 - ERROR - SQL: INSERT INTO `510903xx_sscmydbjgh_exporttable` (`id`, `layer_name`, `geometry_type`, `geometry_wkt`, `geometry_wkb`, `lng`, `lat`, `area`, `perimeter`, `source_file`, `import_time`, `del_flag`, `create_by`, `create_time`, `update_by`, `update_time`, `remark`, `name`, `bsm`, `ysdm`, `xzqdm`, `xzqmc`, `xxghbzdybh`, `xxghbzdymc`, `dkbh`, `cslxkzmj`, `cslvxkzmj`, `cshxkzmj`, `cszxkzmj`, `ydyhfldm`, `ydyhflmc`, `ydmj`, `dxkjgh`, `bz`, `shape_length`, `shape_area`, `jqbh`, `jqmc`, `zjzmj`, `rjl`, `jzxg`, `lvdl`, `jzmd`, `zznjlzlkzl`, `xcsldl`, `tspzl`, `lswdl`, `tcwpb`, `ydkbh`, `jdccrk`, `jzkk`, `tjsfycy`, `qtptsfycy`, `objectid`, `join_count`, `target_fid`, `bsm_1`, `shape_length_1`, `shape_area_1`) VALUES (%s, %s, %s, %s, %s, %s, %s, %s, %s, %s, %s, %s, %s, %s, %s, %s, %s, %s, %s, %s, %s, %s, %s, %s, %s, %s, %s, %s, %s, %s, %s, %s, %s, %s, %s, %s, %s, %s, %s, %s, %s, %s, %s, %s, %s, %s, %s, %s, %s, %s, %s, %s, %s, %s, %s, %s, %s, %s, %s)
2025-08-04 17:20:00,071 - ERROR - 记录样本: {'id': '7e52a0f2e2ed4ec6858442d0034f11e2', 'layer_name': 'SSCMYDBJGH_ExportTable', 'geometry_type': None, 'geometry_wkt': None, 'geometry_wkb': None, 'lng': None, 'lat': None, 'area': None, 'perimeter': None, 'source_file': '/Users/<USER>/Downloads/MyProject.gdb', 'import_time': '2025-08-04 17:19:50', 'del_flag': '0', 'create_by': 'system', 'create_time': '2025-08-04 17:19:50', 'update_by': 'system', 'update_time': '2025-08-04 17:19:50', 'remark': '从MyProject.gdb导入', 'name': '0903娱乐用地', 'bsm': None, 'ysdm': '2090020501', 'xzqdm': '510900', 'xzqmc': '遂宁市', 'xxghbzdybh': '51090000000HD03', 'xxghbzdymc': 'HD03', 'dkbh': 'HD03-03-07', 'cslxkzmj': None, 'cslvxkzmj': None, 'cshxkzmj': None, 'cszxkzmj': None, 'ydyhfldm': '0903', 'ydyhflmc': '娱乐用地', 'ydmj': 24757.83984375, 'dxkjgh': '一般开发区域', 'bz': '已批规划', 'shape_length': 616.1477884887105, 'shape_area': 24759.717620558873, 'jqbh': 'HD03-03', 'jqmc': 'HD03-03', 'zjzmj': 19806.271484375, 'rjl': '0.8', 'jzxg': '24', 'lvdl': '30', 'jzmd': '50', 'zznjlzlkzl': '80', 'xcsldl': '40', 'tspzl': '60', 'lswdl': '50', 'tcwpb': '-', 'ydkbh': 'HD03-03-05', 'jdccrk': '', 'jzkk': '', 'tjsfycy': '', 'qtptsfycy': '原设计条件未配公厕，\n原设计条件标注用途剧院会议', 'objectid': 1, 'join_count': 3, 'target_fid': 1014, 'bsm_1': '510900000000003018', 'shape_length_1': 616.1477884887105, 'shape_area_1': 24759.717620558873}
2025-08-04 17:20:00,132 - ERROR - 导入图层 SSCMYDBJGH_ExportTable 失败: (1264, "Out of range value for column 'ydmj' at row 656")
2025-08-04 17:20:00,135 - ERROR - ✗ 图层 'SSCMYDBJGH_ExportTable' 导入失败
2025-08-04 17:20:00,135 - INFO - 
==================================================
2025-08-04 17:20:00,135 - INFO - 正在处理图层: SSCMYDBJGH_ExportTable3
2025-08-04 17:20:00,135 - INFO - ==================================================
2025-08-04 17:20:00,135 - INFO - 目标表名: 510903xx_sscmydbjgh_exporttable3
2025-08-04 17:20:00,143 - INFO - 图层 SSCMYDBJGH_ExportTable3 字段分析完成: {'BSM': 'VARCHAR(255)', 'YSDM': 'VARCHAR(255)', 'XZQDM': 'VARCHAR(255)', 'XZQMC': 'VARCHAR(255)', 'DKBH': 'VARCHAR(255)', 'CSLXKZMJ': 'DECIMAL(15,6)', 'CSLVXKZMJ': 'DECIMAL(15,6)', 'CSHXKZMJ': 'DECIMAL(10,4)', 'CSZXKZMJ': 'DECIMAL(10,4)', 'YDMJ': 'DECIMAL(10,4)', 'DXKJGH': 'VARCHAR(255)', 'BZ': 'VARCHAR(255)', 'DYBH': 'VARCHAR(255)', 'DYMC': 'VARCHAR(255)', 'JQBH': 'VARCHAR(255)', 'JQMC': 'VARCHAR(255)', 'YDDM': 'VARCHAR(255)', 'YDMC': 'VARCHAR(255)', 'HHYD': 'VARCHAR(255)', 'ZJZMJ': 'DECIMAL(10,4)', 'SHAPE_Length': 'DECIMAL(18,6)', 'SHAPE_Area': 'DECIMAL(20,6)', 'RJLSX': 'DECIMAL(10,1)', 'JZMDSX': 'DECIMAL(10,1)', 'JZXGSX': 'DECIMAL(10,1)', 'LDLXX': 'DECIMAL(10,1)', 'NJLZL': 'DECIMAL(10,1)', 'PTSS': 'VARCHAR(255)', 'QTKZ': 'VARCHAR(258)', 'TCBWPB': 'VARCHAR(255)', 'OBJECTID': 'TINYINT', 'Join_Count': 'TINYINT', 'TARGET_FID': 'TINYINT', 'BSM_1': 'VARCHAR(255)', 'SHAPE_Length_1': 'DECIMAL(18,6)', 'SHAPE_Area_1': 'DECIMAL(20,6)'}
2025-08-04 17:20:00,261 - INFO - 表 510903xx_sscmydbjgh_exporttable3 创建成功
2025-08-04 17:20:00,261 - INFO - 开始读取图层: SSCMYDBJGH_ExportTable3
2025-08-04 17:20:00,268 - INFO - 图层 SSCMYDBJGH_ExportTable3 包含 327 个要素
2025-08-04 17:20:00,268 - INFO - 图层字段: ['BSM', 'YSDM', 'XZQDM', 'XZQMC', 'DKBH', 'CSLXKZMJ', 'CSLVXKZMJ', 'CSHXKZMJ', 'CSZXKZMJ', 'YDMJ', 'DXKJGH', 'BZ', 'DYBH', 'DYMC', 'JQBH', 'JQMC', 'YDDM', 'YDMC', 'HHYD', 'ZJZMJ', 'SHAPE_Length', 'SHAPE_Area', 'RJLSX', 'JZMDSX', 'JZXGSX', 'LDLXX', 'NJLZL', 'PTSS', 'QTKZ', 'TCBWPB', 'OBJECTID', 'Join_Count', 'TARGET_FID', 'BSM_1', 'SHAPE_Length_1', 'SHAPE_Area_1']
2025-08-04 17:20:04,149 - INFO - 已导入 327/327 个要素
2025-08-04 17:20:04,149 - INFO - 图层 SSCMYDBJGH_ExportTable3 导入完成，共导入 327 个要素
2025-08-04 17:20:04,149 - INFO - 其中 327 个要素缺失geometry数据，已正常插入
2025-08-04 17:20:04,149 - INFO - ✓ 图层 'SSCMYDBJGH_ExportTable3' 成功导入到表 '510903xx_sscmydbjgh_exporttable3'
2025-08-04 17:20:04,149 - INFO - 
==================================================
2025-08-04 17:20:04,149 - INFO - 导入完成: 1/2 个图层成功
2025-08-04 17:20:04,149 - INFO - 创建的表: ['510903xx_sscmydbjgh_exporttable3']
2025-08-04 17:20:04,150 - INFO - 数据导入完成！
2025-08-04 17:21:52,884 - INFO - 开始GIS数据导入...
2025-08-04 17:21:52,884 - INFO - GDB路径: /Users/<USER>/Downloads/MyProject.gdb
2025-08-04 17:21:52,884 - INFO - 表前缀: 510903xx
2025-08-04 17:21:52,884 - INFO - 指定图层: ['SSCMYDBJGH_ExportTable', 'SSCMYDBJGH_ExportTable3']
2025-08-04 17:21:52,884 - INFO - 导入模式: 分表
2025-08-04 17:21:52,884 - INFO - 批量大小: 1000
2025-08-04 17:21:54,754 - INFO - 数据库连接成功
2025-08-04 17:21:54,798 - INFO - GDAL signalled an error: err_no=1, msg='PROJ: internal_proj_identify: /Users/<USER>/miniconda3/share/proj/proj.db contains DATABASE.LAYOUT.VERSION.MINOR = 2 whereas a number >= 3 is expected. It comes from another PROJ installation.'
2025-08-04 17:21:54,851 - INFO - GDAL signalled an error: err_no=1, msg='PROJ: internal_proj_identify: /Users/<USER>/miniconda3/share/proj/proj.db contains DATABASE.LAYOUT.VERSION.MINOR = 2 whereas a number >= 3 is expected. It comes from another PROJ installation.'
2025-08-04 17:21:54,875 - INFO - GDAL signalled an error: err_no=1, msg='PROJ: internal_proj_identify: /Users/<USER>/miniconda3/share/proj/proj.db contains DATABASE.LAYOUT.VERSION.MINOR = 2 whereas a number >= 3 is expected. It comes from another PROJ installation.'
2025-08-04 17:21:54,888 - INFO - 发现 9 个图层: ['县级行政区_FeatureToLine', '市级行政区_FeatureToLine', '县级行政区_FeatureToLine1', '县级行政区_FeatureToLine2', '经开区管辖范围线Polyli_FeatureToLine', 'ShorelineContr_FeatureToPoly', 'ShorelineContr_FeatureToPoly1', 'SSCMYDBJGH_ExportTable', 'SSCMYDBJGH_ExportTable3']
2025-08-04 17:21:54,888 - INFO - GDB中可用图层: ['县级行政区_FeatureToLine', '市级行政区_FeatureToLine', '县级行政区_FeatureToLine1', '县级行政区_FeatureToLine2', '经开区管辖范围线Polyli_FeatureToLine', 'ShorelineContr_FeatureToPoly', 'ShorelineContr_FeatureToPoly1', 'SSCMYDBJGH_ExportTable', 'SSCMYDBJGH_ExportTable3']
2025-08-04 17:21:54,888 - INFO - ✓ 图层 'SSCMYDBJGH_ExportTable' 已加入导入列表
2025-08-04 17:21:54,888 - INFO - ✓ 图层 'SSCMYDBJGH_ExportTable3' 已加入导入列表
2025-08-04 17:21:54,888 - INFO - 准备导入 2 个图层: ['SSCMYDBJGH_ExportTable', 'SSCMYDBJGH_ExportTable3']
2025-08-04 17:21:54,888 - INFO - 
==================================================
2025-08-04 17:21:54,888 - INFO - 正在处理图层: SSCMYDBJGH_ExportTable
2025-08-04 17:21:54,888 - INFO - ==================================================
2025-08-04 17:21:54,889 - INFO - 目标表名: 510903xx_sscmydbjgh_exporttable
2025-08-04 17:21:54,977 - INFO - 图层 SSCMYDBJGH_ExportTable 字段分析完成: {'name': 'VARCHAR(255)', 'BSM': 'VARCHAR(255)', 'YSDM': 'VARCHAR(255)', 'XZQDM': 'VARCHAR(255)', 'XZQMC': 'VARCHAR(255)', 'XXGHBZDYBH': 'VARCHAR(255)', 'XXGHBZDYMC': 'VARCHAR(255)', 'DKBH': 'VARCHAR(255)', 'CSLXKZMJ': 'DECIMAL(15,6)', 'CSLVXKZMJ': 'DECIMAL(15,6)', 'CSHXKZMJ': 'DECIMAL(10,4)', 'CSZXKZMJ': 'DECIMAL(15,6)', 'YDYHFLDM': 'VARCHAR(255)', 'YDYHFLMC': 'VARCHAR(255)', 'YDMJ': 'DECIMAL(10,4)', 'DXKJGH': 'VARCHAR(255)', 'BZ': 'VARCHAR(255)', 'Shape_Length': 'DECIMAL(18,6)', 'Shape_Area': 'DECIMAL(20,6)', 'JQBH': 'VARCHAR(255)', 'JQMC': 'VARCHAR(255)', 'ZJZMJ': 'DECIMAL(10,4)', 'RJL': 'VARCHAR(255)', 'JZXG': 'VARCHAR(255)', 'LVDL': 'VARCHAR(255)', 'JZMD': 'VARCHAR(255)', 'ZZNJLZLKZL': 'VARCHAR(255)', 'XCSLDL': 'VARCHAR(255)', 'TSPZL': 'VARCHAR(255)', 'LSWDL': 'VARCHAR(255)', 'TCWPB': 'VARCHAR(255)', 'YDKBH': 'VARCHAR(255)', 'JDCCRK': 'VARCHAR(255)', 'JZKK': 'VARCHAR(255)', 'TJSFYCY': 'VARCHAR(255)', 'QTPTSFYCY': 'VARCHAR(255)', 'OBJECTID': 'TINYINT', 'Join_Count': 'TINYINT', 'TARGET_FID': 'SMALLINT', 'BSM_1': 'VARCHAR(255)', 'Shape_Length_1': 'DECIMAL(18,6)', 'Shape_Area_1': 'DECIMAL(20,6)'}
2025-08-04 17:21:55,470 - INFO - 表 510903xx_sscmydbjgh_exporttable 创建成功
2025-08-04 17:21:55,471 - INFO - 开始读取图层: SSCMYDBJGH_ExportTable
2025-08-04 17:21:55,486 - INFO - 图层 SSCMYDBJGH_ExportTable 包含 783 个要素
2025-08-04 17:21:55,486 - INFO - 图层字段: ['name', 'BSM', 'YSDM', 'XZQDM', 'XZQMC', 'XXGHBZDYBH', 'XXGHBZDYMC', 'DKBH', 'CSLXKZMJ', 'CSLVXKZMJ', 'CSHXKZMJ', 'CSZXKZMJ', 'YDYHFLDM', 'YDYHFLMC', 'YDMJ', 'DXKJGH', 'BZ', 'Shape_Length', 'Shape_Area', 'JQBH', 'JQMC', 'ZJZMJ', 'RJL', 'JZXG', 'LVDL', 'JZMD', 'ZZNJLZLKZL', 'XCSLDL', 'TSPZL', 'LSWDL', 'TCWPB', 'YDKBH', 'JDCCRK', 'JZKK', 'TJSFYCY', 'QTPTSFYCY', 'OBJECTID', 'Join_Count', 'TARGET_FID', 'BSM_1', 'Shape_Length_1', 'Shape_Area_1']
2025-08-04 17:22:04,420 - ERROR - 批量插入失败: (1264, "Out of range value for column 'join_count' at row 656")
2025-08-04 17:22:04,420 - ERROR - SQL: INSERT INTO `510903xx_sscmydbjgh_exporttable` (`id`, `layer_name`, `geometry_type`, `geometry_wkt`, `geometry_wkb`, `lng`, `lat`, `area`, `perimeter`, `source_file`, `import_time`, `del_flag`, `create_by`, `create_time`, `update_by`, `update_time`, `remark`, `name`, `bsm`, `ysdm`, `xzqdm`, `xzqmc`, `xxghbzdybh`, `xxghbzdymc`, `dkbh`, `cslxkzmj`, `cslvxkzmj`, `cshxkzmj`, `cszxkzmj`, `ydyhfldm`, `ydyhflmc`, `ydmj`, `dxkjgh`, `bz`, `shape_length`, `shape_area`, `jqbh`, `jqmc`, `zjzmj`, `rjl`, `jzxg`, `lvdl`, `jzmd`, `zznjlzlkzl`, `xcsldl`, `tspzl`, `lswdl`, `tcwpb`, `ydkbh`, `jdccrk`, `jzkk`, `tjsfycy`, `qtptsfycy`, `objectid`, `join_count`, `target_fid`, `bsm_1`, `shape_length_1`, `shape_area_1`) VALUES (%s, %s, %s, %s, %s, %s, %s, %s, %s, %s, %s, %s, %s, %s, %s, %s, %s, %s, %s, %s, %s, %s, %s, %s, %s, %s, %s, %s, %s, %s, %s, %s, %s, %s, %s, %s, %s, %s, %s, %s, %s, %s, %s, %s, %s, %s, %s, %s, %s, %s, %s, %s, %s, %s, %s, %s, %s, %s, %s)
2025-08-04 17:22:04,421 - ERROR - 记录样本: {'id': 'f8de4d3452b04d3f955c26676c178083', 'layer_name': 'SSCMYDBJGH_ExportTable', 'geometry_type': None, 'geometry_wkt': None, 'geometry_wkb': None, 'lng': None, 'lat': None, 'area': None, 'perimeter': None, 'source_file': '/Users/<USER>/Downloads/MyProject.gdb', 'import_time': '2025-08-04 17:21:55', 'del_flag': '0', 'create_by': 'system', 'create_time': '2025-08-04 17:21:55', 'update_by': 'system', 'update_time': '2025-08-04 17:21:55', 'remark': '从MyProject.gdb导入', 'name': '0903娱乐用地', 'bsm': None, 'ysdm': '2090020501', 'xzqdm': '510900', 'xzqmc': '遂宁市', 'xxghbzdybh': '51090000000HD03', 'xxghbzdymc': 'HD03', 'dkbh': 'HD03-03-07', 'cslxkzmj': None, 'cslvxkzmj': None, 'cshxkzmj': None, 'cszxkzmj': None, 'ydyhfldm': '0903', 'ydyhflmc': '娱乐用地', 'ydmj': 24757.83984375, 'dxkjgh': '一般开发区域', 'bz': '已批规划', 'shape_length': 616.1477884887105, 'shape_area': 24759.717620558873, 'jqbh': 'HD03-03', 'jqmc': 'HD03-03', 'zjzmj': 19806.271484375, 'rjl': '0.8', 'jzxg': '24', 'lvdl': '30', 'jzmd': '50', 'zznjlzlkzl': '80', 'xcsldl': '40', 'tspzl': '60', 'lswdl': '50', 'tcwpb': '-', 'ydkbh': 'HD03-03-05', 'jdccrk': '', 'jzkk': '', 'tjsfycy': '', 'qtptsfycy': '原设计条件未配公厕，\n原设计条件标注用途剧院会议', 'objectid': 1, 'join_count': 3, 'target_fid': 1014, 'bsm_1': '510900000000003018', 'shape_length_1': 616.1477884887105, 'shape_area_1': 24759.717620558873}
2025-08-04 17:22:04,484 - ERROR - 导入图层 SSCMYDBJGH_ExportTable 失败: (1264, "Out of range value for column 'join_count' at row 656")
2025-08-04 17:22:04,485 - ERROR - ✗ 图层 'SSCMYDBJGH_ExportTable' 导入失败
2025-08-04 17:22:04,485 - INFO - 
==================================================
2025-08-04 17:22:04,485 - INFO - 正在处理图层: SSCMYDBJGH_ExportTable3
2025-08-04 17:22:04,485 - INFO - ==================================================
2025-08-04 17:22:04,485 - INFO - 目标表名: 510903xx_sscmydbjgh_exporttable3
2025-08-04 17:22:04,491 - INFO - 图层 SSCMYDBJGH_ExportTable3 字段分析完成: {'BSM': 'VARCHAR(255)', 'YSDM': 'VARCHAR(255)', 'XZQDM': 'VARCHAR(255)', 'XZQMC': 'VARCHAR(255)', 'DKBH': 'VARCHAR(255)', 'CSLXKZMJ': 'DECIMAL(15,6)', 'CSLVXKZMJ': 'DECIMAL(15,6)', 'CSHXKZMJ': 'DECIMAL(10,4)', 'CSZXKZMJ': 'DECIMAL(10,4)', 'YDMJ': 'DECIMAL(10,4)', 'DXKJGH': 'VARCHAR(255)', 'BZ': 'VARCHAR(255)', 'DYBH': 'VARCHAR(255)', 'DYMC': 'VARCHAR(255)', 'JQBH': 'VARCHAR(255)', 'JQMC': 'VARCHAR(255)', 'YDDM': 'VARCHAR(255)', 'YDMC': 'VARCHAR(255)', 'HHYD': 'VARCHAR(255)', 'ZJZMJ': 'DECIMAL(10,4)', 'SHAPE_Length': 'DECIMAL(18,6)', 'SHAPE_Area': 'DECIMAL(20,6)', 'RJLSX': 'DECIMAL(10,1)', 'JZMDSX': 'DECIMAL(10,1)', 'JZXGSX': 'DECIMAL(10,1)', 'LDLXX': 'DECIMAL(10,1)', 'NJLZL': 'DECIMAL(10,1)', 'PTSS': 'VARCHAR(255)', 'QTKZ': 'VARCHAR(258)', 'TCBWPB': 'VARCHAR(255)', 'OBJECTID': 'TINYINT', 'Join_Count': 'TINYINT', 'TARGET_FID': 'TINYINT', 'BSM_1': 'VARCHAR(255)', 'SHAPE_Length_1': 'DECIMAL(18,6)', 'SHAPE_Area_1': 'DECIMAL(20,6)'}
2025-08-04 17:22:04,653 - INFO - 表 510903xx_sscmydbjgh_exporttable3 创建成功
2025-08-04 17:22:04,653 - INFO - 开始读取图层: SSCMYDBJGH_ExportTable3
2025-08-04 17:22:04,658 - INFO - 图层 SSCMYDBJGH_ExportTable3 包含 327 个要素
2025-08-04 17:22:04,658 - INFO - 图层字段: ['BSM', 'YSDM', 'XZQDM', 'XZQMC', 'DKBH', 'CSLXKZMJ', 'CSLVXKZMJ', 'CSHXKZMJ', 'CSZXKZMJ', 'YDMJ', 'DXKJGH', 'BZ', 'DYBH', 'DYMC', 'JQBH', 'JQMC', 'YDDM', 'YDMC', 'HHYD', 'ZJZMJ', 'SHAPE_Length', 'SHAPE_Area', 'RJLSX', 'JZMDSX', 'JZXGSX', 'LDLXX', 'NJLZL', 'PTSS', 'QTKZ', 'TCBWPB', 'OBJECTID', 'Join_Count', 'TARGET_FID', 'BSM_1', 'SHAPE_Length_1', 'SHAPE_Area_1']
2025-08-04 17:22:11,511 - INFO - 已导入 327/327 个要素
2025-08-04 17:22:11,512 - INFO - 图层 SSCMYDBJGH_ExportTable3 导入完成，共导入 327 个要素
2025-08-04 17:22:11,512 - INFO - 其中 327 个要素缺失geometry数据，已正常插入
2025-08-04 17:22:11,513 - INFO - ✓ 图层 'SSCMYDBJGH_ExportTable3' 成功导入到表 '510903xx_sscmydbjgh_exporttable3'
2025-08-04 17:22:11,513 - INFO - 
==================================================
2025-08-04 17:22:11,513 - INFO - 导入完成: 1/2 个图层成功
2025-08-04 17:22:11,513 - INFO - 创建的表: ['510903xx_sscmydbjgh_exporttable3']
2025-08-04 17:22:11,514 - INFO - 数据导入完成！
2025-08-04 17:22:51,210 - INFO - 开始GIS数据导入...
2025-08-04 17:22:51,211 - INFO - GDB路径: /Users/<USER>/Downloads/MyProject.gdb
2025-08-04 17:22:51,211 - INFO - 表前缀: 510903xx
2025-08-04 17:22:51,211 - INFO - 指定图层: ['SSCMYDBJGH_ExportTable']
2025-08-04 17:22:51,211 - INFO - 导入模式: 分表
2025-08-04 17:22:51,211 - INFO - 批量大小: 1000
2025-08-04 17:22:51,609 - INFO - 数据库连接成功
2025-08-04 17:22:51,663 - INFO - GDAL signalled an error: err_no=1, msg='PROJ: internal_proj_identify: /Users/<USER>/miniconda3/share/proj/proj.db contains DATABASE.LAYOUT.VERSION.MINOR = 2 whereas a number >= 3 is expected. It comes from another PROJ installation.'
2025-08-04 17:22:51,718 - INFO - GDAL signalled an error: err_no=1, msg='PROJ: internal_proj_identify: /Users/<USER>/miniconda3/share/proj/proj.db contains DATABASE.LAYOUT.VERSION.MINOR = 2 whereas a number >= 3 is expected. It comes from another PROJ installation.'
2025-08-04 17:22:51,740 - INFO - GDAL signalled an error: err_no=1, msg='PROJ: internal_proj_identify: /Users/<USER>/miniconda3/share/proj/proj.db contains DATABASE.LAYOUT.VERSION.MINOR = 2 whereas a number >= 3 is expected. It comes from another PROJ installation.'
2025-08-04 17:22:51,751 - INFO - 发现 9 个图层: ['县级行政区_FeatureToLine', '市级行政区_FeatureToLine', '县级行政区_FeatureToLine1', '县级行政区_FeatureToLine2', '经开区管辖范围线Polyli_FeatureToLine', 'ShorelineContr_FeatureToPoly', 'ShorelineContr_FeatureToPoly1', 'SSCMYDBJGH_ExportTable', 'SSCMYDBJGH_ExportTable3']
2025-08-04 17:22:51,751 - INFO - GDB中可用图层: ['县级行政区_FeatureToLine', '市级行政区_FeatureToLine', '县级行政区_FeatureToLine1', '县级行政区_FeatureToLine2', '经开区管辖范围线Polyli_FeatureToLine', 'ShorelineContr_FeatureToPoly', 'ShorelineContr_FeatureToPoly1', 'SSCMYDBJGH_ExportTable', 'SSCMYDBJGH_ExportTable3']
2025-08-04 17:22:51,751 - INFO - ✓ 图层 'SSCMYDBJGH_ExportTable' 已加入导入列表
2025-08-04 17:22:51,751 - INFO - 准备导入 1 个图层: ['SSCMYDBJGH_ExportTable']
2025-08-04 17:22:51,751 - INFO - 
==================================================
2025-08-04 17:22:51,752 - INFO - 正在处理图层: SSCMYDBJGH_ExportTable
2025-08-04 17:22:51,752 - INFO - ==================================================
2025-08-04 17:22:51,752 - INFO - 目标表名: 510903xx_sscmydbjgh_exporttable
2025-08-04 17:22:51,851 - INFO - 图层 SSCMYDBJGH_ExportTable 字段分析完成: {'name': 'VARCHAR(255)', 'BSM': 'VARCHAR(255)', 'YSDM': 'VARCHAR(255)', 'XZQDM': 'VARCHAR(255)', 'XZQMC': 'VARCHAR(255)', 'XXGHBZDYBH': 'VARCHAR(255)', 'XXGHBZDYMC': 'VARCHAR(255)', 'DKBH': 'VARCHAR(255)', 'CSLXKZMJ': 'DECIMAL(15,6)', 'CSLVXKZMJ': 'DECIMAL(15,6)', 'CSHXKZMJ': 'DECIMAL(10,4)', 'CSZXKZMJ': 'DECIMAL(15,6)', 'YDYHFLDM': 'VARCHAR(255)', 'YDYHFLMC': 'VARCHAR(255)', 'YDMJ': 'DECIMAL(10,4)', 'DXKJGH': 'VARCHAR(255)', 'BZ': 'VARCHAR(255)', 'Shape_Length': 'DECIMAL(18,6)', 'Shape_Area': 'DECIMAL(20,6)', 'JQBH': 'VARCHAR(255)', 'JQMC': 'VARCHAR(255)', 'ZJZMJ': 'DECIMAL(10,4)', 'RJL': 'VARCHAR(255)', 'JZXG': 'VARCHAR(255)', 'LVDL': 'VARCHAR(255)', 'JZMD': 'VARCHAR(255)', 'ZZNJLZLKZL': 'VARCHAR(255)', 'XCSLDL': 'VARCHAR(255)', 'TSPZL': 'VARCHAR(255)', 'LSWDL': 'VARCHAR(255)', 'TCWPB': 'VARCHAR(255)', 'YDKBH': 'VARCHAR(255)', 'JDCCRK': 'VARCHAR(255)', 'JZKK': 'VARCHAR(255)', 'TJSFYCY': 'VARCHAR(255)', 'QTPTSFYCY': 'VARCHAR(255)', 'OBJECTID': 'TINYINT', 'Join_Count': 'TINYINT', 'TARGET_FID': 'SMALLINT', 'BSM_1': 'VARCHAR(255)', 'Shape_Length_1': 'DECIMAL(18,6)', 'Shape_Area_1': 'DECIMAL(20,6)'}
2025-08-04 17:22:52,026 - INFO - 表 510903xx_sscmydbjgh_exporttable 创建成功
2025-08-04 17:22:52,026 - INFO - 开始读取图层: SSCMYDBJGH_ExportTable
2025-08-04 17:22:52,037 - INFO - 图层 SSCMYDBJGH_ExportTable 包含 783 个要素
2025-08-04 17:22:52,037 - INFO - 图层字段: ['name', 'BSM', 'YSDM', 'XZQDM', 'XZQMC', 'XXGHBZDYBH', 'XXGHBZDYMC', 'DKBH', 'CSLXKZMJ', 'CSLVXKZMJ', 'CSHXKZMJ', 'CSZXKZMJ', 'YDYHFLDM', 'YDYHFLMC', 'YDMJ', 'DXKJGH', 'BZ', 'Shape_Length', 'Shape_Area', 'JQBH', 'JQMC', 'ZJZMJ', 'RJL', 'JZXG', 'LVDL', 'JZMD', 'ZZNJLZLKZL', 'XCSLDL', 'TSPZL', 'LSWDL', 'TCWPB', 'YDKBH', 'JDCCRK', 'JZKK', 'TJSFYCY', 'QTPTSFYCY', 'OBJECTID', 'Join_Count', 'TARGET_FID', 'BSM_1', 'Shape_Length_1', 'Shape_Area_1']
2025-08-04 17:22:59,570 - ERROR - 批量插入失败: (1264, "Out of range value for column 'join_count' at row 656")
2025-08-04 17:22:59,570 - ERROR - SQL: INSERT INTO `510903xx_sscmydbjgh_exporttable` (`id`, `layer_name`, `geometry_type`, `geometry_wkt`, `geometry_wkb`, `lng`, `lat`, `area`, `perimeter`, `source_file`, `import_time`, `del_flag`, `create_by`, `create_time`, `update_by`, `update_time`, `remark`, `name`, `bsm`, `ysdm`, `xzqdm`, `xzqmc`, `xxghbzdybh`, `xxghbzdymc`, `dkbh`, `cslxkzmj`, `cslvxkzmj`, `cshxkzmj`, `cszxkzmj`, `ydyhfldm`, `ydyhflmc`, `ydmj`, `dxkjgh`, `bz`, `shape_length`, `shape_area`, `jqbh`, `jqmc`, `zjzmj`, `rjl`, `jzxg`, `lvdl`, `jzmd`, `zznjlzlkzl`, `xcsldl`, `tspzl`, `lswdl`, `tcwpb`, `ydkbh`, `jdccrk`, `jzkk`, `tjsfycy`, `qtptsfycy`, `objectid`, `join_count`, `target_fid`, `bsm_1`, `shape_length_1`, `shape_area_1`) VALUES (%s, %s, %s, %s, %s, %s, %s, %s, %s, %s, %s, %s, %s, %s, %s, %s, %s, %s, %s, %s, %s, %s, %s, %s, %s, %s, %s, %s, %s, %s, %s, %s, %s, %s, %s, %s, %s, %s, %s, %s, %s, %s, %s, %s, %s, %s, %s, %s, %s, %s, %s, %s, %s, %s, %s, %s, %s, %s, %s)
2025-08-04 17:22:59,571 - ERROR - 记录样本: {'id': 'e756d712514c4d80907c8a96827dbc71', 'layer_name': 'SSCMYDBJGH_ExportTable', 'geometry_type': None, 'geometry_wkt': None, 'geometry_wkb': None, 'lng': None, 'lat': None, 'area': None, 'perimeter': None, 'source_file': '/Users/<USER>/Downloads/MyProject.gdb', 'import_time': '2025-08-04 17:22:52', 'del_flag': '0', 'create_by': 'system', 'create_time': '2025-08-04 17:22:52', 'update_by': 'system', 'update_time': '2025-08-04 17:22:52', 'remark': '从MyProject.gdb导入', 'name': '0903娱乐用地', 'bsm': None, 'ysdm': '2090020501', 'xzqdm': '510900', 'xzqmc': '遂宁市', 'xxghbzdybh': '51090000000HD03', 'xxghbzdymc': 'HD03', 'dkbh': 'HD03-03-07', 'cslxkzmj': None, 'cslvxkzmj': None, 'cshxkzmj': None, 'cszxkzmj': None, 'ydyhfldm': '0903', 'ydyhflmc': '娱乐用地', 'ydmj': 24757.83984375, 'dxkjgh': '一般开发区域', 'bz': '已批规划', 'shape_length': 616.1477884887105, 'shape_area': 24759.717620558873, 'jqbh': 'HD03-03', 'jqmc': 'HD03-03', 'zjzmj': 19806.271484375, 'rjl': '0.8', 'jzxg': '24', 'lvdl': '30', 'jzmd': '50', 'zznjlzlkzl': '80', 'xcsldl': '40', 'tspzl': '60', 'lswdl': '50', 'tcwpb': '-', 'ydkbh': 'HD03-03-05', 'jdccrk': '', 'jzkk': '', 'tjsfycy': '', 'qtptsfycy': '原设计条件未配公厕，\n原设计条件标注用途剧院会议', 'objectid': 1, 'join_count': 3, 'target_fid': 1014, 'bsm_1': '510900000000003018', 'shape_length_1': 616.1477884887105, 'shape_area_1': 24759.717620558873}
2025-08-04 17:22:59,633 - ERROR - 导入图层 SSCMYDBJGH_ExportTable 失败: (1264, "Out of range value for column 'join_count' at row 656")
2025-08-04 17:22:59,636 - ERROR - ✗ 图层 'SSCMYDBJGH_ExportTable' 导入失败
2025-08-04 17:22:59,636 - INFO - 
==================================================
2025-08-04 17:22:59,636 - INFO - 导入完成: 0/1 个图层成功
2025-08-04 17:22:59,636 - INFO - 创建的表: []
2025-08-04 17:22:59,636 - INFO - 数据导入完成！
2025-08-04 17:23:42,281 - INFO - 开始GIS数据导入...
2025-08-04 17:23:42,281 - INFO - GDB路径: /Users/<USER>/Downloads/MyProject.gdb
2025-08-04 17:23:42,281 - INFO - 表前缀: 510903xx
2025-08-04 17:23:42,281 - INFO - 指定图层: ['SSCMYDBJGH_ExportTable']
2025-08-04 17:23:42,281 - INFO - 导入模式: 分表
2025-08-04 17:23:42,281 - INFO - 批量大小: 1000
2025-08-04 17:23:43,010 - INFO - 数据库连接成功
2025-08-04 17:23:43,053 - INFO - GDAL signalled an error: err_no=1, msg='PROJ: internal_proj_identify: /Users/<USER>/miniconda3/share/proj/proj.db contains DATABASE.LAYOUT.VERSION.MINOR = 2 whereas a number >= 3 is expected. It comes from another PROJ installation.'
2025-08-04 17:23:43,105 - INFO - GDAL signalled an error: err_no=1, msg='PROJ: internal_proj_identify: /Users/<USER>/miniconda3/share/proj/proj.db contains DATABASE.LAYOUT.VERSION.MINOR = 2 whereas a number >= 3 is expected. It comes from another PROJ installation.'
2025-08-04 17:23:43,126 - INFO - GDAL signalled an error: err_no=1, msg='PROJ: internal_proj_identify: /Users/<USER>/miniconda3/share/proj/proj.db contains DATABASE.LAYOUT.VERSION.MINOR = 2 whereas a number >= 3 is expected. It comes from another PROJ installation.'
2025-08-04 17:23:43,139 - INFO - 发现 9 个图层: ['县级行政区_FeatureToLine', '市级行政区_FeatureToLine', '县级行政区_FeatureToLine1', '县级行政区_FeatureToLine2', '经开区管辖范围线Polyli_FeatureToLine', 'ShorelineContr_FeatureToPoly', 'ShorelineContr_FeatureToPoly1', 'SSCMYDBJGH_ExportTable', 'SSCMYDBJGH_ExportTable3']
2025-08-04 17:23:43,139 - INFO - GDB中可用图层: ['县级行政区_FeatureToLine', '市级行政区_FeatureToLine', '县级行政区_FeatureToLine1', '县级行政区_FeatureToLine2', '经开区管辖范围线Polyli_FeatureToLine', 'ShorelineContr_FeatureToPoly', 'ShorelineContr_FeatureToPoly1', 'SSCMYDBJGH_ExportTable', 'SSCMYDBJGH_ExportTable3']
2025-08-04 17:23:43,139 - INFO - ✓ 图层 'SSCMYDBJGH_ExportTable' 已加入导入列表
2025-08-04 17:23:43,139 - INFO - 准备导入 1 个图层: ['SSCMYDBJGH_ExportTable']
2025-08-04 17:23:43,139 - INFO - 
==================================================
2025-08-04 17:23:43,139 - INFO - 正在处理图层: SSCMYDBJGH_ExportTable
2025-08-04 17:23:43,139 - INFO - ==================================================
2025-08-04 17:23:43,139 - INFO - 目标表名: 510903xx_sscmydbjgh_exporttable
2025-08-04 17:23:43,241 - INFO - 图层 SSCMYDBJGH_ExportTable 字段分析完成: {'name': 'VARCHAR(255)', 'BSM': 'VARCHAR(255)', 'YSDM': 'VARCHAR(255)', 'XZQDM': 'VARCHAR(255)', 'XZQMC': 'VARCHAR(255)', 'XXGHBZDYBH': 'VARCHAR(255)', 'XXGHBZDYMC': 'VARCHAR(255)', 'DKBH': 'VARCHAR(255)', 'CSLXKZMJ': 'DECIMAL(15,6)', 'CSLVXKZMJ': 'DECIMAL(15,6)', 'CSHXKZMJ': 'DECIMAL(10,4)', 'CSZXKZMJ': 'DECIMAL(15,6)', 'YDYHFLDM': 'VARCHAR(255)', 'YDYHFLMC': 'VARCHAR(255)', 'YDMJ': 'DECIMAL(10,4)', 'DXKJGH': 'VARCHAR(255)', 'BZ': 'VARCHAR(255)', 'Shape_Length': 'DECIMAL(18,6)', 'Shape_Area': 'DECIMAL(20,6)', 'JQBH': 'VARCHAR(255)', 'JQMC': 'VARCHAR(255)', 'ZJZMJ': 'DECIMAL(10,4)', 'RJL': 'VARCHAR(255)', 'JZXG': 'VARCHAR(255)', 'LVDL': 'VARCHAR(255)', 'JZMD': 'VARCHAR(255)', 'ZZNJLZLKZL': 'VARCHAR(255)', 'XCSLDL': 'VARCHAR(255)', 'TSPZL': 'VARCHAR(255)', 'LSWDL': 'VARCHAR(255)', 'TCWPB': 'VARCHAR(255)', 'YDKBH': 'VARCHAR(255)', 'JDCCRK': 'VARCHAR(255)', 'JZKK': 'VARCHAR(255)', 'TJSFYCY': 'VARCHAR(255)', 'QTPTSFYCY': 'VARCHAR(255)', 'OBJECTID': 'TINYINT', 'Join_Count': 'TINYINT', 'TARGET_FID': 'SMALLINT', 'BSM_1': 'VARCHAR(255)', 'Shape_Length_1': 'DECIMAL(18,6)', 'Shape_Area_1': 'DECIMAL(20,6)'}
2025-08-04 17:23:43,406 - INFO - 表 510903xx_sscmydbjgh_exporttable 创建成功
2025-08-04 17:23:43,407 - INFO - 开始读取图层: SSCMYDBJGH_ExportTable
2025-08-04 17:23:43,417 - INFO - 图层 SSCMYDBJGH_ExportTable 包含 783 个要素
2025-08-04 17:23:43,418 - INFO - 图层字段: ['name', 'BSM', 'YSDM', 'XZQDM', 'XZQMC', 'XXGHBZDYBH', 'XXGHBZDYMC', 'DKBH', 'CSLXKZMJ', 'CSLVXKZMJ', 'CSHXKZMJ', 'CSZXKZMJ', 'YDYHFLDM', 'YDYHFLMC', 'YDMJ', 'DXKJGH', 'BZ', 'Shape_Length', 'Shape_Area', 'JQBH', 'JQMC', 'ZJZMJ', 'RJL', 'JZXG', 'LVDL', 'JZMD', 'ZZNJLZLKZL', 'XCSLDL', 'TSPZL', 'LSWDL', 'TCWPB', 'YDKBH', 'JDCCRK', 'JZKK', 'TJSFYCY', 'QTPTSFYCY', 'OBJECTID', 'Join_Count', 'TARGET_FID', 'BSM_1', 'Shape_Length_1', 'Shape_Area_1']
2025-08-04 17:23:52,297 - INFO - 已导入 783/783 个要素
2025-08-04 17:23:52,298 - INFO - 图层 SSCMYDBJGH_ExportTable 导入完成，共导入 783 个要素
2025-08-04 17:23:52,298 - INFO - 其中 783 个要素缺失geometry数据，已正常插入
2025-08-04 17:23:52,301 - INFO - ✓ 图层 'SSCMYDBJGH_ExportTable' 成功导入到表 '510903xx_sscmydbjgh_exporttable'
2025-08-04 17:23:52,301 - INFO - 
==================================================
2025-08-04 17:23:52,301 - INFO - 导入完成: 1/1 个图层成功
2025-08-04 17:23:52,301 - INFO - 创建的表: ['510903xx_sscmydbjgh_exporttable']
2025-08-04 17:23:52,302 - INFO - 数据导入完成！
2025-08-04 17:27:55,895 - INFO - 正在读取GDB文件: /Users/<USER>/Downloads/MyProject.gdb
2025-08-04 17:27:56,797 - INFO - 数据库连接成功
2025-08-04 17:27:56,840 - INFO - GDAL signalled an error: err_no=1, msg='PROJ: internal_proj_identify: /Users/<USER>/miniconda3/share/proj/proj.db contains DATABASE.LAYOUT.VERSION.MINOR = 2 whereas a number >= 3 is expected. It comes from another PROJ installation.'
2025-08-04 17:27:56,893 - INFO - GDAL signalled an error: err_no=1, msg='PROJ: internal_proj_identify: /Users/<USER>/miniconda3/share/proj/proj.db contains DATABASE.LAYOUT.VERSION.MINOR = 2 whereas a number >= 3 is expected. It comes from another PROJ installation.'
2025-08-04 17:27:56,913 - INFO - GDAL signalled an error: err_no=1, msg='PROJ: internal_proj_identify: /Users/<USER>/miniconda3/share/proj/proj.db contains DATABASE.LAYOUT.VERSION.MINOR = 2 whereas a number >= 3 is expected. It comes from another PROJ installation.'
2025-08-04 17:27:56,925 - INFO - 发现 9 个图层: ['县级行政区_FeatureToLine', '市级行政区_FeatureToLine', '县级行政区_FeatureToLine1', '县级行政区_FeatureToLine2', '经开区管辖范围线Polyli_FeatureToLine', 'ShorelineContr_FeatureToPoly', 'ShorelineContr_FeatureToPoly1', 'SSCMYDBJGH_ExportTable', 'SSCMYDBJGH_ExportTable3']
2025-08-04 17:27:56,925 - INFO - 发现 9 个图层:
2025-08-04 17:28:21,667 - INFO - 正在读取GDB文件: /Users/<USER>/Downloads/MyProject.gdb
2025-08-04 17:28:22,300 - INFO - 数据库连接成功
2025-08-04 17:28:22,342 - INFO - GDAL signalled an error: err_no=1, msg='PROJ: internal_proj_identify: /Users/<USER>/miniconda3/share/proj/proj.db contains DATABASE.LAYOUT.VERSION.MINOR = 2 whereas a number >= 3 is expected. It comes from another PROJ installation.'
2025-08-04 17:28:22,395 - INFO - GDAL signalled an error: err_no=1, msg='PROJ: internal_proj_identify: /Users/<USER>/miniconda3/share/proj/proj.db contains DATABASE.LAYOUT.VERSION.MINOR = 2 whereas a number >= 3 is expected. It comes from another PROJ installation.'
2025-08-04 17:28:22,416 - INFO - GDAL signalled an error: err_no=1, msg='PROJ: internal_proj_identify: /Users/<USER>/miniconda3/share/proj/proj.db contains DATABASE.LAYOUT.VERSION.MINOR = 2 whereas a number >= 3 is expected. It comes from another PROJ installation.'
2025-08-04 17:28:22,429 - INFO - 发现 9 个图层: ['县级行政区_FeatureToLine', '市级行政区_FeatureToLine', '县级行政区_FeatureToLine1', '县级行政区_FeatureToLine2', '经开区管辖范围线Polyli_FeatureToLine', 'ShorelineContr_FeatureToPoly', 'ShorelineContr_FeatureToPoly1', 'SSCMYDBJGH_ExportTable', 'SSCMYDBJGH_ExportTable3']
2025-08-04 17:28:22,429 - INFO - 发现 9 个图层:
2025-08-04 17:29:53,159 - INFO - 正在读取GDB文件: /Users/<USER>/Downloads/MyProject.gdb
2025-08-04 17:29:53,594 - INFO - 数据库连接成功
2025-08-04 17:29:53,636 - INFO - GDAL signalled an error: err_no=1, msg='PROJ: internal_proj_identify: /Users/<USER>/miniconda3/share/proj/proj.db contains DATABASE.LAYOUT.VERSION.MINOR = 2 whereas a number >= 3 is expected. It comes from another PROJ installation.'
2025-08-04 17:29:53,689 - INFO - GDAL signalled an error: err_no=1, msg='PROJ: internal_proj_identify: /Users/<USER>/miniconda3/share/proj/proj.db contains DATABASE.LAYOUT.VERSION.MINOR = 2 whereas a number >= 3 is expected. It comes from another PROJ installation.'
2025-08-04 17:29:53,710 - INFO - GDAL signalled an error: err_no=1, msg='PROJ: internal_proj_identify: /Users/<USER>/miniconda3/share/proj/proj.db contains DATABASE.LAYOUT.VERSION.MINOR = 2 whereas a number >= 3 is expected. It comes from another PROJ installation.'
2025-08-04 17:29:53,721 - INFO - 发现 9 个图层: ['县级行政区_FeatureToLine', '市级行政区_FeatureToLine', '县级行政区_FeatureToLine1', '县级行政区_FeatureToLine2', '经开区管辖范围线Polyli_FeatureToLine', 'ShorelineContr_FeatureToPoly', 'ShorelineContr_FeatureToPoly1', 'SSCMYDBJGH_ExportTable', 'SSCMYDBJGH_ExportTable3']
2025-08-04 17:29:53,722 - INFO - 发现 9 个图层:
2025-08-04 17:31:55,153 - INFO - 正在读取GDB文件: /Users/<USER>/Downloads/MyProject.gdb
2025-08-04 17:31:56,033 - INFO - 数据库连接成功
2025-08-04 17:31:56,081 - INFO - GDAL signalled an error: err_no=1, msg='PROJ: internal_proj_identify: /Users/<USER>/miniconda3/share/proj/proj.db contains DATABASE.LAYOUT.VERSION.MINOR = 2 whereas a number >= 3 is expected. It comes from another PROJ installation.'
2025-08-04 17:31:56,134 - INFO - GDAL signalled an error: err_no=1, msg='PROJ: internal_proj_identify: /Users/<USER>/miniconda3/share/proj/proj.db contains DATABASE.LAYOUT.VERSION.MINOR = 2 whereas a number >= 3 is expected. It comes from another PROJ installation.'
2025-08-04 17:31:56,158 - INFO - GDAL signalled an error: err_no=1, msg='PROJ: internal_proj_identify: /Users/<USER>/miniconda3/share/proj/proj.db contains DATABASE.LAYOUT.VERSION.MINOR = 2 whereas a number >= 3 is expected. It comes from another PROJ installation.'
2025-08-04 17:31:56,169 - INFO - 发现 9 个图层: ['县级行政区_FeatureToLine', '市级行政区_FeatureToLine', '县级行政区_FeatureToLine1', '县级行政区_FeatureToLine2', '经开区管辖范围线Polyli_FeatureToLine', 'ShorelineContr_FeatureToPoly', 'ShorelineContr_FeatureToPoly1', 'SSCMYDBJGH_ExportTable', 'SSCMYDBJGH_ExportTable3']
2025-08-04 17:31:56,170 - INFO - 发现 9 个图层:
