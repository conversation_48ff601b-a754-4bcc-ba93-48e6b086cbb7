#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
GIS数据导入工具配置文件
用户可以在这里修改数据库连接和默认设置
"""

import os

# =============================================================================
# 数据库配置
# =============================================================================

# 主数据库配置
DB_CONFIG = {
    'host': '*************',
    'port': 9140,
    'user': 'suining',
    'password': 'Smart@123.',
    'database': 'db_sn_ecology_cloud'
}

# 测试数据库配置（可选）
TEST_DB_CONFIG = {
    'host': 'localhost',
    'port': 3306,
    'user': 'root',
    'password': 'password',
    'database': 'test_gis'
}

# =============================================================================
# 文件路径配置
# =============================================================================

# 默认GDB文件路径
DEFAULT_GDB_PATH = '/Users/<USER>/Downloads/MyProject.gdb'

# 默认表名前缀
DEFAULT_TABLE_PREFIX = '510903xx'

# 日志文件路径
LOG_FILE_PATH = 'gis_import.log'

# =============================================================================
# 导入设置
# =============================================================================

# 默认批量插入大小
DEFAULT_BATCH_SIZE = 1000

# 是否默认创建分表（True）还是统一表（False）
DEFAULT_CREATE_SEPARATE_TABLES = True

# 默认日志级别 ('DEBUG', 'INFO', 'WARNING', 'ERROR')
DEFAULT_LOG_LEVEL = 'INFO'

# =============================================================================
# 字段映射配置
# =============================================================================

# 特殊字段名映射（用于处理特殊字符或保留字）
FIELD_NAME_MAPPING = {
    # 原字段名: 目标字段名
    'class': 'class_type',  # class是MySQL保留字
    'order': 'order_num',   # order是MySQL保留字
    'group': 'group_name',  # group是MySQL保留字
    'index': 'index_num',   # index是MySQL保留字
}

# 需要忽略的字段（这些字段不会被导入）
IGNORED_FIELDS = [
    'OBJECTID',      # ArcGIS内部ID
    'SHAPE_Length',  # 自动计算的长度
    'SHAPE_Area',    # 自动计算的面积
    'GlobalID',      # 全局唯一标识符
]

# =============================================================================
# 坐标系配置
# =============================================================================

# 目标坐标系（导入后统一转换为此坐标系）
TARGET_CRS = 'EPSG:4326'  # WGS84地理坐标系

# 常用坐标系映射
CRS_MAPPING = {
    # 中国常用坐标系
    'CGCS2000': 'EPSG:4490',
    'Beijing54': 'EPSG:4214',
    'Xian80': 'EPSG:4610',
    'WGS84': 'EPSG:4326',
    
    # 投影坐标系示例
    'CGCS2000_3_Degree_GK_Zone_38': 'EPSG:4548',
    'CGCS2000_3_Degree_GK_Zone_39': 'EPSG:4549',
    'CGCS2000_3_Degree_GK_Zone_40': 'EPSG:4550',
}

# =============================================================================
# 表结构配置
# =============================================================================

# 系统字段配置
SYSTEM_FIELDS = {
    'id': {
        'type': 'varchar(64)',
        'null': False,
        'comment': '主键ID'
    },
    'layer_name': {
        'type': 'varchar(255)',
        'null': False,
        'comment': '图层名称'
    },
    'geometry_type': {
        'type': 'varchar(50)',
        'null': True,
        'comment': '几何类型(POINT/LINESTRING/POLYGON)'
    },
    'geometry_wkt': {
        'type': 'LONGTEXT',
        'null': True,
        'comment': '几何数据(WKT格式)'
    },
    'geometry_wkb': {
        'type': 'LONGBLOB',
        'null': True,
        'comment': '几何数据(WKB格式)'
    },
    'lng': {
        'type': 'decimal(12,8)',
        'null': True,
        'comment': '经度'
    },
    'lat': {
        'type': 'decimal(12,8)',
        'null': True,
        'comment': '纬度'
    },
    'area': {
        'type': 'decimal(20,6)',
        'null': True,
        'comment': '面积(平方米)'
    },
    'perimeter': {
        'type': 'decimal(18,6)',
        'null': True,
        'comment': '周长(米)'
    }
}

# 管理字段配置
MANAGEMENT_FIELDS = {
    'source_file': {
        'type': 'varchar(500)',
        'null': True,
        'comment': '源文件路径'
    },
    'import_time': {
        'type': 'datetime',
        'null': True,
        'comment': '导入时间'
    },
    'del_flag': {
        'type': "char(1) DEFAULT '0'",
        'null': True,
        'comment': '删除标志(0存在 1删除)'
    },
    'create_by': {
        'type': "varchar(64) DEFAULT 'system'",
        'null': True,
        'comment': '创建者'
    },
    'create_time': {
        'type': 'datetime DEFAULT CURRENT_TIMESTAMP',
        'null': True,
        'comment': '创建时间'
    },
    'update_by': {
        'type': "varchar(64) DEFAULT 'system'",
        'null': True,
        'comment': '更新者'
    },
    'update_time': {
        'type': 'datetime DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP',
        'null': True,
        'comment': '更新时间'
    },
    'remark': {
        'type': 'varchar(500)',
        'null': True,
        'comment': '备注'
    }
}

# =============================================================================
# 性能优化配置
# =============================================================================

# 数据库连接池配置
CONNECTION_POOL_CONFIG = {
    'pool_size': 5,
    'max_overflow': 10,
    'pool_timeout': 30,
    'pool_recycle': 3600
}

# 批量处理配置
BATCH_CONFIG = {
    'default_batch_size': 1000,
    'max_batch_size': 5000,
    'memory_limit_mb': 512  # 内存限制（MB）
}

# =============================================================================
# 环境变量支持
# =============================================================================

def get_config_from_env():
    """从环境变量获取配置，覆盖默认配置"""
    
    # 数据库配置
    if os.getenv('DB_HOST'):
        DB_CONFIG['host'] = os.getenv('DB_HOST')
    if os.getenv('DB_PORT'):
        DB_CONFIG['port'] = int(os.getenv('DB_PORT'))
    if os.getenv('DB_USER'):
        DB_CONFIG['user'] = os.getenv('DB_USER')
    if os.getenv('DB_PASSWORD'):
        DB_CONFIG['password'] = os.getenv('DB_PASSWORD')
    if os.getenv('DB_DATABASE'):
        DB_CONFIG['database'] = os.getenv('DB_DATABASE')
    
    # 文件路径配置
    global DEFAULT_GDB_PATH, DEFAULT_TABLE_PREFIX
    if os.getenv('GDB_PATH'):
        DEFAULT_GDB_PATH = os.getenv('GDB_PATH')
    if os.getenv('TABLE_PREFIX'):
        DEFAULT_TABLE_PREFIX = os.getenv('TABLE_PREFIX')

# 初始化时从环境变量加载配置
get_config_from_env()

# =============================================================================
# 配置验证
# =============================================================================

def validate_config():
    """验证配置的有效性"""
    errors = []
    
    # 验证数据库配置
    required_db_keys = ['host', 'port', 'user', 'password', 'database']
    for key in required_db_keys:
        if key not in DB_CONFIG or not DB_CONFIG[key]:
            errors.append(f"数据库配置缺少必需的参数: {key}")
    
    # 验证端口号
    if not isinstance(DB_CONFIG.get('port'), int) or DB_CONFIG['port'] <= 0:
        errors.append("数据库端口必须是正整数")
    
    # 验证批量大小
    if DEFAULT_BATCH_SIZE <= 0:
        errors.append("批量大小必须是正整数")
    
    return errors

# =============================================================================
# 配置信息显示
# =============================================================================

def print_config_info():
    """打印当前配置信息"""
    print("=" * 60)
    print("GIS数据导入工具 - 当前配置")
    print("=" * 60)
    print(f"数据库主机: {DB_CONFIG['host']}:{DB_CONFIG['port']}")
    print(f"数据库名称: {DB_CONFIG['database']}")
    print(f"数据库用户: {DB_CONFIG['user']}")
    print(f"默认GDB路径: {DEFAULT_GDB_PATH}")
    print(f"默认表前缀: {DEFAULT_TABLE_PREFIX}")
    print(f"批量大小: {DEFAULT_BATCH_SIZE}")
    print(f"日志级别: {DEFAULT_LOG_LEVEL}")
    print(f"目标坐标系: {TARGET_CRS}")
    print("=" * 60)

if __name__ == '__main__':
    # 验证配置
    errors = validate_config()
    if errors:
        print("配置验证失败:")
        for error in errors:
            print(f"  - {error}")
    else:
        print("配置验证通过")
    
    # 显示配置信息
    print_config_info()
