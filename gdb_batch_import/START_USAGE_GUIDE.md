# GIS数据导入工具使用指南

修改后的`start.py`支持指定处理特定图层，提供了更灵活的导入选项。

## 🚀 快速开始

### 1. 列出所有可用图层

```bash
python start.py --list-layers
```

这将显示GDB文件中所有可用的图层：

```
2024-01-01 10:00:00 - INFO - 正在读取GDB文件: /Users/<USER>/Downloads/MyProject.gdb
2024-01-01 10:00:01 - INFO - 发现 5 个图层:
   1. 行政区划
   2. 道路网络
   3. 建筑物
   4. 水系
   5. 土地利用
```

### 2. 导入所有图层（默认行为）

```bash
python start.py
```

这将导入GDB中的所有图层，为每个图层创建单独的表。

### 3. 导入指定的图层

```bash
python start.py --layers "行政区划" "道路网络" "建筑物"
```

只导入指定的图层，忽略其他图层。

### 4. 导入到统一表

```bash
python start.py --layers "行政区划" "道路网络" --unified-table
```

将指定的图层导入到同一个表中。

## 📋 命令行参数详解

### 基本参数

| 参数 | 说明 | 默认值 | 示例 |
|------|------|--------|------|
| `--gdb-path` | GDB文件路径 | `/Users/<USER>/Downloads/MyProject.gdb` | `--gdb-path "/path/to/your.gdb"` |
| `--table-prefix` | 表名前缀 | `510903xx` | `--table-prefix "custom_prefix"` |
| `--layers` | 要导入的图层列表 | 所有图层 | `--layers "图层1" "图层2"` |

### 功能参数

| 参数 | 说明 | 示例 |
|------|------|------|
| `--list-layers` | 列出所有可用图层 | `--list-layers` |
| `--unified-table` | 导入到统一表 | `--unified-table` |
| `--verbose` | 显示详细日志 | `--verbose` |

## 💡 使用示例

### 示例1：探索GDB内容

```bash
# 首先查看有哪些图层
python start.py --list-layers

# 输出示例：
# 发现 8 个图层:
#    1. administrative_boundary
#    2. road_network
#    3. building_footprint
#    4. water_body
#    5. land_use
#    6. poi_points
#    7. elevation_contour
#    8. vegetation_area
```

### 示例2：选择性导入

```bash
# 只导入行政边界和道路网络
python start.py --layers "administrative_boundary" "road_network"

# 使用自定义表前缀
python start.py --table-prefix "beijing_data" --layers "building_footprint" "poi_points"
```

### 示例3：统一表导入

```bash
# 将多个相似图层导入到同一个表
python start.py --layers "poi_points" "landmark_points" --unified-table

# 导入所有图层到统一表
python start.py --unified-table
```

### 示例4：使用不同的GDB文件

```bash
# 处理不同的GDB文件
python start.py --gdb-path "/data/shanghai.gdb" --table-prefix "shanghai" --layers "district" "subway"
```

### 示例5：详细日志模式

```bash
# 启用详细日志，便于调试
python start.py --verbose --layers "complex_geometry_layer"
```

## 🔧 高级用法

### 批量处理多个图层

```bash
# 处理所有包含"boundary"的图层（需要先用--list-layers查看确切名称）
python start.py --layers "city_boundary" "district_boundary" "county_boundary"
```

### 分步骤导入

```bash
# 第一步：导入基础地理数据
python start.py --layers "administrative_boundary" "water_body" --table-prefix "base_geo"

# 第二步：导入POI数据
python start.py --layers "poi_points" "landmark_points" --table-prefix "poi_data"

# 第三步：导入交通数据
python start.py --layers "road_network" "subway_line" --table-prefix "transport"
```

## 📊 导入结果

### 分表模式（默认）
每个图层创建一个单独的表：
- `510903xx_administrative_boundary`
- `510903xx_road_network`
- `510903xx_building_footprint`

### 统一表模式
所有图层导入到一个表：
- `510903xx_unified`

表中会有`layer_name`字段标识数据来源图层。

## ⚠️ 注意事项

1. **图层名称大小写敏感**：请使用`--list-layers`获取准确的图层名称
2. **表名规范**：图层名称会自动转换为符合MySQL规范的表名
3. **数据库连接**：确保数据库配置正确且可连接
4. **磁盘空间**：大型GDB文件导入前请确保有足够的磁盘空间
5. **内存使用**：大图层可能消耗较多内存，建议分批处理

## 🐛 故障排除

### 常见问题

1. **图层不存在错误**
   ```
   ✗ 图层 'wrong_name' 不存在，将被跳过
   ```
   解决：使用`--list-layers`查看正确的图层名称

2. **数据库连接失败**
   ```
   数据库连接失败: (2003, "Can't connect to MySQL server")
   ```
   解决：检查数据库配置和网络连接

3. **GDB文件不存在**
   ```
   GDB文件不存在: /path/to/file.gdb
   ```
   解决：检查文件路径是否正确

### 日志文件

导入过程中的详细日志会保存到`gis_import.log`文件中，可以用于问题诊断。

## 🔄 与原版本的区别

| 功能 | 原版本 | 新版本 |
|------|--------|--------|
| 图层选择 | 导入所有图层 | ✅ 支持指定图层 |
| 命令行参数 | 无 | ✅ 丰富的参数选项 |
| 图层预览 | 无 | ✅ --list-layers功能 |
| 导入模式 | 仅分表 | ✅ 分表/统一表可选 |
| 错误处理 | 基础 | ✅ 增强的错误处理 |
| 日志记录 | 基础 | ✅ 详细的进度日志 |

修改后的版本提供了更好的用户体验和更灵活的导入选项！
