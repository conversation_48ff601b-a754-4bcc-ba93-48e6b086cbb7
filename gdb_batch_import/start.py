#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
GIS数据导入启动脚本
支持指定处理特定图层
"""

import os
import sys
import argparse
import logging
from typing import List, Optional
from gis_data_import import GISDataImporter

# 导入配置
try:
    from config import (
        DB_CONFIG, DEFAULT_GDB_PATH, DEFAULT_TABLE_PREFIX,
        LOG_FILE_PATH, DEFAULT_LOG_LEVEL, DEFAULT_BATCH_SIZE,
        validate_config
    )
except ImportError:
    # 如果没有配置文件，使用默认配置
    print("警告: 未找到config.py配置文件，使用内置默认配置")
    DB_CONFIG = {
        'host': '*************',
        'port': 9140,
        'user': 'suining',
        'password': 'Smart@123.',
        'database': 'db_sn_ecology_cloud'
    }
    DEFAULT_GDB_PATH = '/Users/<USER>/Downloads/222/510900遂宁市河东二期实施层面详细规划成果.gdb'
    DEFAULT_TABLE_PREFIX = '5109033333'
    LOG_FILE_PATH = 'gis_import.log'
    DEFAULT_LOG_LEVEL = 'INFO'
    DEFAULT_BATCH_SIZE = 1000

    def validate_config():
        return []

# 配置日志
def setup_logging(log_level='INFO'):
    """设置日志配置"""
    level = getattr(logging, log_level.upper(), logging.INFO)
    logging.basicConfig(
        level=level,
        format='%(asctime)s - %(levelname)s - %(message)s',
        handlers=[
            logging.FileHandler(LOG_FILE_PATH, encoding='utf-8'),
            logging.StreamHandler()
        ]
    )

# 初始化日志
setup_logging(DEFAULT_LOG_LEVEL)
logger = logging.getLogger(__name__)


def list_available_layers(gdb_path: str) -> List[str]:
    """
    列出GDB中可用的图层

    Args:
        gdb_path: GDB文件路径

    Returns:
        List[str]: 图层名称列表
    """
    try:
        importer = GISDataImporter(DB_CONFIG)
        layers = importer.read_gdb_layers(gdb_path)
        importer.close()
        return layers
    except Exception as e:
        logger.error(f"获取图层列表失败: {e}")
        return []


def import_specific_layers(gdb_path: str, table_prefix: str,
                          target_layers: Optional[List[str]] = None,
                          create_separate_tables: bool = True,
                          batch_size: int = DEFAULT_BATCH_SIZE) -> bool:
    """
    导入指定的图层

    Args:
        gdb_path: GDB文件路径
        table_prefix: 表名前缀
        target_layers: 要导入的图层列表，None表示导入所有图层
        create_separate_tables: 是否为每个图层创建单独的表
        batch_size: 批量插入大小

    Returns:
        bool: 导入是否成功
    """
    try:
        # 创建导入器
        importer = GISDataImporter(DB_CONFIG)

        # 获取所有可用图层
        all_layers = importer.read_gdb_layers(gdb_path)
        if not all_layers:
            logger.error("未发现任何图层")
            return False

        logger.info(f"GDB中可用图层: {all_layers}")

        # 确定要导入的图层
        if target_layers is None:
            # 导入所有图层
            layers_to_import = all_layers
            logger.info("将导入所有图层")
        else:
            # 验证指定的图层是否存在
            layers_to_import = []
            for layer in target_layers:
                if layer in all_layers:
                    layers_to_import.append(layer)
                    logger.info(f"✓ 图层 '{layer}' 已加入导入列表")
                else:
                    logger.warning(f"✗ 图层 '{layer}' 不存在，将被跳过")

            if not layers_to_import:
                logger.error("没有有效的图层可以导入")
                return False

        logger.info(f"准备导入 {len(layers_to_import)} 个图层: {layers_to_import}")

        if create_separate_tables:
            # 为每个图层创建单独的表
            success_count = 0
            created_tables = []

            for layer_name in layers_to_import:
                logger.info(f"\n{'='*50}")
                logger.info(f"正在处理图层: {layer_name}")
                logger.info(f"{'='*50}")

                # 清理图层名，生成表名
                clean_layer_name = importer._sanitize_table_name(layer_name)
                layer_table_name = f"{table_prefix}_{clean_layer_name}"

                logger.info(f"目标表名: {layer_table_name}")

                # 根据图层字段动态创建表
                if importer.create_dynamic_table(layer_table_name, gdb_path, layer_name):
                    if importer.import_layer_data(gdb_path, layer_name, layer_table_name, batch_size):
                        success_count += 1
                        created_tables.append(layer_table_name)
                        logger.info(f"✓ 图层 '{layer_name}' 成功导入到表 '{layer_table_name}'")
                    else:
                        logger.error(f"✗ 图层 '{layer_name}' 导入失败")
                else:
                    logger.error(f"✗ 图层 '{layer_name}' 表创建失败")

            logger.info(f"\n{'='*50}")
            logger.info(f"导入完成: {success_count}/{len(layers_to_import)} 个图层成功")
            logger.info(f"创建的表: {created_tables}")

        else:
            # 所有图层导入到同一个表
            unified_table_name = f"{table_prefix}_unified"
            logger.info(f"将所有图层导入到统一表: {unified_table_name}")

            # 分析所有要导入图层的字段，创建统一的表结构
            all_fields = {}
            for layer_name in layers_to_import:
                layer_fields = importer.analyze_gdb_schema(gdb_path, layer_name)
                all_fields.update(layer_fields)

            # 创建包含所有字段的统一表
            if importer._create_unified_table(unified_table_name, all_fields):
                success_count = 0
                for layer_name in layers_to_import:
                    logger.info(f"正在导入图层: {layer_name}")
                    if importer.import_layer_data(gdb_path, layer_name, unified_table_name, batch_size):
                        success_count += 1
                    else:
                        logger.error(f"图层 {layer_name} 导入失败")

                logger.info(f"导入完成: {success_count}/{len(layers_to_import)} 个图层成功")
                logger.info(f"统一表名: {unified_table_name}")
            else:
                logger.error("统一表创建失败")
                return False

        # 关闭连接
        importer.close()
        return True

    except Exception as e:
        logger.error(f"导入过程失败: {e}")
        return False


def main():
    """主函数"""
    # 验证配置
    config_errors = validate_config()
    if config_errors:
        logger.error("配置验证失败:")
        for error in config_errors:
            logger.error(f"  - {error}")
        sys.exit(1)

    parser = argparse.ArgumentParser(
        description='GIS数据导入工具 - 支持指定处理特定图层',
        formatter_class=argparse.RawDescriptionHelpFormatter,
        epilog="""
使用示例:
  # 列出所有可用图层
  python start.py --list-layers

  # 导入所有图层（默认行为）
  python start.py

  # 导入指定的图层
  python start.py --layers "图层1" "图层2" "图层3"

  # 导入指定图层到统一表
  python start.py --layers "图层1" "图层2" --unified-table
python start.py --layers "SSCMYDBJGH_ExportTable" "SSCMYDBJGH_ExportTable3"
  # 使用自定义GDB路径和表前缀
  python start.py --gdb-path "/path/to/your.gdb" --table-prefix "custom_prefix" --layers "图层1"
        """
    )

    parser.add_argument(
        '--gdb-path',
        default=DEFAULT_GDB_PATH,
        help=f'GDB文件路径 (默认: {DEFAULT_GDB_PATH})'
    )

    parser.add_argument(
        '--table-prefix',
        default=DEFAULT_TABLE_PREFIX,
        help=f'表名前缀 (默认: {DEFAULT_TABLE_PREFIX})'
    )

    parser.add_argument(
        '--layers',
        nargs='*',
        help='要导入的图层名称列表，不指定则导入所有图层'
    )

    parser.add_argument(
        '--list-layers',
        action='store_true',
        help='列出GDB中所有可用的图层'
    )

    parser.add_argument(
        '--unified-table',
        action='store_true',
        help='将所有图层导入到同一个表中（默认为每个图层创建单独的表）'
    )

    parser.add_argument(
        '--verbose',
        action='store_true',
        help='显示详细日志'
    )

    parser.add_argument(
        '--batch-size',
        type=int,
        default=DEFAULT_BATCH_SIZE,
        help=f'批量插入大小 (默认: {DEFAULT_BATCH_SIZE})'
    )

    parser.add_argument(
        '--config-info',
        action='store_true',
        help='显示当前配置信息'
    )

    args = parser.parse_args()

    # 设置日志级别
    if args.verbose:
        setup_logging('DEBUG')
        logger.setLevel(logging.DEBUG)

    # 显示配置信息
    if args.config_info:
        try:
            from config import print_config_info
            print_config_info()
        except ImportError:
            logger.info("当前配置:")
            logger.info(f"  数据库: {DB_CONFIG['host']}:{DB_CONFIG['port']}")
            logger.info(f"  数据库名: {DB_CONFIG['database']}")
            logger.info(f"  默认GDB路径: {DEFAULT_GDB_PATH}")
            logger.info(f"  默认表前缀: {DEFAULT_TABLE_PREFIX}")
        return

    # 验证批量大小
    if args.batch_size <= 0:
        logger.error("批量大小必须是正整数")
        sys.exit(1)

    # 检查GDB文件是否存在
    if not os.path.exists(args.gdb_path):
        logger.error(f"GDB文件不存在: {args.gdb_path}")
        sys.exit(1)

    # 列出图层
    if args.list_layers:
        logger.info(f"正在读取GDB文件: {args.gdb_path}")
        layers = list_available_layers(args.gdb_path)
        if layers:
            logger.info(f"发现 {len(layers)} 个图层:")
            for i, layer in enumerate(layers, 1):
                print(f"  {i:2d}. {layer}")
        else:
            logger.error("未发现任何图层")
        return

    # 执行导入
    logger.info("开始GIS数据导入...")
    logger.info(f"GDB路径: {args.gdb_path}")
    logger.info(f"表前缀: {args.table_prefix}")
    logger.info(f"指定图层: {args.layers if args.layers else '所有图层'}")
    logger.info(f"导入模式: {'统一表' if args.unified_table else '分表'}")
    logger.info(f"批量大小: {args.batch_size}")

    success = import_specific_layers(
        gdb_path=args.gdb_path,
        table_prefix=args.table_prefix,
        target_layers=args.layers,
        create_separate_tables=not args.unified_table,
        batch_size=args.batch_size
    )

    if success:
        logger.info("数据导入完成！")
    else:
        logger.error("数据导入失败！")
        sys.exit(1)


if __name__ == '__main__':
    main()