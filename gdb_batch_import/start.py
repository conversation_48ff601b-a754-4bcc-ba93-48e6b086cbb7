from gis_data_import import GISDataImporter

# 数据库配置
db_config = {
    'host': '*************',
    'port': 9140,
    'user': 'suining',
    'password': 'Smart@123.',
    'database': 'db_sn_ecology_cloud'
}

# 创建导入器
importer = GISDataImporter(db_config)


# 导入GDB数据（分表模式）
importer.import_gdb_directory('/Users/<USER>/Downloads/MyProject.gdb', '510903xx', create_separate_tables=True)

# 关闭连接
importer.close()