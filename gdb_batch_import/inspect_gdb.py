#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
GDB目录结构查看工具
用于分析GDB目录中的图层和字段结构
"""

import os
import sys
import fiona
import geopandas as gpd
import pandas as pd
from pathlib import Path

def inspect_gdb_structure(gdb_path: str):
    """
    检查GDB目录结构
    
    Args:
        gdb_path: GDB目录路径
    """
    try:
        if not os.path.exists(gdb_path):
            print(f"错误: GDB目录不存在: {gdb_path}")
            return
        
        if not os.path.isdir(gdb_path):
            print(f"错误: {gdb_path} 不是一个目录")
            print("GDB应该是一个目录，请检查路径")
            return
        
        print(f"正在分析GDB目录: {gdb_path}")
        print("=" * 80)
        
        def sanitize_table_name(name: str) -> str:
            """清理表名，确保符合MySQL命名规范"""
            import re
            clean_name = name.lower()
            clean_name = re.sub(r'[^a-z0-9_]', '_', clean_name)
            clean_name = re.sub(r'_+', '_', clean_name)
            clean_name = clean_name.strip('_')
            if clean_name and clean_name[0].isdigit():
                clean_name = 'layer_' + clean_name
            return clean_name
        
        # 获取图层列表
        layers = fiona.listlayers(gdb_path)
        print(f"发现 {len(layers)} 个图层:")
        
        for i, layer_name in enumerate(layers, 1):
            clean_name = sanitize_table_name(layer_name)
            table_name = f"suining_{clean_name}"
            
            print(f"\n{i}. 图层: {layer_name}")
            print(f"   建议表名: {table_name}")
            print("-" * 60)
            
            try:
                # 读取少量数据来分析结构
                gdf = gpd.read_file(gdb_path, layer=layer_name, rows=5)
                
                print(f"   要素数量: {len(gpd.read_file(gdb_path, layer=layer_name))}")
                print(f"   几何类型: {gdf.geometry.geom_type.iloc[0] if not gdf.empty else 'Unknown'}")
                print(f"   坐标系: {gdf.crs}")
                
                # 显示字段信息
                print("   字段信息:")
                for col in gdf.columns:
                    if col != 'geometry':
                        dtype = gdf[col].dtype
                        non_null_count = gdf[col].count()
                        sample_value = gdf[col].dropna().iloc[0] if non_null_count > 0 else None
                        
                        # 推荐的MySQL类型
                        mysql_type = get_mysql_type(dtype, gdf[col])
                        
                        print(f"     - {col}: {dtype} -> {mysql_type}")
                        if sample_value is not None:
                            print(f"       示例值: {sample_value}")
                
                # 显示边界框
                if not gdf.empty:
                    bounds = gdf.total_bounds
                    print(f"   边界框: [{bounds[0]:.6f}, {bounds[1]:.6f}, {bounds[2]:.6f}, {bounds[3]:.6f}]")
                
                # 显示前几行数据示例
                if not gdf.empty:
                    print("   数据示例:")
                    for idx, row in gdf.head(2).iterrows():
                        print(f"     要素 {idx}:")
                        for col in gdf.columns:
                            if col != 'geometry':
                                value = row[col]
                                if pd.notna(value):
                                    print(f"       {col}: {value}")
                
            except Exception as e:
                print(f"   错误: 无法读取图层 {layer_name}: {e}")
        
        print("\n" + "=" * 80)
        print("分析完成!")
        
        # 生成建议的导入策略和表名预览
        print("\n建议的导入策略:")
        if len(layers) == 1:
            print("- 单图层文件，建议使用分表模式")
        elif len(layers) <= 5:
            print("- 图层数量较少，推荐使用分表模式")
        else:
            print("- 图层数量较多，推荐使用分表模式")
        
        print(f"\n分表模式预览 (表名格式: suining_图层名):")
        for i, layer_name in enumerate(layers, 1):
            clean_name = sanitize_table_name(layer_name)
            table_name = f"suining_{clean_name}"
            print(f"  {i:2d}. {layer_name} -> {table_name}")
        
        print(f"\n导入命令:")
        print(f"python import_separate_tables.py")
        print(f"# 或者")
        print(f"python run_import.py")
        
    except Exception as e:
        print(f"分析GDB目录失败: {e}")

def get_mysql_type(dtype, series) -> str:
    """
    根据pandas数据类型推荐MySQL类型
    
    Args:
        dtype: pandas数据类型
        series: 数据序列
        
    Returns:
        str: 推荐的MySQL类型
    """
    if dtype == 'object':
        # 检查字符串长度
        max_length = 0
        sample_values = series.dropna()
        if not sample_values.empty:
            max_length = sample_values.astype(str).str.len().max()
        
        if max_length > 1000:
            return 'TEXT'
        elif max_length > 255:
            return f'VARCHAR({min(max_length * 2, 1000)})'
        else:
            return f'VARCHAR({max(max_length * 2, 255)})'
            
    elif dtype in ['int64', 'int32']:
        return 'BIGINT'
    elif dtype in ['float64', 'float32']:
        return 'DECIMAL(15,6)'
    elif dtype == 'bool':
        return 'TINYINT(1)'
    elif 'datetime' in str(dtype):
        return 'DATETIME'
    else:
        return 'VARCHAR(255)'

def main():
    """主函数"""
    if len(sys.argv) != 2:
        print("使用方法: python inspect_gdb.py <gdb_directory_path>")
        print("示例: python inspect_gdb.py /path/to/遂宁市.gdb")
        print("注意：GDB是一个目录，不是单个文件")
        sys.exit(1)
    
    gdb_path = sys.argv[1]
    inspect_gdb_structure(gdb_path)

if __name__ == '__main__':
    main()
