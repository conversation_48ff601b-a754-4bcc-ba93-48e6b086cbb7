#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
测试start.py是否正确使用DEFAULT_GDB_PATH作为默认值
"""

import sys
import subprocess
import os

def test_default_gdb_path():
    """测试默认GDB路径设置"""
    print("测试start.py的默认GDB路径设置...")
    
    # 测试帮助信息中是否显示正确的默认路径
    cmd = [sys.executable, 'start.py', '--help']
    
    try:
        result = subprocess.run(cmd, capture_output=True, text=True, cwd=os.path.dirname(__file__))
        
        if result.returncode == 0:
            help_text = result.stdout
            
            # 检查帮助信息中是否包含正确的默认路径
            if '/Users/<USER>/Downloads/MyProject.gdb' in help_text:
                print("✓ 帮助信息中显示了正确的默认GDB路径")
                
                # 提取并显示相关行
                lines = help_text.split('\n')
                for line in lines:
                    if '--gdb-path' in line or 'MyProject.gdb' in line:
                        print(f"  {line.strip()}")
                
                return True
            else:
                print("✗ 帮助信息中未找到预期的默认GDB路径")
                print("帮助信息内容:")
                print(help_text)
                return False
        else:
            print(f"✗ 执行start.py --help失败，返回码: {result.returncode}")
            print("错误输出:", result.stderr)
            return False
            
    except Exception as e:
        print(f"✗ 执行测试时发生异常: {e}")
        return False

def test_config_consistency():
    """测试配置一致性"""
    print("\n测试配置一致性...")
    
    try:
        # 导入配置
        sys.path.insert(0, os.path.dirname(__file__))
        
        try:
            from config import DEFAULT_GDB_PATH as CONFIG_DEFAULT_GDB_PATH
            config_path = CONFIG_DEFAULT_GDB_PATH
            print(f"config.py中的DEFAULT_GDB_PATH: {config_path}")
        except ImportError:
            print("警告: 无法导入config.py")
            config_path = None
        
        # 检查start.py中的备用默认值
        with open(os.path.join(os.path.dirname(__file__), 'start.py'), 'r', encoding='utf-8') as f:
            start_content = f.read()
            
        # 查找备用默认值
        import re
        pattern = r"DEFAULT_GDB_PATH = '([^']+)'"
        matches = re.findall(pattern, start_content)
        
        if matches:
            fallback_path = matches[-1]  # 取最后一个匹配（应该是备用配置中的）
            print(f"start.py中的备用DEFAULT_GDB_PATH: {fallback_path}")
            
            if config_path and config_path == fallback_path:
                print("✓ 配置文件和备用配置中的路径一致")
                return True
            elif not config_path:
                print("✓ 使用备用配置中的路径")
                return True
            else:
                print("✗ 配置文件和备用配置中的路径不一致")
                return False
        else:
            print("✗ 未在start.py中找到DEFAULT_GDB_PATH定义")
            return False
            
    except Exception as e:
        print(f"✗ 测试配置一致性时发生异常: {e}")
        return False

def test_config_info_display():
    """测试配置信息显示"""
    print("\n测试配置信息显示...")
    
    cmd = [sys.executable, 'start.py', '--config-info']
    
    try:
        result = subprocess.run(cmd, capture_output=True, text=True, cwd=os.path.dirname(__file__))
        
        if result.returncode == 0:
            config_output = result.stdout
            
            if 'MyProject.gdb' in config_output:
                print("✓ 配置信息中显示了正确的默认GDB路径")
                
                # 显示相关行
                lines = config_output.split('\n')
                for line in lines:
                    if 'GDB' in line or 'MyProject.gdb' in line:
                        print(f"  {line.strip()}")
                
                return True
            else:
                print("✗ 配置信息中未找到预期的GDB路径")
                print("配置信息输出:")
                print(config_output)
                return False
        else:
            print(f"✗ 执行start.py --config-info失败，返回码: {result.returncode}")
            print("错误输出:", result.stderr)
            return False
            
    except Exception as e:
        print(f"✗ 测试配置信息显示时发生异常: {e}")
        return False

def main():
    """主测试函数"""
    print("=" * 60)
    print("测试start.py的DEFAULT_GDB_PATH使用情况")
    print("=" * 60)
    
    # 检查start.py是否存在
    start_py_path = os.path.join(os.path.dirname(__file__), 'start.py')
    if not os.path.exists(start_py_path):
        print(f"错误: 找不到start.py文件: {start_py_path}")
        return
    
    tests = [
        ("默认GDB路径设置", test_default_gdb_path),
        ("配置一致性", test_config_consistency),
        ("配置信息显示", test_config_info_display),
    ]
    
    passed = 0
    total = len(tests)
    
    for test_name, test_func in tests:
        try:
            if test_func():
                print(f"✓ {test_name} - 通过")
                passed += 1
            else:
                print(f"✗ {test_name} - 失败")
        except Exception as e:
            print(f"✗ {test_name} - 异常: {e}")
    
    print("\n" + "=" * 60)
    print(f"测试结果: {passed}/{total} 通过")
    print("=" * 60)
    
    if passed == total:
        print("✅ 所有测试通过！start.py正确使用了DEFAULT_GDB_PATH")
    else:
        print("❌ 部分测试失败，请检查配置")
    
    print("\n当前DEFAULT_GDB_PATH配置:")
    print("  - config.py: /Users/<USER>/Downloads/MyProject.gdb")
    print("  - start.py备用: /Users/<USER>/Downloads/MyProject.gdb")
    print("  - 环境变量: GDB_PATH (如果设置)")

if __name__ == '__main__':
    main()
