#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
使用修改后的GIS数据导入脚本的示例
展示如何处理包含缺失geometry的数据
"""

import os
import sys
import logging

# 添加当前目录到Python路径
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

from gis_data_import import GISDataImporter

# 配置日志
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(levelname)s - %(message)s'
)
logger = logging.getLogger(__name__)

def main():
    """主函数 - 演示如何使用修改后的导入功能"""
    
    # 数据库配置（请根据实际情况修改）
    db_config = {
        'host': 'localhost',
        'port': 3306,
        'user': 'root',
        'password': 'your_password',
        'database': 'your_database'
    }
    
    # GDB文件路径（请修改为实际路径）
    gdb_path = '/path/to/your/data.gdb'
    
    # 表名前缀
    table_prefix = 'imported_data'
    
    try:
        logger.info("开始GIS数据导入...")
        logger.info("修改后的功能特点:")
        logger.info("  ✓ 支持缺失geometry的行数据正常插入")
        logger.info("  ✓ 增强的错误处理和日志记录")
        logger.info("  ✓ 详细的导入统计信息")
        logger.info("  ✓ 安全的geometry字段处理")
        
        # 创建导入器
        importer = GISDataImporter(db_config)
        
        # 检查GDB文件是否存在
        if not os.path.exists(gdb_path):
            logger.error(f"GDB文件不存在: {gdb_path}")
            logger.info("请修改example_usage.py中的gdb_path变量为实际的GDB文件路径")
            return
        
        # 获取图层列表
        layers = importer.read_gdb_layers(gdb_path)
        if not layers:
            logger.error("未发现任何图层")
            return
        
        logger.info(f"发现 {len(layers)} 个图层: {layers}")
        
        # 导入数据（为每个图层创建单独的表）
        logger.info("开始导入数据...")
        importer.import_gdb_directory(
            gdb_dir=gdb_path,
            table_prefix=table_prefix,
            create_separate_tables=True
        )
        
        # 关闭连接
        importer.close()
        
        logger.info("数据导入完成！")
        logger.info("\n导入后的数据特点:")
        logger.info("  - 所有记录都已成功插入，包括缺失geometry的记录")
        logger.info("  - 缺失geometry的记录的相关字段（geometry_type, geometry_wkt, geometry_wkb, lng, lat, area, perimeter）将设置为NULL")
        logger.info("  - 其他属性字段正常保存")
        logger.info("  - 导入日志中会显示缺失geometry记录的统计信息")
        
    except Exception as e:
        logger.error(f"导入失败: {e}")
        sys.exit(1)

def demonstrate_key_improvements():
    """演示关键改进点"""
    
    logger.info("\n=== 关键改进点演示 ===")
    
    logger.info("\n1. 安全的geometry获取:")
    logger.info("   - 新增_safe_get_geometry()方法")
    logger.info("   - 处理geometry为None、空几何、无效几何等情况")
    logger.info("   - 详细的调试日志记录")
    
    logger.info("\n2. 增强的错误处理:")
    logger.info("   - 每个geometry处理步骤都有异常捕获")
    logger.info("   - 坐标转换失败时的优雅降级")
    logger.info("   - WKT/WKB生成失败的处理")
    
    logger.info("\n3. 详细的统计信息:")
    logger.info("   - 统计缺失geometry的记录数量")
    logger.info("   - 在导入完成后显示统计结果")
    logger.info("   - 帮助用户了解数据质量")
    
    logger.info("\n4. 数据完整性保证:")
    logger.info("   - 即使geometry缺失，其他属性字段仍正常导入")
    logger.info("   - 所有geometry相关字段正确设置为NULL")
    logger.info("   - 不会因为geometry问题导致整行数据丢失")

if __name__ == '__main__':
    demonstrate_key_improvements()
    print("\n" + "="*50)
    main()
