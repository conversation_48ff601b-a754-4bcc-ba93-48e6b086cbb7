#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
检查源表中的面积相关字段
"""

import pymysql

conn = pymysql.connect(
    host='localhost',
    port=3306,
    user='root',
    password='123456',
    database='land',
    charset='utf8mb4'
)

try:
    with conn.cursor() as cursor:
        print('=== 检查源表中的面积相关字段 ===')
        
        source_tables = [
            'hedong_yangsheng_danyuan_dycmydbjgh',
            'hedong_yangsheng_danyuan_dyhf', 
            'hedong_yangsheng_danyuan_ghfw',
            'hedong_yangsheng_shishi_unified'
        ]
        
        for table_name in source_tables:
            print(f'\n--- {table_name} ---')
            sql = f'DESCRIBE `{table_name}`'
            cursor.execute(sql)
            columns = cursor.fetchall()
            
            area_fields = []
            for col in columns:
                field_name = col[0].lower()
                if any(keyword in field_name for keyword in ['mj', 'area', '面积']):
                    area_fields.append((col[0], col[1]))
            
            if area_fields:
                print('面积相关字段:')
                for field_name, field_type in area_fields:
                    print(f'  {field_name}: {field_type}')
                    
                    # 查看样本数据
                    sql = f'SELECT `{field_name}` FROM `{table_name}` WHERE `{field_name}` IS NOT NULL LIMIT 3'
                    cursor.execute(sql)
                    samples = cursor.fetchall()
                    if samples:
                        sample_values = [str(row[0]) for row in samples]
                        print(f'    样本值: {sample_values}')
            else:
                print('未找到面积相关字段')
        
finally:
    conn.close()
