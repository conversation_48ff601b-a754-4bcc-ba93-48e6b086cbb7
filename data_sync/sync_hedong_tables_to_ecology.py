#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
同步河东养生单元相关表数据到 ecology_intelligent_land_plot 表
包含4张源表：
1. hedong_yangsheng_danyuan_dycmydbjgh
2. hedong_yangsheng_danyuan_dyhf
3. hedong_yangsheng_danyuan_ghfw
4. hedong_yangsheng_shishi_unified
"""

import pymysql
import logging
from typing import Dict, Any, List, Tuple
from pyproj import CRS, Transformer
import shapely.wkb as wkb
from shapely.ops import transform

# 配置日志
logging.basicConfig(
    level=logging.DEBUG,
    format='%(asctime)s - %(levelname)s - %(message)s',
    handlers=[
        logging.FileHandler('hedong_data_sync.log', encoding='utf-8'),
        logging.StreamHandler()
    ]
)
logger = logging.getLogger(__name__)

class HedongDataSyncer:
    """河东数据同步器"""
    
    def __init__(self, db_config: Dict[str, Any]):
        """
        初始化同步器
        
        Args:
            db_config: 数据库配置
        """
        self.db_config = db_config
        self.connection = None
        self.transformer = None
        self._connect_db()
        self._setup_transformer()
        
        # 源表配置
        self.source_tables = [
            'hedong_yangsheng_danyuan_dycmydbjgh',
            'hedong_yangsheng_danyuan_dyhf', 
            'hedong_yangsheng_danyuan_ghfw',
            'hedong_yangsheng_shishi_unified'
        ]
        
        # 目标表
        self.target_table = 'ecology_intelligent_land_plot'
    
    def _connect_db(self):
        """连接数据库"""
        try:
            self.connection = pymysql.connect(
                host=self.db_config['host'],
                port=self.db_config['port'],
                user=self.db_config['user'],
                password=self.db_config['password'],
                database=self.db_config['database'],
                charset='utf8mb4',
                autocommit=False
            )
            logger.info(f"成功连接到数据库: {self.db_config['host']}:{self.db_config['port']}/{self.db_config['database']}")
        except Exception as e:
            logger.error(f"数据库连接失败: {e}")
            raise
    
    def _setup_transformer(self):
        """设置坐标转换器"""
        try:
            # 使用与之前相同的 CGCS2000 3度带第35带投影坐标系定义
            cgcs2000_wkt = '''PROJCS["CGCS2000 3 Degree GK Zone 35",
    GEOGCS["China Geodetic Coordinate System 2000",
        DATUM["D China 2000",
            SPHEROID["CGCS2000",6378137.0,298.257222101],
            TOWGS84[0,0,0,0,0,0,0]],
        PRIMEM["Greenwich",0.0],
        UNIT["Degree",0.0174532925199433]],
    PROJECTION["Gauss_Kruger"],
    PARAMETER["latitude_of_origin",0.0],
    PARAMETER["central_meridian",105.0],
    PARAMETER["scale_factor",1.0],
    PARAMETER["false_easting",35500000.0],
    PARAMETER["false_northing",0.0],
    UNIT["Meter",1.0]]'''
            
            # 创建坐标系
            source_crs = CRS.from_wkt(cgcs2000_wkt)
            target_crs = CRS.from_epsg(4326)  # WGS84 经纬度坐标系
            
            # 创建转换器
            self.transformer = Transformer.from_crs(source_crs, target_crs, always_xy=True)
            logger.info("坐标转换器初始化成功")
            
        except Exception as e:
            logger.error(f"坐标转换器初始化失败: {e}")
            raise
    
    def check_table_exists(self, table_name: str) -> bool:
        """检查表是否存在"""
        try:
            with self.connection.cursor() as cursor:
                cursor.execute("""
                    SELECT COUNT(*) 
                    FROM information_schema.tables 
                    WHERE table_schema = %s AND table_name = %s
                """, (self.db_config['database'], table_name))
                result = cursor.fetchone()
                exists = result[0] > 0
                logger.info(f"表 {table_name} {'存在' if exists else '不存在'}")
                return exists
        except Exception as e:
            logger.error(f"检查表是否存在失败: {e}")
            return False
    
    def get_table_structure(self, table_name: str) -> List[Tuple]:
        """获取表结构"""
        try:
            with self.connection.cursor() as cursor:
                cursor.execute(f"DESCRIBE `{table_name}`")
                columns = cursor.fetchall()
                logger.info(f"表 {table_name} 有 {len(columns)} 个字段")
                return columns
        except Exception as e:
            logger.error(f"获取表结构失败: {e}")
            return []
    
    def get_table_count(self, table_name: str) -> int:
        """获取表记录数量"""
        try:
            with self.connection.cursor() as cursor:
                cursor.execute(f"SELECT COUNT(*) FROM `{table_name}`")
                count = cursor.fetchone()[0]
                logger.info(f"表 {table_name} 有 {count} 条记录")
                return count
        except Exception as e:
            logger.error(f"获取表记录数量失败: {e}")
            return 0
    
    def analyze_source_tables(self):
        """分析源表结构和数据"""
        logger.info("=== 分析源表结构和数据 ===")
        
        for table_name in self.source_tables:
            logger.info(f"\n--- 分析表: {table_name} ---")
            
            if not self.check_table_exists(table_name):
                logger.warning(f"表 {table_name} 不存在，跳过")
                continue
            
            # 获取表结构
            columns = self.get_table_structure(table_name)
            if columns:
                logger.info("字段列表:")
                for col in columns[:10]:  # 只显示前10个字段
                    logger.info(f"  - {col[0]}: {col[1]}")
                if len(columns) > 10:
                    logger.info(f"  ... 还有 {len(columns) - 10} 个字段")
            
            # 获取记录数量
            count = self.get_table_count(table_name)
            
            # 检查是否有几何字段
            geometry_fields = []
            for col in columns:
                field_name = col[0].lower()
                field_type = col[1].lower()
                if any(geo in field_name for geo in ['shape', 'geom', 'geometry']) or 'geometry' in field_type:
                    geometry_fields.append((col[0], col[1]))
            
            if geometry_fields:
                logger.info("几何字段:")
                for field_name, field_type in geometry_fields:
                    logger.info(f"  - {field_name}: {field_type}")
            else:
                logger.info("未发现几何字段")
    
    def transform_geometry_to_wgs84(self, geometry):
        """将几何对象从投影坐标系转换为 WGS84 经纬度坐标系"""
        try:
            # 使用 shapely 的 transform 函数进行坐标转换
            def transform_coords(x, y, z=None):
                # 转换坐标
                lon, lat = self.transformer.transform(x, y)
                return lon, lat
            
            # 转换几何对象
            transformed_geom = transform(transform_coords, geometry)
            return transformed_geom
            
        except Exception as e:
            logger.error(f"几何对象坐标转换失败: {e}")
            return None
    
    def sync_table_data(self, source_table: str, batch_size: int = 100) -> int:
        """同步单个表的数据"""
        try:
            logger.info(f"开始同步表: {source_table}")
            
            if not self.check_table_exists(source_table):
                logger.warning(f"源表 {source_table} 不存在，跳过同步")
                return 0
            
            # 获取源表数据
            with self.connection.cursor(pymysql.cursors.DictCursor) as cursor:
                cursor.execute(f"SELECT * FROM `{source_table}`")
                source_records = cursor.fetchall()
            
            if not source_records:
                logger.info(f"表 {source_table} 没有数据")
                return 0
            
            logger.info(f"表 {source_table} 有 {len(source_records)} 条记录需要同步")
            
            synced_count = 0
            failed_count = 0
            
            # 批量处理
            for i in range(0, len(source_records), batch_size):
                batch = source_records[i:i + batch_size]
                batch_inserts = []
                
                for record in batch:
                    try:
                        # 准备插入数据
                        insert_data = self._prepare_insert_data(record, source_table)
                        if insert_data:
                            batch_inserts.append(insert_data)
                        else:
                            failed_count += 1
                            
                    except Exception as e:
                        failed_count += 1
                        logger.error(f"处理记录失败: {e}")
                
                # 批量插入
                if batch_inserts:
                    inserted = self._batch_insert_records(batch_inserts)
                    synced_count += inserted
                    logger.info(f"已同步 {synced_count}/{len(source_records)} 条记录")
            
            logger.info(f"表 {source_table} 同步完成: 成功 {synced_count} 条, 失败 {failed_count} 条")
            return synced_count
            
        except Exception as e:
            logger.error(f"同步表 {source_table} 失败: {e}")
            return 0
    
    def _prepare_insert_data(self, record: Dict, source_table: str) -> Dict:
        """准备插入数据"""
        try:
            # 检查 ID 是否存在
            record_id = record.get('id')
            if not record_id:
                logger.warning(f"记录缺少 ID，跳过")
                return None

            # 基础字段映射（根据实际表结构）
            insert_data = {
                'id': record_id,  # 直接使用源表的 ID
                'name': record.get('name', record.get('NAME', '')),
                'remark': f'来源: {source_table}',  # 在备注中标注来源
                'bsm': record.get('bsm', record.get('BSM', '')),
                'ysdm': record.get('ysdm', record.get('YSDM', '')),
                'xzqdm': record.get('xzqdm', record.get('XZQDM', '')),
                'xzqmc': record.get('xzqmc', record.get('XZQMC', '')),
            }
            
            # 处理几何数据
            shape_data = None
            shape_wkt = None
            geom_wgs84 = None
            lng = None
            lat = None
            bbox = None

            # 查找几何字段（优先使用 geometry_wkb）
            geometry_field = None
            for field_name in ['geometry_wkb', 'shape', 'geom', 'geometry']:
                if field_name in record and record[field_name] is not None:
                    geometry_field = field_name
                    break
            
            # 优先使用源表中已有的经纬度坐标
            if 'lng' in record and 'lat' in record and record['lng'] is not None and record['lat'] is not None:
                lng = float(record['lng'])
                lat = float(record['lat'])
                logger.debug(f"使用源表坐标: lng={lng}, lat={lat}")

            if geometry_field and record[geometry_field]:
                try:
                    # 解析 WKB 数据
                    wkb_data = record[geometry_field]
                    if isinstance(wkb_data, bytes):
                        geometry = wkb.loads(wkb_data)

                        # 检查源表数据是否已经是 WGS84 坐标
                        bounds = geometry.bounds
                        if 100 <= bounds[0] <= 120 and 100 <= bounds[2] <= 120 and 20 <= bounds[1] <= 50 and 20 <= bounds[3] <= 50:
                            # 数据已经是经纬度格式，直接使用
                            logger.debug(f"检测到经纬度格式数据，直接使用")
                            wgs84_geometry = geometry
                        else:
                            # 需要坐标转换
                            logger.debug(f"检测到投影坐标数据，进行转换")
                            wgs84_geometry = self.transform_geometry_to_wgs84(geometry)

                        if wgs84_geometry:
                            # 生成各种格式的几何数据
                            shape_data = wkb_data  # 原始 WKB
                            shape_wkt = wgs84_geometry.wkt  # WKT 格式
                            geom_wgs84 = wgs84_geometry.wkt  # WGS84 几何格式

                            # 如果没有从源表获取到坐标，从几何数据计算
                            if lng is None or lat is None:
                                bounds = wgs84_geometry.bounds
                                lng = (bounds[0] + bounds[2]) / 2
                                lat = (bounds[1] + bounds[3]) / 2

                            # 检查是否有无穷大值
                            import math
                            if lng is not None and lat is not None:
                                if math.isinf(lng) or math.isnan(lng) or math.isinf(lat) or math.isnan(lat):
                                    logger.warning(f"检测到无效坐标值: lng={lng}, lat={lat}")
                                    return None

                            # 生成边界框
                            bounds = wgs84_geometry.bounds
                            bbox = f"{bounds[0]},{bounds[1]},{bounds[2]},{bounds[3]}"

                except Exception as e:
                    logger.warning(f"处理几何数据失败: {e}")
            
            # 添加几何相关字段（根据实际表结构）
            insert_data.update({
                'shape': shape_data,
                'lng': lng,
                'lat': lat,
                'bbox': bbox
            })

            # 注意：目标表没有 geom_simple 字段，所以不添加几何数据
            
            # 添加其他可能的字段
            for field_name, value in record.items():
                if field_name.lower() not in ['shape', 'geom', 'geometry', 'geometry_wkb', 'geometry_wkt'] and value is not None:
                    # 根据字段名映射到目标表字段
                    if field_name.lower() in ['shape_area']:
                        insert_data['shape_area'] = value
                    elif field_name.lower() in ['shape_length']:
                        insert_data['shape_length'] = value
                    elif field_name.lower() in ['id', 'objectid', 'fid']:
                        # 不直接映射 ID 字段，避免冲突
                        pass  # 跳过 ID 字段
            
            return insert_data
            
        except Exception as e:
            logger.error(f"准备插入数据失败: {e}")
            return None
    
    def _batch_insert_records(self, batch_inserts: List[Dict]) -> int:
        """批量插入记录"""
        try:
            if not batch_inserts:
                return 0

            # 过滤掉 None 值的字段，并排除 id 字段
            filtered_inserts = []
            for record in batch_inserts:
                filtered_record = {}
                for key, value in record.items():
                    if key.lower() != 'id' and value is not None:  # 跳过 id 字段和空值
                        filtered_record[key] = value
                if filtered_record:  # 只有非空记录才添加
                    filtered_inserts.append(filtered_record)

            if not filtered_inserts:
                return 0

            # 构建插入 SQL
            fields = list(filtered_inserts[0].keys())
            placeholders = ', '.join(['%s'] * len(fields))
            fields_str = ', '.join([f'`{field}`' for field in fields])

            insert_sql = f"""
            INSERT INTO `{self.target_table}` ({fields_str})
            VALUES ({placeholders})
            """
            
            # 准备数据
            values_list = []
            for record in batch_inserts:
                values = []
                for field in fields:
                    value = record.get(field)
                    values.append(value)
                values_list.append(values)

            # 调试：打印第一条记录的数据
            if values_list:
                logger.debug(f"插入字段: {fields}")
                logger.debug(f"第一条记录数据: {values_list[0]}")

            # 执行插入
            with self.connection.cursor() as cursor:
                cursor.executemany(insert_sql, values_list)
                self.connection.commit()
                return len(values_list)
                
        except Exception as e:
            logger.error(f"批量插入失败: {e}")
            self.connection.rollback()
            return 0
    
    def sync_all_tables(self) -> Dict[str, int]:
        """同步所有源表"""
        logger.info("=== 开始同步所有源表 ===")
        
        sync_results = {}
        total_synced = 0
        
        for table_name in self.source_tables:
            logger.info(f"\n--- 同步表: {table_name} ---")
            synced_count = self.sync_table_data(table_name, batch_size=50)
            sync_results[table_name] = synced_count
            total_synced += synced_count
        
        logger.info(f"\n=== 同步完成 ===")
        logger.info(f"总计同步记录数: {total_synced}")
        for table_name, count in sync_results.items():
            logger.info(f"  {table_name}: {count} 条")
        
        return sync_results
    
    def verify_sync_results(self):
        """验证同步结果"""
        try:
            logger.info("=== 验证同步结果 ===")
            
            with self.connection.cursor() as cursor:
                # 检查目标表总记录数
                cursor.execute(f"SELECT COUNT(*) FROM `{self.target_table}`")
                total_count = cursor.fetchone()[0]
                logger.info(f"目标表总记录数: {total_count}")
                
                # 按来源表统计
                for table_name in self.source_tables:
                    cursor.execute(f"""
                        SELECT COUNT(*) FROM `{self.target_table}`
                        WHERE remark LIKE %s
                    """, (f'%{table_name}%',))
                    count = cursor.fetchone()[0]
                    logger.info(f"来源 {table_name}: {count} 条记录")

                # 显示样本数据
                cursor.execute(f"""
                    SELECT id, name, remark, lng, lat
                    FROM `{self.target_table}`
                    WHERE remark LIKE '%hedong_yangsheng%'
                    ORDER BY id DESC
                    LIMIT 5
                """)
                
                samples = cursor.fetchall()
                if samples:
                    logger.info("样本数据:")
                    for sample in samples:
                        logger.info(f"  ID: {sample[0]}, 名称: {sample[1]}, 图层: {sample[2]}, 备注: {sample[3]}")
                
        except Exception as e:
            logger.error(f"验证同步结果失败: {e}")
    
    def close(self):
        """关闭数据库连接"""
        if self.connection:
            self.connection.close()
            logger.info("数据库连接已关闭")

def main():
    """主函数"""
    
    # 数据库配置
    db_config = {
        'host': 'localhost',
        'port': 3306,
        'user': 'root',
        'password': '123456',
        'database': 'land'
    }
    
    try:
        print("=" * 70)
        print("河东养生单元数据同步到 ecology_intelligent_land_plot")
        print("=" * 70)
        print("源表:")
        print("1. hedong_yangsheng_danyuan_dycmydbjgh")
        print("2. hedong_yangsheng_danyuan_dyhf")
        print("3. hedong_yangsheng_danyuan_ghfw")
        print("4. hedong_yangsheng_shishi_unified")
        print(f"目标表: ecology_intelligent_land_plot")
        print(f"数据库: {db_config['host']}:{db_config['port']}/{db_config['database']}")
        print("-" * 70)
        
        # 创建同步器
        syncer = HedongDataSyncer(db_config)
        
        # 分析源表
        print("\n1. 分析源表结构和数据...")
        syncer.analyze_source_tables()
        
        # 确认操作
        print("\n" + "-" * 70)
        print("将执行以下操作:")
        print("1. 分析所有源表的结构和数据")
        print("2. 将源表数据转换并同步到目标表")
        print("3. 在 remark 字段中标注数据来源")
        print("4. 转换几何数据为 WGS84 坐标系")
        print("5. 生成 lng/lat 坐标和 bbox 边界框")
        
        confirm = input("\n确认执行数据同步? (Y/n): ").lower().strip()
        if confirm == 'n':
            print("操作已取消")
            return
        
        print("\n2. 开始数据同步...")
        
        # 执行同步
        sync_results = syncer.sync_all_tables()
        
        # 验证结果
        print("\n3. 验证同步结果...")
        syncer.verify_sync_results()
        
        # 关闭连接
        syncer.close()
        
        print("\n" + "=" * 70)
        print("✓ 数据同步完成!")
        print()
        print("同步结果:")
        total_synced = sum(sync_results.values())
        print(f"总计同步: {total_synced} 条记录")
        for table_name, count in sync_results.items():
            print(f"  {table_name}: {count} 条")
        print()
        print("验证查询:")
        print("SELECT id, name, layer_name, remark FROM ecology_intelligent_land_plot")
        print("WHERE remark LIKE '%hedong_yangsheng%' ORDER BY id DESC LIMIT 10;")
        print()
        print(f"详细日志: hedong_data_sync.log")
        
    except Exception as e:
        print(f"✗ 同步失败: {e}")
        logger.error(f"程序执行失败: {e}")

if __name__ == '__main__':
    main()
