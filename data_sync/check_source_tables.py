#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
检查源表是否存在
"""

import pymysql

# 连接数据库
conn = pymysql.connect(
    host='localhost',
    port=3306,
    user='root',
    password='123456',
    database='land',
    charset='utf8mb4'
)

try:
    with conn.cursor() as cursor:
        print('=== 检查源表是否存在 ===')
        
        source_tables = [
            'hedong_yangsheng_danyuan_dycmydbjgh',
            'hedong_yangsheng_danyuan_dyhf', 
            'hedong_yangsheng_danyuan_ghfw',
            'hedong_yangsheng_shishi_unified'
        ]
        
        for table_name in source_tables:
            cursor.execute("""
                SELECT COUNT(*) 
                FROM information_schema.tables 
                WHERE table_schema = %s AND table_name = %s
            """, ('land', table_name))
            exists = cursor.fetchone()[0] > 0
            
            if exists:
                sql = f"SELECT COUNT(*) FROM `{table_name}`"
                cursor.execute(sql)
                count = cursor.fetchone()[0]
                print(f'✓ {table_name}: 存在，{count} 条记录')
                
                # 查看表结构
                sql = f"DESCRIBE `{table_name}`"
                cursor.execute(sql)
                columns = cursor.fetchall()
                geometry_fields = [col[0] for col in columns if 'geom' in col[0].lower() or 'shape' in col[0].lower()]
                if geometry_fields:
                    print(f'    几何字段: {geometry_fields}')
                else:
                    print(f'    未发现几何字段')
            else:
                print(f'✗ {table_name}: 不存在')
        
finally:
    conn.close()
