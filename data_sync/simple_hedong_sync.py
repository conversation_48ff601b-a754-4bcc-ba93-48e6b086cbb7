#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
简化版河东数据同步脚本
只同步基本字段，避免字段不匹配问题
"""

import pymysql
import logging

# 配置日志
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(levelname)s - %(message)s'
)
logger = logging.getLogger(__name__)

def sync_table_data(source_table: str, db_config: dict) -> int:
    """同步单个表的数据"""
    
    conn = pymysql.connect(**db_config)
    
    try:
        with conn.cursor(pymysql.cursors.DictCursor) as cursor:
            logger.info(f"开始同步表: {source_table}")
            
            # 获取源表数据
            cursor.execute(f"SELECT * FROM `{source_table}`")  # 同步所有记录
            records = cursor.fetchall()
            
            if not records:
                logger.info(f"表 {source_table} 没有数据")
                return 0
            
            logger.info(f"获取到 {len(records)} 条记录")
            
            synced_count = 0
            
            for record in records:
                try:
                    # 准备基本字段数据（不包括 GEOMETRY 字段）
                    insert_data = {
                        'id': record.get('id'),
                        'name': record.get('layer_name', ''),  # 使用 layer_name 作为 name
                        'remark': f'来源: {source_table}',
                        'lng': record.get('lng'),
                        'lat': record.get('lat'),
                        'shape': record.get('geometry_wkb'),
                        'shape_area': record.get('shape_area'),
                        'shape_length': record.get('shape_length'),
                        'bsm': record.get('bsm', ''),
                        'ysdm': record.get('ysdm', ''),
                        'xzqdm': record.get('xzqdm', ''),
                        'xzqmc': record.get('xzqmc', '')
                    }

                    # 映射面积字段到目标表的 ydmj 字段 - 优先级顺序查找
                    area_fields = ['ydmj', 'jsydmj', 'dymj', 'ghmj', 'area']
                    for area_field in area_fields:
                        if record.get(area_field) is not None:
                            insert_data['ydmj'] = record.get(area_field)
                            # 同时映射到 jsydmj 字段
                            insert_data['jsydmj'] = record.get(area_field)
                            logger.info(f"使用 {area_field} 字段作为面积: {record.get(area_field)}")
                            break

                    # 过滤掉 None 值
                    filtered_data = {k: v for k, v in insert_data.items() if v is not None}

                    if not filtered_data.get('id'):
                        logger.warning(f"记录缺少 ID，跳过")
                        continue

                    # 获取 WKT 数据用于 GEOMETRY 字段
                    geometry_wkt = record.get('geometry_wkt')

                    if geometry_wkt:
                        # 构建包含 GEOMETRY 字段的插入 SQL
                        fields = list(filtered_data.keys()) + ['geom_wgs824']
                        placeholders = ', '.join(['%s'] * len(filtered_data)) + ', ST_GeomFromText(%s)'
                        fields_str = ', '.join([f'`{field}`' for field in fields])
                        values = list(filtered_data.values()) + [geometry_wkt]

                        insert_sql = f"""
                        INSERT INTO ecology_intelligent_land_plot ({fields_str})
                        VALUES ({placeholders})
                        """
                    else:
                        # 如果没有 WKT 数据，创建一个默认的 POINT
                        lng = record.get('lng', 0)
                        lat = record.get('lat', 0)
                        default_wkt = f'POINT({lng} {lat})'

                        fields = list(filtered_data.keys()) + ['geom_wgs824']
                        placeholders = ', '.join(['%s'] * len(filtered_data)) + ', ST_GeomFromText(%s)'
                        fields_str = ', '.join([f'`{field}`' for field in fields])
                        values = list(filtered_data.values()) + [default_wkt]

                        insert_sql = f"""
                        INSERT INTO ecology_intelligent_land_plot ({fields_str})
                        VALUES ({placeholders})
                        """

                    # 执行插入
                    cursor.execute(insert_sql, values)
                    synced_count += 1
                    logger.info(f"成功插入记录 ID: {filtered_data['id']}")
                    
                except Exception as e:
                    logger.error(f"插入记录失败: {e}")
                    logger.error(f"记录 ID: {record.get('id')}")
                    continue
            
            conn.commit()
            logger.info(f"表 {source_table} 同步完成: {synced_count} 条记录")
            return synced_count
            
    except Exception as e:
        logger.error(f"同步表 {source_table} 失败: {e}")
        conn.rollback()
        return 0
    finally:
        conn.close()

def main():
    """主函数"""
    
    # 数据库配置
    db_config = {
        'host': 'localhost',
        'port': 3306,
        'user': 'root',
        'password': '123456',
        'database': 'land',
        'charset': 'utf8mb4'
    }
    
    # 源表列表
    source_tables = [
        'hedong_yangsheng_danyuan_dycmydbjgh',
        'hedong_yangsheng_danyuan_dyhf',
        'hedong_yangsheng_danyuan_ghfw',
        'hedong_yangsheng_shishi_unified'
    ]
    
    print("=" * 60)
    print("简化版河东数据同步")
    print("=" * 60)
    
    total_synced = 0
    
    for table_name in source_tables:
        print(f"\n同步表: {table_name}")
        synced = sync_table_data(table_name, db_config)
        total_synced += synced
        print(f"同步结果: {synced} 条记录")
    
    print(f"\n总计同步: {total_synced} 条记录")
    
    # 验证结果
    conn = pymysql.connect(**db_config)
    try:
        with conn.cursor() as cursor:
            cursor.execute("""
                SELECT COUNT(*) FROM ecology_intelligent_land_plot 
                WHERE remark LIKE '%hedong_yangsheng%'
            """)
            count = cursor.fetchone()[0]
            print(f"验证: 目标表中有 {count} 条河东数据")
            
            # 显示样本
            cursor.execute("""
                SELECT id, name, remark, lng, lat 
                FROM ecology_intelligent_land_plot 
                WHERE remark LIKE '%hedong_yangsheng%'
                ORDER BY id DESC
                LIMIT 3
            """)
            samples = cursor.fetchall()
            
            if samples:
                print("\n样本数据:")
                for sample in samples:
                    print(f"  ID: {sample[0]}, 名称: {sample[1]}, 备注: {sample[2]}")
    finally:
        conn.close()

if __name__ == '__main__':
    main()
