#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
基于 Java 算法的 WKB 到经纬度转换脚本
将 ecology_intelligent_land_plot 表中的 WKB 数据转换为准确的经纬度坐标
"""

import pymysql
import logging
from typing import Dict, Any, Tuple
import struct
from pyproj import CRS, Transformer
import shapely.wkb as wkb
from shapely.geometry import Point, Polygon, MultiPolygon
import base64

# 配置日志
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(levelname)s - %(message)s',
    handlers=[
        logging.FileHandler('wkb_coordinate_conversion.log', encoding='utf-8'),
        logging.StreamHandler()
    ]
)
logger = logging.getLogger(__name__)

class WKBCoordinateConverter:
    """WKB 坐标转换器"""
    
    def __init__(self, db_config: Dict[str, Any]):
        """
        初始化转换器
        
        Args:
            db_config: 数据库配置
        """
        self.db_config = db_config
        self.connection = None
        self.transformer = None
        self._connect_db()
        self._setup_transformer()
    
    def _connect_db(self):
        """连接数据库"""
        try:
            self.connection = pymysql.connect(
                host=self.db_config['host'],
                port=self.db_config['port'],
                user=self.db_config['user'],
                password=self.db_config['password'],
                database=self.db_config['database'],
                charset='utf8mb4',
                autocommit=False
            )
            logger.info(f"成功连接到数据库: {self.db_config['host']}:{self.db_config['port']}/{self.db_config['database']}")
        except Exception as e:
            logger.error(f"数据库连接失败: {e}")
            raise
    
    def _setup_transformer(self):
        """设置坐标转换器"""
        try:
            # 定义 CGCS2000 3度带第35带投影坐标系（基于Java代码中的WKT）
            cgcs2000_wkt = """
            PROJCS["CGCS2000 3 Degree GK Zone 35",
                GEOGCS["China Geodetic Coordinate System 2000",
                    DATUM["D China 2000",
                        SPHEROID["CGCS2000",6378137.0,298.257222101],
                        TOWGS84[0,0,0,0,0,0,0]],
                    PRIMEM["Greenwich",0.0],
                    UNIT["Degree",0.0174532925199433]],
                PROJECTION["Gauss_Kruger"],
                PARAMETER["latitude_of_origin",0.0],
                PARAMETER["central_meridian",105.0],
                PARAMETER["scale_factor",1.0],
                PARAMETER["false_easting",35500000.0],
                PARAMETER["false_northing",0.0],
                UNIT["Meter",1.0]]
            """
            
            # 创建坐标系
            source_crs = CRS.from_wkt(cgcs2000_wkt)
            target_crs = CRS.from_epsg(4326)  # WGS84
            
            # 创建转换器
            self.transformer = Transformer.from_crs(source_crs, target_crs, always_xy=True)
            logger.info("坐标转换器初始化成功")
            
        except Exception as e:
            logger.error(f"坐标转换器初始化失败: {e}")
            # 使用备用方法
            self._setup_approximate_transformer()
    
    def _setup_approximate_transformer(self):
        """设置近似坐标转换器"""
        try:
            # 使用 EPSG 代码作为备用方案
            source_crs = CRS.from_epsg(4490)  # CGCS2000
            target_crs = CRS.from_epsg(4326)  # WGS84
            self.transformer = Transformer.from_crs(source_crs, target_crs, always_xy=True)
            logger.info("使用近似坐标转换器")
        except Exception as e:
            logger.error(f"近似坐标转换器初始化失败: {e}")
            self.transformer = None
    
    def convert_wkb_to_bbox(self, wkb_data: bytes) -> Tuple[float, float, float, float]:
        """
        将 WKB 数据转换为边界框
        
        Args:
            wkb_data: WKB 二进制数据
            
        Returns:
            Tuple[float, float, float, float]: (min_lng, min_lat, max_lng, max_lat)
        """
        try:
            # 解析 WKB 数据为几何对象
            geometry = wkb.loads(wkb_data)
            
            # 获取边界框
            bounds = geometry.bounds  # (minx, miny, maxx, maxy)
            min_x, min_y, max_x, max_y = bounds
            
            logger.debug(f"原始坐标范围 - minX: {min_x}, maxX: {max_x}, minY: {min_y}, maxY: {max_y}")
            
            if self.transformer:
                # 转换四个角点
                corners = [
                    (min_x, min_y),  # 左下角
                    (max_x, max_y),  # 右上角
                    (max_x, min_y),  # 右下角
                    (min_x, max_y)   # 左上角
                ]
                
                transformed_corners = []
                for x, y in corners:
                    try:
                        lng, lat = self.transformer.transform(x, y)
                        transformed_corners.append((lng, lat))
                        logger.debug(f"转换: ({x}, {y}) -> ({lng}, {lat})")
                    except Exception as e:
                        logger.warning(f"坐标转换失败: {e}")
                        continue
                
                if transformed_corners:
                    # 计算转换后的边界框
                    lngs = [corner[0] for corner in transformed_corners]
                    lats = [corner[1] for corner in transformed_corners]
                    
                    min_lng = min(lngs)
                    max_lng = max(lngs)
                    min_lat = min(lats)
                    max_lat = max(lats)
                    
                    return min_lng, min_lat, max_lng, max_lat
            
            # 如果转换失败，返回原始坐标（可能不准确）
            logger.warning("使用原始坐标作为备用方案")
            return min_x, min_y, max_x, max_y
            
        except Exception as e:
            logger.error(f"WKB 转换失败: {e}")
            return None, None, None, None
    
    def get_geometry_center(self, wkb_data: bytes) -> Tuple[float, float]:
        """
        获取几何对象的中心点坐标
        
        Args:
            wkb_data: WKB 二进制数据
            
        Returns:
            Tuple[float, float]: (lng, lat)
        """
        try:
            # 解析 WKB 数据
            geometry = wkb.loads(wkb_data)
            
            # 获取中心点
            centroid = geometry.centroid
            x, y = centroid.x, centroid.y
            
            if self.transformer:
                try:
                    lng, lat = self.transformer.transform(x, y)
                    return lng, lat
                except Exception as e:
                    logger.warning(f"中心点坐标转换失败: {e}")
            
            # 备用方案
            return x, y
            
        except Exception as e:
            logger.error(f"获取中心点失败: {e}")
            return None, None
    
    def update_coordinates_from_wkb(self, table_name: str, batch_size: int = 100) -> bool:
        """
        从 WKB 数据更新坐标字段
        
        Args:
            table_name: 表名
            batch_size: 批处理大小
            
        Returns:
            bool: 更新是否成功
        """
        try:
            logger.info("开始从 WKB 数据更新坐标...")
            
            # 获取需要更新的记录
            with self.connection.cursor() as cursor:
                cursor.execute(f"""
                    SELECT id, shape 
                    FROM `{table_name}` 
                    WHERE shape IS NOT NULL 
                    AND (lng IS NULL OR lat IS NULL OR bbox IS NULL)
                    LIMIT {batch_size * 10}
                """)
                records = cursor.fetchall()
            
            if not records:
                logger.info("没有需要更新的记录")
                return True
            
            logger.info(f"找到 {len(records)} 条需要更新的记录")
            
            updated_count = 0
            failed_count = 0
            
            # 批量处理
            for i in range(0, len(records), batch_size):
                batch = records[i:i + batch_size]
                batch_updates = []
                
                for record_id, wkb_data in batch:
                    try:
                        # 获取中心点坐标
                        lng, lat = self.get_geometry_center(wkb_data)
                        
                        # 获取边界框
                        min_lng, min_lat, max_lng, max_lat = self.convert_wkb_to_bbox(wkb_data)
                        
                        if lng is not None and lat is not None:
                            bbox_str = None
                            if all(coord is not None for coord in [min_lng, min_lat, max_lng, max_lat]):
                                bbox_str = f"{min_lng},{min_lat},{max_lng},{max_lat}"
                            
                            batch_updates.append((lng, lat, bbox_str, record_id))
                        else:
                            failed_count += 1
                            logger.warning(f"记录 {record_id} 坐标转换失败")
                            
                    except Exception as e:
                        failed_count += 1
                        logger.error(f"处理记录 {record_id} 失败: {e}")
                
                # 批量更新数据库
                if batch_updates:
                    with self.connection.cursor() as cursor:
                        cursor.executemany(f"""
                            UPDATE `{table_name}` 
                            SET lng = %s, lat = %s, bbox = %s 
                            WHERE id = %s
                        """, batch_updates)
                        self.connection.commit()
                        updated_count += len(batch_updates)
                        logger.info(f"已更新 {updated_count} 条记录")
            
            logger.info(f"坐标更新完成: 成功 {updated_count} 条, 失败 {failed_count} 条")
            return True
            
        except Exception as e:
            logger.error(f"更新坐标失败: {e}")
            self.connection.rollback()
            return False
    
    def verify_conversion(self, table_name: str, limit: int = 5):
        """验证转换结果"""
        try:
            logger.info("=== 转换结果验证 ===")
            
            with self.connection.cursor(pymysql.cursors.DictCursor) as cursor:
                cursor.execute(f"""
                    SELECT id, name, lng, lat, bbox,
                           LEFT(shape_wkt, 100) as shape_preview
                    FROM `{table_name}` 
                    WHERE lng IS NOT NULL AND lat IS NOT NULL
                    LIMIT {limit}
                """)
                
                records = cursor.fetchall()
                
                for i, record in enumerate(records, 1):
                    logger.info(f"--- 记录 {i} ---")
                    logger.info(f"ID: {record['id']}")
                    logger.info(f"名称: {record['name']}")
                    logger.info(f"经度: {record['lng']}")
                    logger.info(f"纬度: {record['lat']}")
                    logger.info(f"边界框: {record['bbox']}")
                    if record['shape_preview']:
                        logger.info(f"几何预览: {record['shape_preview']}...")
                    logger.info("")
                
        except Exception as e:
            logger.error(f"验证转换结果失败: {e}")
    
    def get_statistics(self, table_name: str):
        """获取统计信息"""
        try:
            with self.connection.cursor() as cursor:
                # 总记录数
                cursor.execute(f"SELECT COUNT(*) FROM `{table_name}`")
                total = cursor.fetchone()[0]
                
                # 有 WKB 数据的记录
                cursor.execute(f"SELECT COUNT(*) FROM `{table_name}` WHERE shape IS NOT NULL")
                wkb_count = cursor.fetchone()[0]
                
                # 有坐标数据的记录
                cursor.execute(f"SELECT COUNT(*) FROM `{table_name}` WHERE lng IS NOT NULL AND lat IS NOT NULL")
                coord_count = cursor.fetchone()[0]
                
                # 有边界框数据的记录
                cursor.execute(f"SELECT COUNT(*) FROM `{table_name}` WHERE bbox IS NOT NULL")
                bbox_count = cursor.fetchone()[0]
                
                logger.info("=== 统计信息 ===")
                logger.info(f"总记录数: {total}")
                logger.info(f"有 WKB 数据: {wkb_count}")
                logger.info(f"有坐标数据: {coord_count}")
                logger.info(f"有边界框数据: {bbox_count}")
                
                return {
                    'total': total,
                    'wkb': wkb_count,
                    'coord': coord_count,
                    'bbox': bbox_count
                }
                
        except Exception as e:
            logger.error(f"获取统计信息失败: {e}")
            return None
    
    def close(self):
        """关闭数据库连接"""
        if self.connection:
            self.connection.close()
            logger.info("数据库连接已关闭")

def main():
    """主函数"""
    
    # 数据库配置
    db_config = {
        'host': 'localhost',
        'port': 3306,
        'user': 'root',
        'password': '123456',
        'database': 'land'
    }
    
    # 目标表名
    table_name = 'ecology_intelligent_land_plot'
    
    try:
        print("=" * 60)
        print("基于 Java 算法的 WKB 坐标转换")
        print("=" * 60)
        print(f"目标表: {table_name}")
        print(f"数据库: {db_config['host']}:{db_config['port']}/{db_config['database']}")
        print("-" * 60)
        
        # 创建转换器
        converter = WKBCoordinateConverter(db_config)
        
        # 显示当前状态
        print("\n当前状态:")
        stats = converter.get_statistics(table_name)
        
        # 确认操作
        print("\n" + "-" * 60)
        print("将执行以下操作:")
        print("1. 使用精确的 CGCS2000 到 WGS84 坐标转换")
        print("2. 从 WKB 数据提取准确的中心点坐标")
        print("3. 计算准确的边界框")
        print("4. 更新 lng, lat, bbox 字段")
        
        confirm = input("\n确认执行坐标转换? (Y/n): ").lower().strip()
        if confirm == 'n':
            print("操作已取消")
            return
        
        print("\n开始转换...")
        
        # 执行坐标转换
        if converter.update_coordinates_from_wkb(table_name, batch_size=50):
            print("✓ 坐标转换成功")
        else:
            print("✗ 坐标转换失败")
            return
        
        # 显示最终状态
        print("\n最终状态:")
        final_stats = converter.get_statistics(table_name)
        
        # 验证转换结果
        print("\n转换结果验证:")
        converter.verify_conversion(table_name, limit=3)
        
        # 关闭连接
        converter.close()
        
        print("\n" + "=" * 60)
        print("✓ 坐标转换完成!")
        print()
        print("转换说明:")
        print("- 使用精确的 CGCS2000 3度带第35带投影坐标系")
        print("- 转换为 WGS84 地理坐标系 (EPSG:4326)")
        print("- 提取几何对象的中心点作为代表坐标")
        print("- 计算准确的边界框范围")
        print()
        print("使用示例:")
        print(f"SELECT id, name, lng, lat, bbox FROM {table_name} WHERE lng IS NOT NULL LIMIT 5;")
        print()
        print(f"详细日志请查看: wkb_coordinate_conversion.log")
        
    except Exception as e:
        print(f"✗ 转换失败: {e}")
        logger.error(f"程序执行失败: {e}")

if __name__ == '__main__':
    main()
