#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
检查 ecology_intelligent_land_plot 表的结构和数据
分析现有字段，为添加 shape 字段做准备
"""

import pymysql
import json
from typing import Dict, Any

class TableAnalyzer:
    """表结构分析器"""
    
    def __init__(self, db_config: Dict[str, Any]):
        """
        初始化分析器
        
        Args:
            db_config: 数据库配置
        """
        self.db_config = db_config
        self.connection = None
        self._connect_db()
    
    def _connect_db(self):
        """连接数据库"""
        try:
            self.connection = pymysql.connect(
                host=self.db_config['host'],
                port=self.db_config['port'],
                user=self.db_config['user'],
                password=self.db_config['password'],
                database=self.db_config['database'],
                charset='utf8mb4',
                autocommit=False
            )
            print(f"✓ 成功连接到数据库: {self.db_config['host']}:{self.db_config['port']}/{self.db_config['database']}")
        except Exception as e:
            print(f"✗ 数据库连接失败: {e}")
            raise
    
    def check_table_exists(self, table_name: str) -> bool:
        """检查表是否存在"""
        try:
            with self.connection.cursor() as cursor:
                cursor.execute("""
                    SELECT COUNT(*) 
                    FROM information_schema.tables 
                    WHERE table_schema = %s AND table_name = %s
                """, (self.db_config['database'], table_name))
                result = cursor.fetchone()
                exists = result[0] > 0
                print(f"表 {table_name}: {'存在' if exists else '不存在'}")
                return exists
        except Exception as e:
            print(f"✗ 检查表是否存在失败: {e}")
            return False
    
    def get_table_structure(self, table_name: str):
        """获取并显示表结构"""
        try:
            print(f"\n=== 表 {table_name} 结构信息 ===")
            
            with self.connection.cursor() as cursor:
                cursor.execute(f"DESCRIBE `{table_name}`")
                columns = cursor.fetchall()
                
                if not columns:
                    print("表结构为空")
                    return
                
                print(f"字段总数: {len(columns)}")
                print("\n字段详情:")
                print("-" * 80)
                print(f"{'字段名':<25} {'类型':<20} {'允许NULL':<8} {'键':<8} {'默认值':<15} {'额外'}")
                print("-" * 80)
                
                for col in columns:
                    field_name = col[0] or ''
                    field_type = col[1] or ''
                    is_null = col[2] or ''
                    key = col[3] or ''
                    default = str(col[4]) if col[4] is not None else 'NULL'
                    extra = col[5] or ''
                    
                    print(f"{field_name:<25} {field_type:<20} {is_null:<8} {key:<8} {default:<15} {extra}")
                
                return columns
                
        except Exception as e:
            print(f"✗ 获取表结构失败: {e}")
            return None
    
    def get_table_count(self, table_name: str) -> int:
        """获取表记录数量"""
        try:
            with self.connection.cursor() as cursor:
                cursor.execute(f"SELECT COUNT(*) FROM `{table_name}`")
                count = cursor.fetchone()[0]
                print(f"\n记录总数: {count}")
                return count
        except Exception as e:
            print(f"✗ 获取记录数量失败: {e}")
            return 0
    
    def get_sample_data(self, table_name: str, limit: int = 3):
        """获取样本数据"""
        try:
            print(f"\n=== 样本数据 (前{limit}条) ===")
            
            with self.connection.cursor(pymysql.cursors.DictCursor) as cursor:
                cursor.execute(f"SELECT * FROM `{table_name}` LIMIT %s", (limit,))
                rows = cursor.fetchall()
                
                if not rows:
                    print("表中没有数据")
                    return
                
                for i, row in enumerate(rows, 1):
                    print(f"\n--- 记录 {i} ---")
                    for key, value in row.items():
                        # 处理长文本显示
                        if isinstance(value, str) and len(value) > 100:
                            display_value = value[:100] + "..."
                        else:
                            display_value = value
                        print(f"{key}: {display_value}")
                
        except Exception as e:
            print(f"✗ 获取样本数据失败: {e}")
    
    def check_coordinate_fields(self, table_name: str):
        """检查是否已有坐标相关字段"""
        try:
            print(f"\n=== 坐标相关字段检查 ===")
            
            with self.connection.cursor() as cursor:
                cursor.execute(f"DESCRIBE `{table_name}`")
                columns = cursor.fetchall()
                
                coordinate_fields = []
                geometry_fields = []
                
                for col in columns:
                    field_name = col[0].lower()
                    field_type = col[1].lower()
                    
                    # 检查坐标字段
                    if any(coord in field_name for coord in ['lng', 'lat', 'lon', 'x', 'y', 'longitude', 'latitude']):
                        coordinate_fields.append((col[0], col[1]))
                    
                    # 检查几何字段
                    if any(geo in field_name for geo in ['geometry', 'shape', 'geom', 'wkt', 'wkb']):
                        geometry_fields.append((col[0], col[1]))
                
                if coordinate_fields:
                    print("发现坐标字段:")
                    for field_name, field_type in coordinate_fields:
                        print(f"  - {field_name}: {field_type}")
                else:
                    print("未发现坐标字段")
                
                if geometry_fields:
                    print("发现几何字段:")
                    for field_name, field_type in geometry_fields:
                        print(f"  - {field_name}: {field_type}")
                else:
                    print("未发现几何字段")
                
                return coordinate_fields, geometry_fields
                
        except Exception as e:
            print(f"✗ 检查坐标字段失败: {e}")
            return [], []
    
    def analyze_coordinate_data(self, table_name: str, coordinate_fields: list):
        """分析坐标数据的情况"""
        if not coordinate_fields:
            return
        
        try:
            print(f"\n=== 坐标数据分析 ===")
            
            with self.connection.cursor() as cursor:
                for field_name, field_type in coordinate_fields:
                    # 检查非空数量
                    cursor.execute(f"SELECT COUNT(*) FROM `{table_name}` WHERE `{field_name}` IS NOT NULL")
                    non_null_count = cursor.fetchone()[0]
                    
                    # 检查数值范围
                    cursor.execute(f"SELECT MIN(`{field_name}`), MAX(`{field_name}`) FROM `{table_name}` WHERE `{field_name}` IS NOT NULL")
                    min_max = cursor.fetchone()
                    
                    print(f"{field_name}:")
                    print(f"  - 非空记录数: {non_null_count}")
                    if min_max[0] is not None:
                        print(f"  - 数值范围: {min_max[0]} ~ {min_max[1]}")
                    
                    # 显示几个样本值
                    cursor.execute(f"SELECT `{field_name}` FROM `{table_name}` WHERE `{field_name}` IS NOT NULL LIMIT 5")
                    samples = cursor.fetchall()
                    if samples:
                        sample_values = [str(row[0]) for row in samples]
                        print(f"  - 样本值: {', '.join(sample_values)}")
                    print()
                    
        except Exception as e:
            print(f"✗ 分析坐标数据失败: {e}")
    
    def close(self):
        """关闭数据库连接"""
        if self.connection:
            self.connection.close()
            print("\n数据库连接已关闭")

def main():
    """主函数"""
    
    # 数据库配置
    db_config = {
        'host': 'localhost',
        'port': 3306,
        'user': 'root',
        'password': '123456',  # 请修改为实际密码
        'database': 'land'
    }
    
    # 目标表名
    table_name = 'ecology_intelligent_land_plot'
    
    try:
        print("=" * 60)
        print("ecology_intelligent_land_plot 表结构和数据分析")
        print("=" * 60)
        print(f"目标表: {table_name}")
        print(f"数据库: {db_config['host']}:{db_config['port']}/{db_config['database']}")
        
        # 创建分析器
        analyzer = TableAnalyzer(db_config)
        
        # 检查表是否存在
        if not analyzer.check_table_exists(table_name):
            print(f"✗ 表 {table_name} 不存在，请检查表名或数据库")
            return
        
        # 获取表结构
        columns = analyzer.get_table_structure(table_name)
        if not columns:
            return
        
        # 获取记录数量
        count = analyzer.get_table_count(table_name)
        
        # 获取样本数据
        if count > 0:
            analyzer.get_sample_data(table_name, limit=2)
        
        # 检查坐标相关字段
        coordinate_fields, geometry_fields = analyzer.check_coordinate_fields(table_name)
        
        # 分析坐标数据
        if coordinate_fields:
            analyzer.analyze_coordinate_data(table_name, coordinate_fields)
        
        # 给出建议
        print("=" * 60)
        print("分析结果和建议:")
        print("=" * 60)
        
        if geometry_fields:
            print("✓ 表中已存在几何字段，请检查是否需要添加新的 shape 字段")
            for field_name, field_type in geometry_fields:
                print(f"  现有几何字段: {field_name} ({field_type})")
        else:
            print("• 表中未发现几何字段，可以添加 shape 字段")
        
        if coordinate_fields:
            print("✓ 表中已存在坐标字段，可以基于这些字段生成 shape 数据")
            for field_name, field_type in coordinate_fields:
                print(f"  坐标字段: {field_name} ({field_type})")
        else:
            print("• 表中未发现坐标字段，需要先确定坐标数据来源")
        
        print("\n建议的 shape 字段规格:")
        print("- 字段名: shape")
        print("- 类型: LONGTEXT")
        print("- 用途: 存储 WKT 格式的几何数据")
        print("- 示例: POINT(104.065735 30.659462)")
        
        # 关闭连接
        analyzer.close()
        
    except Exception as e:
        print(f"✗ 分析失败: {e}")

if __name__ == '__main__':
    main()
