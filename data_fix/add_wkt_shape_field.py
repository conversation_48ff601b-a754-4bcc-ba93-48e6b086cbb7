#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
为 ecology_intelligent_land_plot 表添加 WKT 格式的 shape 字段
将现有的 LONGBLOB 格式几何数据转换为 WKT 文本格式
"""

import pymysql
import logging
from typing import Dict, Any
import struct

# 配置日志
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(levelname)s - %(message)s',
    handlers=[
        logging.FileHandler('wkt_shape_field_fix.log', encoding='utf-8'),
        logging.StreamHandler()
    ]
)
logger = logging.getLogger(__name__)

class WKTShapeFieldFixer:
    """为表添加 WKT 格式 shape 字段的修复器"""
    
    def __init__(self, db_config: Dict[str, Any]):
        """
        初始化修复器
        
        Args:
            db_config: 数据库配置
        """
        self.db_config = db_config
        self.connection = None
        self._connect_db()
    
    def _connect_db(self):
        """连接数据库"""
        try:
            self.connection = pymysql.connect(
                host=self.db_config['host'],
                port=self.db_config['port'],
                user=self.db_config['user'],
                password=self.db_config['password'],
                database=self.db_config['database'],
                charset='utf8mb4',
                autocommit=False
            )
            logger.info(f"成功连接到数据库: {self.db_config['host']}:{self.db_config['port']}/{self.db_config['database']}")
        except Exception as e:
            logger.error(f"数据库连接失败: {e}")
            raise
    
    def check_field_exists(self, table_name: str, field_name: str) -> bool:
        """检查字段是否存在"""
        try:
            with self.connection.cursor() as cursor:
                cursor.execute("""
                    SELECT COUNT(*) 
                    FROM information_schema.columns 
                    WHERE table_schema = %s AND table_name = %s AND column_name = %s
                """, (self.db_config['database'], table_name, field_name))
                result = cursor.fetchone()
                exists = result[0] > 0
                logger.info(f"表 {table_name} 中字段 {field_name} {'存在' if exists else '不存在'}")
                return exists
        except Exception as e:
            logger.error(f"检查字段是否存在失败: {e}")
            return False
    
    def add_wkt_shape_field(self, table_name: str) -> bool:
        """添加 WKT 格式的 shape 字段"""
        try:
            # 检查是否已存在 shape_wkt 字段
            if self.check_field_exists(table_name, 'shape_wkt'):
                logger.warning(f"字段 'shape_wkt' 已存在于表 {table_name} 中")
                return True
            
            # 添加 shape_wkt 字段
            alter_sql = f"""
            ALTER TABLE `{table_name}` 
            ADD COLUMN `shape_wkt` LONGTEXT DEFAULT NULL COMMENT '几何形状数据(WKT格式,存储经纬度)'
            """
            
            with self.connection.cursor() as cursor:
                cursor.execute(alter_sql)
                self.connection.commit()
                logger.info(f"成功为表 {table_name} 添加 shape_wkt 字段")
                return True
                
        except Exception as e:
            logger.error(f"添加 shape_wkt 字段失败: {e}")
            self.connection.rollback()
            return False
    
    def add_lng_lat_fields(self, table_name: str) -> bool:
        """添加经纬度字段"""
        try:
            fields_to_add = []
            
            # 检查 lng 字段
            if not self.check_field_exists(table_name, 'lng'):
                fields_to_add.append("`lng` DECIMAL(12,8) DEFAULT NULL COMMENT '经度'")
            
            # 检查 lat 字段
            if not self.check_field_exists(table_name, 'lat'):
                fields_to_add.append("`lat` DECIMAL(12,8) DEFAULT NULL COMMENT '纬度'")
            
            if not fields_to_add:
                logger.info("经纬度字段已存在")
                return True
            
            # 添加字段
            alter_sql = f"""
            ALTER TABLE `{table_name}` 
            ADD COLUMN {', ADD COLUMN '.join(fields_to_add)}
            """
            
            with self.connection.cursor() as cursor:
                cursor.execute(alter_sql)
                self.connection.commit()
                logger.info(f"成功为表 {table_name} 添加经纬度字段")
                return True
                
        except Exception as e:
            logger.error(f"添加经纬度字段失败: {e}")
            self.connection.rollback()
            return False
    
    def convert_blob_to_wkt_using_mysql(self, table_name: str, limit: int = None) -> bool:
        """使用 MySQL 的 ST_AsText 函数将 LONGBLOB 转换为 WKT"""
        try:
            logger.info("开始使用 MySQL 内置函数转换几何数据...")
            
            # 构建 WHERE 条件
            where_clause = "WHERE shape IS NOT NULL AND shape_wkt IS NULL"
            if limit:
                where_clause += f" LIMIT {limit}"
            
            # 使用 MySQL 的空间函数转换
            update_sql = f"""
            UPDATE `{table_name}` 
            SET shape_wkt = ST_AsText(ST_GeomFromWKB(shape))
            {where_clause}
            """
            
            with self.connection.cursor() as cursor:
                cursor.execute(update_sql)
                affected_rows = cursor.rowcount
                self.connection.commit()
                logger.info(f"成功转换 {affected_rows} 条记录的几何数据为 WKT 格式")
                return True
                
        except Exception as e:
            logger.error(f"转换几何数据失败: {e}")
            self.connection.rollback()
            return False
    
    def extract_coordinates_from_bbox(self, table_name: str, limit: int = None) -> bool:
        """从 bbox 字段提取中心点坐标"""
        try:
            logger.info("开始从 bbox 字段提取坐标...")
            
            # 构建 WHERE 条件
            where_clause = "WHERE bbox IS NOT NULL AND (lng IS NULL OR lat IS NULL)"
            if limit:
                where_clause += f" LIMIT {limit}"
            
            # 提取 bbox 中心点坐标
            update_sql = f"""
            UPDATE `{table_name}` 
            SET 
                lng = (
                    CAST(SUBSTRING_INDEX(bbox, ',', 1) AS DECIMAL(12,8)) + 
                    CAST(SUBSTRING_INDEX(SUBSTRING_INDEX(bbox, ',', 3), ',', -1) AS DECIMAL(12,8))
                ) / 2,
                lat = (
                    CAST(SUBSTRING_INDEX(SUBSTRING_INDEX(bbox, ',', 2), ',', -1) AS DECIMAL(12,8)) + 
                    CAST(SUBSTRING_INDEX(bbox, ',', -1) AS DECIMAL(12,8))
                ) / 2
            {where_clause}
            """
            
            with self.connection.cursor() as cursor:
                cursor.execute(update_sql)
                affected_rows = cursor.rowcount
                self.connection.commit()
                logger.info(f"成功为 {affected_rows} 条记录提取中心点坐标")
                return True
                
        except Exception as e:
            logger.error(f"提取坐标失败: {e}")
            self.connection.rollback()
            return False
    
    def get_conversion_status(self, table_name: str):
        """获取转换状态"""
        try:
            with self.connection.cursor() as cursor:
                # 总记录数
                cursor.execute(f"SELECT COUNT(*) FROM `{table_name}`")
                total_count = cursor.fetchone()[0]
                
                # 有 shape 数据的记录数
                cursor.execute(f"SELECT COUNT(*) FROM `{table_name}` WHERE shape IS NOT NULL")
                shape_count = cursor.fetchone()[0]
                
                # 有 shape_wkt 数据的记录数
                cursor.execute(f"SELECT COUNT(*) FROM `{table_name}` WHERE shape_wkt IS NOT NULL")
                wkt_count = cursor.fetchone()[0]
                
                # 有坐标数据的记录数
                cursor.execute(f"SELECT COUNT(*) FROM `{table_name}` WHERE lng IS NOT NULL AND lat IS NOT NULL")
                coord_count = cursor.fetchone()[0]
                
                logger.info(f"转换状态统计:")
                logger.info(f"  总记录数: {total_count}")
                logger.info(f"  有 shape 数据: {shape_count}")
                logger.info(f"  有 shape_wkt 数据: {wkt_count}")
                logger.info(f"  有坐标数据: {coord_count}")
                
                return {
                    'total': total_count,
                    'shape': shape_count,
                    'wkt': wkt_count,
                    'coord': coord_count
                }
                
        except Exception as e:
            logger.error(f"获取转换状态失败: {e}")
            return None
    
    def show_sample_data(self, table_name: str, limit: int = 3):
        """显示样本数据"""
        try:
            logger.info(f"=== 样本数据 (前{limit}条) ===")
            
            with self.connection.cursor(pymysql.cursors.DictCursor) as cursor:
                cursor.execute(f"""
                    SELECT id, name, lng, lat, 
                           LEFT(shape_wkt, 100) as shape_wkt_preview,
                           bbox
                    FROM `{table_name}` 
                    WHERE shape_wkt IS NOT NULL 
                    LIMIT %s
                """, (limit,))
                rows = cursor.fetchall()
                
                if not rows:
                    logger.info("没有找到已转换的数据")
                    return
                
                for i, row in enumerate(rows, 1):
                    logger.info(f"--- 记录 {i} ---")
                    for key, value in row.items():
                        logger.info(f"{key}: {value}")
                    logger.info("")
                
        except Exception as e:
            logger.error(f"显示样本数据失败: {e}")
    
    def close(self):
        """关闭数据库连接"""
        if self.connection:
            self.connection.close()
            logger.info("数据库连接已关闭")

def main():
    """主函数"""
    
    # 数据库配置
    db_config = {
        'host': 'localhost',
        'port': 3306,
        'user': 'root',
        'password': '123456',  # 请修改为实际密码
        'database': 'land'
    }
    
    # 目标表名
    table_name = 'ecology_intelligent_land_plot'
    
    try:
        print("=" * 60)
        print("为 ecology_intelligent_land_plot 表添加 WKT 格式 shape 字段")
        print("=" * 60)
        print(f"目标表: {table_name}")
        print(f"数据库: {db_config['host']}:{db_config['port']}/{db_config['database']}")
        print("-" * 60)
        
        # 创建修复器
        fixer = WKTShapeFieldFixer(db_config)
        
        # 显示当前状态
        print("\n当前状态:")
        status = fixer.get_conversion_status(table_name)
        
        # 确认操作
        print("\n" + "-" * 60)
        print("将执行以下操作:")
        print("1. 添加 shape_wkt 字段（WKT 格式几何数据）")
        print("2. 添加 lng, lat 字段（经纬度坐标）")
        print("3. 将现有 LONGBLOB 几何数据转换为 WKT 格式")
        print("4. 从 bbox 字段提取中心点坐标")
        
        confirm = input("\n确认执行这些操作? (Y/n): ").lower().strip()
        if confirm == 'n':
            print("操作已取消")
            return
        
        print("\n开始处理...")
        
        # 1. 添加 WKT shape 字段
        print("1. 添加 shape_wkt 字段...")
        if fixer.add_wkt_shape_field(table_name):
            print("✓ shape_wkt 字段添加成功")
        else:
            print("✗ shape_wkt 字段添加失败")
            return
        
        # 2. 添加经纬度字段
        print("2. 添加经纬度字段...")
        if fixer.add_lng_lat_fields(table_name):
            print("✓ 经纬度字段添加成功")
        else:
            print("✗ 经纬度字段添加失败")
        
        # 3. 转换几何数据
        print("3. 转换几何数据为 WKT 格式...")
        if fixer.convert_blob_to_wkt_using_mysql(table_name):
            print("✓ 几何数据转换成功")
        else:
            print("✗ 几何数据转换失败")
        
        # 4. 提取坐标
        print("4. 从 bbox 提取坐标...")
        if fixer.extract_coordinates_from_bbox(table_name):
            print("✓ 坐标提取成功")
        else:
            print("✗ 坐标提取失败")
        
        # 显示最终状态
        print("\n最终状态:")
        final_status = fixer.get_conversion_status(table_name)
        
        # 显示样本数据
        print("\n样本数据:")
        fixer.show_sample_data(table_name, limit=2)
        
        # 关闭连接
        fixer.close()
        
        print("\n" + "=" * 60)
        print("✓ 处理完成!")
        print()
        print("新增字段说明:")
        print("- shape_wkt: LONGTEXT - WKT 格式几何数据")
        print("- lng: DECIMAL(12,8) - 经度")
        print("- lat: DECIMAL(12,8) - 纬度")
        print()
        print("使用示例:")
        print("-- 查看 WKT 格式几何数据")
        print(f"SELECT id, name, lng, lat, LEFT(shape_wkt, 100) as shape_preview FROM {table_name} LIMIT 5;")
        print()
        print("-- 查询指定区域的数据")
        print(f"SELECT * FROM {table_name} WHERE lng BETWEEN 105.5 AND 105.7 AND lat BETWEEN 30.5 AND 30.7;")
        print()
        print(f"详细日志请查看: wkt_shape_field_fix.log")
        
    except Exception as e:
        print(f"✗ 操作失败: {e}")
        logger.error(f"程序执行失败: {e}")

if __name__ == '__main__':
    main()
