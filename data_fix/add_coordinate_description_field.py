#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
为 ecology_intelligent_land_plot 表添加经纬度描述字段
将 shape 或 shape_wkt 转换为经纬度的文本描述
"""

import pymysql
import logging
from typing import Dict, Any
import re

# 配置日志
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(levelname)s - %(message)s',
    handlers=[
        logging.FileHandler('coordinate_description_field.log', encoding='utf-8'),
        logging.StreamHandler()
    ]
)
logger = logging.getLogger(__name__)

class CoordinateDescriptionProcessor:
    """经纬度描述字段处理器"""
    
    def __init__(self, db_config: Dict[str, Any]):
        """
        初始化处理器
        
        Args:
            db_config: 数据库配置
        """
        self.db_config = db_config
        self.connection = None
        self._connect_db()
    
    def _connect_db(self):
        """连接数据库"""
        try:
            self.connection = pymysql.connect(
                host=self.db_config['host'],
                port=self.db_config['port'],
                user=self.db_config['user'],
                password=self.db_config['password'],
                database=self.db_config['database'],
                charset='utf8mb4',
                autocommit=False
            )
            logger.info(f"成功连接到数据库: {self.db_config['host']}:{self.db_config['port']}/{self.db_config['database']}")
        except Exception as e:
            logger.error(f"数据库连接失败: {e}")
            raise
    
    def check_field_exists(self, table_name: str, field_name: str) -> bool:
        """检查字段是否存在"""
        try:
            with self.connection.cursor() as cursor:
                cursor.execute("""
                    SELECT COUNT(*) 
                    FROM information_schema.columns 
                    WHERE table_schema = %s AND table_name = %s AND column_name = %s
                """, (self.db_config['database'], table_name, field_name))
                result = cursor.fetchone()
                exists = result[0] > 0
                logger.info(f"表 {table_name} 中字段 {field_name} {'存在' if exists else '不存在'}")
                return exists
        except Exception as e:
            logger.error(f"检查字段是否存在失败: {e}")
            return False
    
    def add_coordinate_description_field(self, table_name: str) -> bool:
        """添加经纬度描述字段"""
        try:
            # 检查字段是否已存在
            if self.check_field_exists(table_name, 'coordinate_description'):
                logger.warning(f"字段 'coordinate_description' 已存在于表 {table_name} 中")
                return True
            
            # 添加经纬度描述字段
            alter_sql = f"""
            ALTER TABLE `{table_name}` 
            ADD COLUMN `coordinate_description` TEXT DEFAULT NULL COMMENT '经纬度描述信息'
            """
            
            with self.connection.cursor() as cursor:
                cursor.execute(alter_sql)
                self.connection.commit()
                logger.info(f"成功为表 {table_name} 添加 coordinate_description 字段")
                return True
                
        except Exception as e:
            logger.error(f"添加 coordinate_description 字段失败: {e}")
            self.connection.rollback()
            return False
    
    def format_coordinate_description(self, lng: float, lat: float, bbox: str = None) -> str:
        """
        格式化经纬度描述
        
        Args:
            lng: 经度
            lat: 纬度
            bbox: 边界框字符串
            
        Returns:
            str: 格式化的经纬度描述
        """
        try:
            # 基本坐标描述
            description_parts = []
            
            # 1. 基本经纬度信息
            description_parts.append(f"中心坐标: 东经{lng:.6f}°, 北纬{lat:.6f}°")
            
            # 2. 度分秒格式
            lng_deg, lng_min, lng_sec = self._decimal_to_dms(lng)
            lat_deg, lat_min, lat_sec = self._decimal_to_dms(lat)
            description_parts.append(f"度分秒格式: 东经{lng_deg}°{lng_min}′{lng_sec:.2f}″, 北纬{lat_deg}°{lat_min}′{lat_sec:.2f}″")
            
            # 3. 边界框信息
            if bbox:
                try:
                    bbox_parts = bbox.split(',')
                    if len(bbox_parts) == 4:
                        min_lng, min_lat, max_lng, max_lat = map(float, bbox_parts)
                        description_parts.append(f"边界范围: 东经{min_lng:.6f}°~{max_lng:.6f}°, 北纬{min_lat:.6f}°~{max_lat:.6f}°")
                        
                        # 计算范围大小
                        lng_span = max_lng - min_lng
                        lat_span = max_lat - min_lat
                        description_parts.append(f"覆盖范围: 经度跨度{lng_span:.6f}°, 纬度跨度{lat_span:.6f}°")
                except:
                    pass
            
            # 4. 地理位置描述
            location_desc = self._get_location_description(lng, lat)
            if location_desc:
                description_parts.append(f"地理位置: {location_desc}")
            
            return " | ".join(description_parts)
            
        except Exception as e:
            logger.error(f"格式化坐标描述失败: {e}")
            return f"经度{lng:.6f}°, 纬度{lat:.6f}°"
    
    def _decimal_to_dms(self, decimal_degree: float) -> tuple:
        """将十进制度转换为度分秒"""
        try:
            degrees = int(decimal_degree)
            minutes_float = (decimal_degree - degrees) * 60
            minutes = int(minutes_float)
            seconds = (minutes_float - minutes) * 60
            return degrees, minutes, seconds
        except:
            return 0, 0, 0.0
    
    def _get_location_description(self, lng: float, lat: float) -> str:
        """根据经纬度获取地理位置描述"""
        try:
            # 基于坐标范围判断大致位置（针对遂宁市）
            if 105.5 <= lng <= 105.7 and 30.5 <= lat <= 30.7:
                if lng < 105.58:
                    if lat < 30.57:
                        return "遂宁市河东新区西南部"
                    else:
                        return "遂宁市河东新区西北部"
                else:
                    if lat < 30.57:
                        return "遂宁市河东新区东南部"
                    else:
                        return "遂宁市河东新区东北部"
            else:
                return "四川省遂宁市"
        except:
            return ""
    
    def extract_coordinates_from_wkt(self, wkt_text: str) -> tuple:
        """从 WKT 文本中提取坐标信息"""
        try:
            if not wkt_text:
                return None, None
            
            # 提取数字坐标
            numbers = re.findall(r'-?\d+\.?\d*', wkt_text)
            if len(numbers) >= 2:
                # 取前两个数字作为坐标
                x, y = float(numbers[0]), float(numbers[1])
                return x, y
            
            return None, None
            
        except Exception as e:
            logger.error(f"从 WKT 提取坐标失败: {e}")
            return None, None
    
    def update_coordinate_descriptions(self, table_name: str, batch_size: int = 100) -> bool:
        """更新经纬度描述字段"""
        try:
            logger.info("开始更新经纬度描述...")
            
            # 获取需要更新的记录
            with self.connection.cursor() as cursor:
                cursor.execute(f"""
                    SELECT id, lng, lat, bbox, shape_wkt
                    FROM `{table_name}` 
                    WHERE coordinate_description IS NULL
                    AND (lng IS NOT NULL OR shape_wkt IS NOT NULL)
                    LIMIT {batch_size * 10}
                """)
                records = cursor.fetchall()
            
            if not records:
                logger.info("没有需要更新的记录")
                return True
            
            logger.info(f"找到 {len(records)} 条需要更新的记录")
            
            updated_count = 0
            failed_count = 0
            
            # 批量处理
            for i in range(0, len(records), batch_size):
                batch = records[i:i + batch_size]
                batch_updates = []
                
                for record_id, lng, lat, bbox, shape_wkt in batch:
                    try:
                        # 优先使用 lng/lat，如果没有则从 shape_wkt 提取
                        if lng is not None and lat is not None:
                            use_lng, use_lat = lng, lat
                        elif shape_wkt:
                            use_lng, use_lat = self.extract_coordinates_from_wkt(shape_wkt)
                            if use_lng is None or use_lat is None:
                                failed_count += 1
                                logger.warning(f"记录 {record_id} 无法提取坐标")
                                continue
                        else:
                            failed_count += 1
                            logger.warning(f"记录 {record_id} 没有可用的坐标数据")
                            continue
                        
                        # 生成坐标描述
                        description = self.format_coordinate_description(use_lng, use_lat, bbox)
                        batch_updates.append((description, record_id))
                        
                    except Exception as e:
                        failed_count += 1
                        logger.error(f"处理记录 {record_id} 失败: {e}")
                
                # 批量更新数据库
                if batch_updates:
                    with self.connection.cursor() as cursor:
                        cursor.executemany(f"""
                            UPDATE `{table_name}` 
                            SET coordinate_description = %s 
                            WHERE id = %s
                        """, batch_updates)
                        self.connection.commit()
                        updated_count += len(batch_updates)
                        logger.info(f"已更新 {updated_count} 条记录")
            
            logger.info(f"经纬度描述更新完成: 成功 {updated_count} 条, 失败 {failed_count} 条")
            return True
            
        except Exception as e:
            logger.error(f"更新经纬度描述失败: {e}")
            self.connection.rollback()
            return False
    
    def show_sample_descriptions(self, table_name: str, limit: int = 3):
        """显示样本描述数据"""
        try:
            logger.info(f"=== 经纬度描述样本 (前{limit}条) ===")
            
            with self.connection.cursor(pymysql.cursors.DictCursor) as cursor:
                cursor.execute(f"""
                    SELECT id, name, lng, lat, coordinate_description
                    FROM `{table_name}` 
                    WHERE coordinate_description IS NOT NULL
                    ORDER BY id
                    LIMIT {limit}
                """)
                
                records = cursor.fetchall()
                
                for i, record in enumerate(records, 1):
                    logger.info(f"--- 记录 {i} ---")
                    logger.info(f"ID: {record['id']}")
                    logger.info(f"名称: {record['name']}")
                    logger.info(f"经度: {record['lng']}")
                    logger.info(f"纬度: {record['lat']}")
                    logger.info(f"描述: {record['coordinate_description']}")
                    logger.info("")
                
        except Exception as e:
            logger.error(f"显示样本数据失败: {e}")
    
    def get_statistics(self, table_name: str):
        """获取统计信息"""
        try:
            with self.connection.cursor() as cursor:
                # 总记录数
                cursor.execute(f"SELECT COUNT(*) FROM `{table_name}`")
                total = cursor.fetchone()[0]
                
                # 有描述的记录数
                cursor.execute(f"SELECT COUNT(*) FROM `{table_name}` WHERE coordinate_description IS NOT NULL")
                desc_count = cursor.fetchone()[0]
                
                logger.info("=== 统计信息 ===")
                logger.info(f"总记录数: {total}")
                logger.info(f"有经纬度描述: {desc_count} ({desc_count/total*100:.1f}%)")
                
                return {'total': total, 'description': desc_count}
                
        except Exception as e:
            logger.error(f"获取统计信息失败: {e}")
            return None
    
    def close(self):
        """关闭数据库连接"""
        if self.connection:
            self.connection.close()
            logger.info("数据库连接已关闭")

def main():
    """主函数"""
    
    # 数据库配置
    db_config = {
        'host': 'localhost',
        'port': 3306,
        'user': 'root',
        'password': '123456',
        'database': 'land'
    }
    
    # 目标表名
    table_name = 'ecology_intelligent_land_plot'
    
    try:
        print("=" * 60)
        print("为 ecology_intelligent_land_plot 表添加经纬度描述字段")
        print("=" * 60)
        print(f"目标表: {table_name}")
        print(f"数据库: {db_config['host']}:{db_config['port']}/{db_config['database']}")
        print("-" * 60)
        
        # 创建处理器
        processor = CoordinateDescriptionProcessor(db_config)
        
        # 显示当前状态
        print("\n当前状态:")
        stats = processor.get_statistics(table_name)
        
        # 确认操作
        print("\n" + "-" * 60)
        print("将执行以下操作:")
        print("1. 添加 coordinate_description 字段（TEXT 类型）")
        print("2. 从 lng/lat 或 shape_wkt 提取坐标信息")
        print("3. 生成详细的经纬度描述文本")
        print("4. 包含度分秒格式、边界范围、地理位置等信息")
        
        confirm = input("\n确认执行操作? (Y/n): ").lower().strip()
        if confirm == 'n':
            print("操作已取消")
            return
        
        print("\n开始处理...")
        
        # 1. 添加字段
        print("1. 添加 coordinate_description 字段...")
        if processor.add_coordinate_description_field(table_name):
            print("✓ coordinate_description 字段添加成功")
        else:
            print("✗ coordinate_description 字段添加失败")
            return
        
        # 2. 更新描述数据
        print("2. 更新经纬度描述...")
        if processor.update_coordinate_descriptions(table_name, batch_size=50):
            print("✓ 经纬度描述更新成功")
        else:
            print("✗ 经纬度描述更新失败")
            return
        
        # 显示最终状态
        print("\n最终状态:")
        final_stats = processor.get_statistics(table_name)
        
        # 显示样本数据
        print("\n样本数据:")
        processor.show_sample_descriptions(table_name, limit=2)
        
        # 关闭连接
        processor.close()
        
        print("\n" + "=" * 60)
        print("✓ 经纬度描述字段处理完成!")
        print()
        print("新增字段说明:")
        print("- coordinate_description: TEXT - 详细的经纬度描述信息")
        print("- 包含: 中心坐标、度分秒格式、边界范围、地理位置")
        print()
        print("使用示例:")
        print(f"SELECT id, name, coordinate_description FROM {table_name} LIMIT 5;")
        print()
        print(f"详细日志请查看: coordinate_description_field.log")
        
    except Exception as e:
        print(f"✗ 操作失败: {e}")
        logger.error(f"程序执行失败: {e}")

if __name__ == '__main__':
    main()
