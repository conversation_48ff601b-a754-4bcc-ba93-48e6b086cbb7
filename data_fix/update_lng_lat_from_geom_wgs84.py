#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
从 geom_wgs84 字段提取中心点坐标更新 lng 和 lat 字段
确保 lng/lat 与 geom_wgs84 的中心点坐标一致
"""

import pymysql
import logging
from typing import Dict, Any

# 配置日志
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(levelname)s - %(message)s',
    handlers=[
        logging.FileHandler('update_lng_lat_from_geom.log', encoding='utf-8'),
        logging.StreamHandler()
    ]
)
logger = logging.getLogger(__name__)

class LngLatUpdater:
    """从 geom_wgs84 更新 lng/lat 坐标的处理器"""
    
    def __init__(self, db_config: Dict[str, Any]):
        """
        初始化处理器
        
        Args:
            db_config: 数据库配置
        """
        self.db_config = db_config
        self.connection = None
        self._connect_db()
    
    def _connect_db(self):
        """连接数据库"""
        try:
            self.connection = pymysql.connect(
                host=self.db_config['host'],
                port=self.db_config['port'],
                user=self.db_config['user'],
                password=self.db_config['password'],
                database=self.db_config['database'],
                charset='utf8mb4',
                autocommit=False
            )
            logger.info(f"成功连接到数据库: {self.db_config['host']}:{self.db_config['port']}/{self.db_config['database']}")
        except Exception as e:
            logger.error(f"数据库连接失败: {e}")
            raise
    
    def check_data_status(self, table_name: str):
        """检查当前数据状态"""
        try:
            with self.connection.cursor() as cursor:
                # 总记录数
                cursor.execute(f"SELECT COUNT(*) FROM `{table_name}`")
                total = cursor.fetchone()[0]
                
                # 有 geom_wgs84 数据的记录
                cursor.execute(f"SELECT COUNT(*) FROM `{table_name}` WHERE geom_wgs84 IS NOT NULL")
                geom_count = cursor.fetchone()[0]
                
                # 有 lng/lat 数据的记录
                cursor.execute(f"SELECT COUNT(*) FROM `{table_name}` WHERE lng IS NOT NULL AND lat IS NOT NULL")
                coord_count = cursor.fetchone()[0]
                
                logger.info("=== 当前数据状态 ===")
                logger.info(f"总记录数: {total}")
                logger.info(f"有 geom_wgs84 数据: {geom_count}")
                logger.info(f"有 lng/lat 数据: {coord_count}")
                
                return {'total': total, 'geom': geom_count, 'coord': coord_count}
                
        except Exception as e:
            logger.error(f"检查数据状态失败: {e}")
            return None
    
    def show_current_coordinates_sample(self, table_name: str, limit: int = 3):
        """显示当前坐标样本"""
        try:
            logger.info(f"=== 当前坐标样本 (前{limit}条) ===")
            
            with self.connection.cursor() as cursor:
                cursor.execute(f"""
                    SELECT id, name, lng, lat
                    FROM `{table_name}` 
                    WHERE lng IS NOT NULL AND lat IS NOT NULL
                    ORDER BY id
                    LIMIT {limit}
                """)
                
                records = cursor.fetchall()
                
                for i, record in enumerate(records, 1):
                    logger.info(f"--- 记录 {i} ---")
                    logger.info(f"ID: {record[0]}")
                    logger.info(f"名称: {record[1]}")
                    logger.info(f"当前经度: {record[2]:.8f}")
                    logger.info(f"当前纬度: {record[3]:.8f}")
                
        except Exception as e:
            logger.error(f"显示坐标样本失败: {e}")
    
    def update_lng_lat_from_geom_wgs84(self, table_name: str, batch_size: int = 100) -> bool:
        """
        从 geom_wgs84 字段提取中心点坐标更新 lng 和 lat 字段
        
        注意：MySQL 8.0 对地理坐标系的 MULTIPOLYGON 不支持 ST_Centroid，
        我们需要使用其他方法来获取中心点
        """
        try:
            logger.info("开始从 geom_wgs84 更新 lng/lat 坐标...")
            
            # 获取需要更新的记录
            with self.connection.cursor() as cursor:
                cursor.execute(f"""
                    SELECT COUNT(*) 
                    FROM `{table_name}` 
                    WHERE geom_wgs84 IS NOT NULL
                """)
                total_count = cursor.fetchone()[0]
            
            if total_count == 0:
                logger.warning("没有 geom_wgs84 数据可用于更新")
                return False
            
            logger.info(f"找到 {total_count} 条记录需要更新坐标")
            
            updated_count = 0
            failed_count = 0
            
            # 分批处理
            offset = 0
            while offset < total_count:
                with self.connection.cursor() as cursor:
                    # 获取一批记录
                    cursor.execute(f"""
                        SELECT id, geom_wgs84
                        FROM `{table_name}` 
                        WHERE geom_wgs84 IS NOT NULL
                        ORDER BY id
                        LIMIT {batch_size} OFFSET {offset}
                    """)
                    
                    batch_records = cursor.fetchall()
                    if not batch_records:
                        break
                    
                    batch_updates = []
                    
                    for record_id, geom_wgs84_data in batch_records:
                        try:
                            # 使用 ST_AsText 获取 WKT，然后解析中心点
                            # 由于 ST_Centroid 不支持地理坐标系的 MULTIPOLYGON，
                            # 我们使用边界框的中心点作为替代
                            cursor.execute("""
                                SELECT 
                                    (ST_X(ST_PointN(ST_ExteriorRing(ST_GeometryN(geom_wgs84, 1)), 1)) + 
                                     ST_X(ST_PointN(ST_ExteriorRing(ST_GeometryN(geom_wgs84, 1)), 3))) / 2 as center_x,
                                    (ST_Y(ST_PointN(ST_ExteriorRing(ST_GeometryN(geom_wgs84, 1)), 1)) + 
                                     ST_Y(ST_PointN(ST_ExteriorRing(ST_GeometryN(geom_wgs84, 1)), 3))) / 2 as center_y
                                FROM ecology_intelligent_land_plot 
                                WHERE id = %s
                            """, (record_id,))
                            
                            center_result = cursor.fetchone()
                            if center_result and center_result[0] is not None and center_result[1] is not None:
                                # 注意：geom_wgs84 中的坐标顺序是 (纬度, 经度)
                                # 所以 center_x 是纬度，center_y 是经度
                                lat = center_result[0]  # X 坐标是纬度
                                lng = center_result[1]  # Y 坐标是经度
                                
                                batch_updates.append((lng, lat, record_id))
                            else:
                                # 如果上面的方法失败，尝试使用简单的边界框方法
                                cursor.execute("""
                                    SELECT 
                                        (ST_X(ST_PointN(ST_ExteriorRing(ST_Envelope(geom_wgs84)), 1)) + 
                                         ST_X(ST_PointN(ST_ExteriorRing(ST_Envelope(geom_wgs84)), 3))) / 2 as center_lng,
                                        (ST_Y(ST_PointN(ST_ExteriorRing(ST_Envelope(geom_wgs84)), 1)) + 
                                         ST_Y(ST_PointN(ST_ExteriorRing(ST_Envelope(geom_wgs84)), 3))) / 2 as center_lat
                                    FROM ecology_intelligent_land_plot 
                                    WHERE id = %s
                                """, (record_id,))
                                
                                envelope_result = cursor.fetchone()
                                if envelope_result and envelope_result[0] is not None and envelope_result[1] is not None:
                                    # 这里的结果顺序是 (纬度, 经度)
                                    lat = envelope_result[0]
                                    lng = envelope_result[1]
                                    batch_updates.append((lng, lat, record_id))
                                else:
                                    failed_count += 1
                                    logger.warning(f"记录 {record_id} 无法提取中心点坐标")
                            
                        except Exception as e:
                            failed_count += 1
                            logger.error(f"处理记录 {record_id} 失败: {e}")
                    
                    # 批量更新坐标
                    if batch_updates:
                        cursor.executemany(f"""
                            UPDATE `{table_name}` 
                            SET lng = %s, lat = %s 
                            WHERE id = %s
                        """, batch_updates)
                        self.connection.commit()
                        updated_count += len(batch_updates)
                        logger.info(f"已更新 {updated_count}/{total_count} 条记录")
                
                offset += batch_size
            
            logger.info(f"坐标更新完成: 成功 {updated_count} 条, 失败 {failed_count} 条")
            return True
            
        except Exception as e:
            logger.error(f"更新坐标失败: {e}")
            self.connection.rollback()
            return False
    
    def verify_updated_coordinates(self, table_name: str, limit: int = 5):
        """验证更新后的坐标"""
        try:
            logger.info(f"=== 更新后坐标验证 (前{limit}条) ===")
            
            with self.connection.cursor() as cursor:
                cursor.execute(f"""
                    SELECT id, name, lng, lat
                    FROM `{table_name}` 
                    WHERE lng IS NOT NULL AND lat IS NOT NULL
                    ORDER BY id
                    LIMIT {limit}
                """)
                
                records = cursor.fetchall()
                
                for i, record in enumerate(records, 1):
                    logger.info(f"--- 记录 {i} ---")
                    logger.info(f"ID: {record[0]}")
                    logger.info(f"名称: {record[1]}")
                    logger.info(f"更新后经度: {record[2]:.8f}")
                    logger.info(f"更新后纬度: {record[3]:.8f}")
                
                # 检查坐标范围
                cursor.execute(f"""
                    SELECT MIN(lng), MAX(lng), MIN(lat), MAX(lat) 
                    FROM `{table_name}` 
                    WHERE lng IS NOT NULL AND lat IS NOT NULL
                """)
                
                coord_range = cursor.fetchone()
                if coord_range[0] is not None:
                    logger.info(f"经度范围: {coord_range[0]:.6f} ~ {coord_range[1]:.6f}")
                    logger.info(f"纬度范围: {coord_range[2]:.6f} ~ {coord_range[3]:.6f}")
                    
                    # 验证坐标是否在合理范围内
                    if 105.0 <= coord_range[0] <= 106.0 and 105.0 <= coord_range[1] <= 106.0:
                        if 30.0 <= coord_range[2] <= 31.0 and 30.0 <= coord_range[3] <= 31.0:
                            logger.info("✓ 坐标范围正常，符合遂宁市地理位置")
                        else:
                            logger.warning("⚠ 纬度范围异常")
                    else:
                        logger.warning("⚠ 经度范围异常")
                
        except Exception as e:
            logger.error(f"验证坐标失败: {e}")
    
    def close(self):
        """关闭数据库连接"""
        if self.connection:
            self.connection.close()
            logger.info("数据库连接已关闭")

def main():
    """主函数"""
    
    # 数据库配置
    db_config = {
        'host': 'localhost',
        'port': 3306,
        'user': 'root',
        'password': '123456',
        'database': 'land'
    }
    
    # 目标表名
    table_name = 'ecology_intelligent_land_plot'
    
    try:
        print("=" * 60)
        print("从 geom_wgs84 更新 lng/lat 坐标")
        print("=" * 60)
        print(f"目标表: {table_name}")
        print(f"数据库: {db_config['host']}:{db_config['port']}/{db_config['database']}")
        print("-" * 60)
        
        # 创建更新器
        updater = LngLatUpdater(db_config)
        
        # 显示当前状态
        print("\n当前状态:")
        stats = updater.check_data_status(table_name)
        
        # 显示当前坐标样本
        print("\n当前坐标样本:")
        updater.show_current_coordinates_sample(table_name, limit=3)
        
        # 确认操作
        print("\n" + "-" * 60)
        print("将执行以下操作:")
        print("1. 从 geom_wgs84 字段提取中心点坐标")
        print("2. 更新 lng 和 lat 字段")
        print("3. 确保坐标与 geom_wgs84 的中心点一致")
        print("4. 验证更新结果")
        
        confirm = input("\n确认执行坐标更新? (Y/n): ").lower().strip()
        if confirm == 'n':
            print("操作已取消")
            return
        
        print("\n开始更新...")
        
        # 执行坐标更新
        if updater.update_lng_lat_from_geom_wgs84(table_name, batch_size=50):
            print("✓ 坐标更新成功")
        else:
            print("✗ 坐标更新失败")
            return
        
        # 显示最终状态
        print("\n最终状态:")
        final_stats = updater.check_data_status(table_name)
        
        # 验证更新结果
        print("\n更新结果验证:")
        updater.verify_updated_coordinates(table_name, limit=3)
        
        # 关闭连接
        updater.close()
        
        print("\n" + "=" * 60)
        print("✓ lng/lat 坐标更新完成!")
        print()
        print("更新说明:")
        print("- lng/lat 现在是 geom_wgs84 字段的中心点坐标")
        print("- 坐标格式: 经度(lng), 纬度(lat)")
        print("- 坐标系: WGS84 (EPSG:4326)")
        print()
        print("验证查询:")
        print(f"SELECT id, name, lng, lat FROM {table_name} ORDER BY id LIMIT 5;")
        print()
        print(f"详细日志: update_lng_lat_from_geom.log")
        
    except Exception as e:
        print(f"✗ 更新失败: {e}")
        logger.error(f"程序执行失败: {e}")

if __name__ == '__main__':
    main()
