#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
基于 Java 算法的 WKB 坐标转换脚本
使用精确的 CGCS2000 到 WGS84 坐标转换算法
更新 ecology_intelligent_land_plot 表的经纬度坐标
"""

import pymysql
import logging
from typing import Dict, Any, Tuple
from pyproj import CRS, Transformer
import shapely.wkb as wkb

# 配置日志
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(levelname)s - %(message)s',
    handlers=[
        logging.FileHandler('java_algorithm_coordinate_conversion.log', encoding='utf-8'),
        logging.StreamHandler()
    ]
)
logger = logging.getLogger(__name__)

class JavaAlgorithmCoordinateConverter:
    """基于 Java 算法的坐标转换器"""

    def __init__(self, db_config: Dict[str, Any]):
        """
        初始化转换器

        Args:
            db_config: 数据库配置
        """
        self.db_config = db_config
        self.connection = None
        self.transformer = None
        self._connect_db()
        self._setup_transformer()
    
    def _connect_db(self):
        """连接数据库"""
        try:
            self.connection = pymysql.connect(
                host=self.db_config['host'],
                port=self.db_config['port'],
                user=self.db_config['user'],
                password=self.db_config['password'],
                database=self.db_config['database'],
                charset='utf8mb4',
                autocommit=False
            )
            logger.info(f"成功连接到数据库: {self.db_config['host']}:{self.db_config['port']}/{self.db_config['database']}")
        except Exception as e:
            logger.error(f"数据库连接失败: {e}")
            raise
    
    def check_table_exists(self, table_name: str) -> bool:
        """
        检查表是否存在
        
        Args:
            table_name: 表名
            
        Returns:
            bool: 表是否存在
        """
        try:
            with self.connection.cursor() as cursor:
                cursor.execute("""
                    SELECT COUNT(*) 
                    FROM information_schema.tables 
                    WHERE table_schema = %s AND table_name = %s
                """, (self.db_config['database'], table_name))
                result = cursor.fetchone()
                exists = result[0] > 0
                logger.info(f"表 {table_name} {'存在' if exists else '不存在'}")
                return exists
        except Exception as e:
            logger.error(f"检查表是否存在失败: {e}")
            return False
    
    def check_field_exists(self, table_name: str, field_name: str) -> bool:
        """
        检查字段是否存在
        
        Args:
            table_name: 表名
            field_name: 字段名
            
        Returns:
            bool: 字段是否存在
        """
        try:
            with self.connection.cursor() as cursor:
                cursor.execute("""
                    SELECT COUNT(*) 
                    FROM information_schema.columns 
                    WHERE table_schema = %s AND table_name = %s AND column_name = %s
                """, (self.db_config['database'], table_name, field_name))
                result = cursor.fetchone()
                exists = result[0] > 0
                logger.info(f"表 {table_name} 中字段 {field_name} {'存在' if exists else '不存在'}")
                return exists
        except Exception as e:
            logger.error(f"检查字段是否存在失败: {e}")
            return False
    
    def get_table_structure(self, table_name: str) -> list:
        """
        获取表结构
        
        Args:
            table_name: 表名
            
        Returns:
            list: 表结构信息
        """
        try:
            with self.connection.cursor() as cursor:
                cursor.execute(f"DESCRIBE `{table_name}`")
                columns = cursor.fetchall()
                logger.info(f"表 {table_name} 当前字段数量: {len(columns)}")
                return columns
        except Exception as e:
            logger.error(f"获取表结构失败: {e}")
            return []
    
    def add_shape_field(self, table_name: str) -> bool:
        """
        为表添加 shape 字段
        
        Args:
            table_name: 表名
            
        Returns:
            bool: 添加是否成功
        """
        try:
            # 检查表是否存在
            if not self.check_table_exists(table_name):
                logger.error(f"表 {table_name} 不存在，无法添加字段")
                return False
            
            # 检查字段是否已存在
            if self.check_field_exists(table_name, 'shape'):
                logger.warning(f"字段 'shape' 已存在于表 {table_name} 中")
                return True
            
            # 添加 shape 字段
            alter_sql = f"""
            ALTER TABLE `{table_name}` 
            ADD COLUMN `shape` LONGTEXT DEFAULT NULL COMMENT '几何形状数据(WKT格式,存储经纬度)'
            """
            
            with self.connection.cursor() as cursor:
                cursor.execute(alter_sql)
                self.connection.commit()
                logger.info(f"成功为表 {table_name} 添加 shape 字段")
                return True
                
        except Exception as e:
            logger.error(f"添加 shape 字段失败: {e}")
            self.connection.rollback()
            return False
    
    def add_shape_index(self, table_name: str) -> bool:
        """
        为 shape 字段添加索引（可选）
        
        Args:
            table_name: 表名
            
        Returns:
            bool: 添加索引是否成功
        """
        try:
            # 检查索引是否已存在
            with self.connection.cursor() as cursor:
                cursor.execute("""
                    SELECT COUNT(*) 
                    FROM information_schema.statistics 
                    WHERE table_schema = %s AND table_name = %s AND index_name = 'idx_shape'
                """, (self.db_config['database'], table_name))
                result = cursor.fetchone()
                
                if result[0] > 0:
                    logger.info(f"索引 idx_shape 已存在于表 {table_name}")
                    return True
                
                # 创建索引（对于 LONGTEXT 字段，我们创建前缀索引）
                index_sql = f"""
                CREATE INDEX `idx_shape` ON `{table_name}` (`shape`(255))
                """
                
                cursor.execute(index_sql)
                self.connection.commit()
                logger.info(f"成功为表 {table_name} 的 shape 字段创建索引")
                return True
                
        except Exception as e:
            logger.warning(f"创建 shape 字段索引失败（这是可选操作）: {e}")
            self.connection.rollback()
            return False
    
    def show_table_info(self, table_name: str):
        """
        显示表信息
        
        Args:
            table_name: 表名
        """
        try:
            logger.info(f"=== 表 {table_name} 信息 ===")
            
            # 显示表结构
            columns = self.get_table_structure(table_name)
            if columns:
                logger.info("字段列表:")
                for col in columns:
                    field_name = col[0]
                    field_type = col[1]
                    is_null = col[2]
                    key = col[3]
                    default = col[4]
                    extra = col[5]
                    logger.info(f"  - {field_name}: {field_type} (NULL: {is_null}, KEY: {key}, DEFAULT: {default}, EXTRA: {extra})")
            
            # 显示记录数量
            with self.connection.cursor() as cursor:
                cursor.execute(f"SELECT COUNT(*) FROM `{table_name}`")
                count = cursor.fetchone()[0]
                logger.info(f"记录数量: {count}")
                
        except Exception as e:
            logger.error(f"显示表信息失败: {e}")
    
    def close(self):
        """关闭数据库连接"""
        if self.connection:
            self.connection.close()
            logger.info("数据库连接已关闭")

def main():
    """主函数"""
    
    # 数据库配置 - 使用您提供的配置
    db_config = {
        'host': 'localhost',
        'port': 3306,
        'user': 'root',
        'password': '123456',  # 请修改为实际密码
        'database': 'land'
    }
    
    # 目标表名
    table_name = 'ecology_intelligent_land_plot'
    
    try:
        print("=" * 60)
        print("为 ecology_intelligent_land_plot 表添加 shape 字段")
        print("=" * 60)
        print(f"目标表: {table_name}")
        print(f"数据库: {db_config['host']}:{db_config['port']}/{db_config['database']}")
        print("-" * 60)
        
        # 创建修复器
        fixer = ShapeFieldFixer(db_config)
        
        # 显示修改前的表信息
        print("\n修改前的表信息:")
        fixer.show_table_info(table_name)
        
        # 确认操作
        print("\n" + "-" * 60)
        confirm = input("确认为表添加 shape 字段? (Y/n): ").lower().strip()
        if confirm == 'n':
            print("操作已取消")
            return
        
        print("\n开始添加字段...")
        
        # 添加 shape 字段
        if fixer.add_shape_field(table_name):
            print("✓ shape 字段添加成功")
            
            # 可选：添加索引
            print("正在为 shape 字段创建索引...")
            if fixer.add_shape_index(table_name):
                print("✓ shape 字段索引创建成功")
            else:
                print("⚠ shape 字段索引创建失败（不影响主要功能）")
        else:
            print("✗ shape 字段添加失败")
            return
        
        # 显示修改后的表信息
        print("\n修改后的表信息:")
        fixer.show_table_info(table_name)
        
        # 关闭连接
        fixer.close()
        
        print("\n" + "=" * 60)
        print("✓ 字段添加完成!")
        print()
        print("新增字段说明:")
        print("- 字段名: shape")
        print("- 类型: LONGTEXT")
        print("- 用途: 存储经纬度的几何数据（WKT格式）")
        print("- 示例数据: POINT(104.065735 30.659462)")
        print()
        print("使用示例:")
        print("-- 查看新字段")
        print(f"DESCRIBE {table_name};")
        print()
        print("-- 更新 shape 字段（示例）")
        print(f"UPDATE {table_name} SET shape = CONCAT('POINT(', lng, ' ', lat, ')') WHERE lng IS NOT NULL AND lat IS NOT NULL;")
        print()
        print(f"详细日志请查看: shape_field_fix.log")
        
    except Exception as e:
        print(f"✗ 操作失败: {e}")
        logger.error(f"程序执行失败: {e}")

if __name__ == '__main__':
    main()
