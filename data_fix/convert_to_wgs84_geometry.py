#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
将 shape/shape_wkt 转换为经纬度坐标系的 GEOMETRY 字段
基于 Java 算法将 CGCS2000 投影坐标转换为 WGS84 经纬度坐标
存储在 GEOMETRY 类型字段中
"""

import pymysql
import logging
from typing import Dict, Any
from pyproj import CRS, Transformer
import shapely.wkb as wkb
from shapely.ops import transform
from shapely.geometry import Point, Polygon, MultiPolygon

# 配置日志
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(levelname)s - %(message)s',
    handlers=[
        logging.FileHandler('wgs84_geometry_conversion.log', encoding='utf-8'),
        logging.StreamHandler()
    ]
)
logger = logging.getLogger(__name__)

class WGS84GeometryConverter:
    """将投影坐标系几何数据转换为 WGS84 经纬度坐标系的转换器"""
    
    def __init__(self, db_config: Dict[str, Any]):
        """
        初始化转换器
        
        Args:
            db_config: 数据库配置
        """
        self.db_config = db_config
        self.connection = None
        self.transformer = None
        self._connect_db()
        self._setup_transformer()
    
    def _connect_db(self):
        """连接数据库"""
        try:
            self.connection = pymysql.connect(
                host=self.db_config['host'],
                port=self.db_config['port'],
                user=self.db_config['user'],
                password=self.db_config['password'],
                database=self.db_config['database'],
                charset='utf8mb4',
                autocommit=False
            )
            logger.info(f"成功连接到数据库: {self.db_config['host']}:{self.db_config['port']}/{self.db_config['database']}")
        except Exception as e:
            logger.error(f"数据库连接失败: {e}")
            raise
    
    def _setup_transformer(self):
        """设置坐标转换器 - 基于 Java 代码的 CGCS2000 定义"""
        try:
            # 使用与 Java 代码完全相同的 CGCS2000 3度带第35带投影坐标系定义
            cgcs2000_wkt = '''PROJCS["CGCS2000 3 Degree GK Zone 35",
    GEOGCS["China Geodetic Coordinate System 2000",
        DATUM["D China 2000",
            SPHEROID["CGCS2000",6378137.0,298.257222101],
            TOWGS84[0,0,0,0,0,0,0]],
        PRIMEM["Greenwich",0.0],
        UNIT["Degree",0.0174532925199433]],
    PROJECTION["Gauss_Kruger"],
    PARAMETER["latitude_of_origin",0.0],
    PARAMETER["central_meridian",105.0],
    PARAMETER["scale_factor",1.0],
    PARAMETER["false_easting",35500000.0],
    PARAMETER["false_northing",0.0],
    UNIT["Meter",1.0]]'''
            
            # 创建坐标系
            source_crs = CRS.from_wkt(cgcs2000_wkt)
            target_crs = CRS.from_epsg(4326)  # WGS84 经纬度坐标系
            
            # 创建转换器
            self.transformer = Transformer.from_crs(source_crs, target_crs, always_xy=True)
            logger.info("CGCS2000 到 WGS84 坐标转换器初始化成功")
            
        except Exception as e:
            logger.error(f"坐标转换器初始化失败: {e}")
            raise
    
    def check_field_exists(self, table_name: str, field_name: str) -> bool:
        """检查字段是否存在"""
        try:
            with self.connection.cursor() as cursor:
                cursor.execute("""
                    SELECT COUNT(*) 
                    FROM information_schema.columns 
                    WHERE table_schema = %s AND table_name = %s AND column_name = %s
                """, (self.db_config['database'], table_name, field_name))
                result = cursor.fetchone()
                exists = result[0] > 0
                logger.info(f"表 {table_name} 中字段 {field_name} {'存在' if exists else '不存在'}")
                return exists
        except Exception as e:
            logger.error(f"检查字段是否存在失败: {e}")
            return False
    
    def add_wgs84_geometry_field(self, table_name: str, field_name: str = 'geom_wgs84') -> bool:
        """添加 WGS84 经纬度坐标系的 GEOMETRY 字段"""
        try:
            # 检查字段是否已存在
            if self.check_field_exists(table_name, field_name):
                logger.warning(f"字段 '{field_name}' 已存在于表 {table_name} 中")
                return True
            
            # 添加 GEOMETRY 字段
            alter_sql = f"""
            ALTER TABLE `{table_name}` 
            ADD COLUMN `{field_name}` GEOMETRY DEFAULT NULL COMMENT 'WGS84经纬度坐标系几何数据'
            """
            
            with self.connection.cursor() as cursor:
                cursor.execute(alter_sql)
                self.connection.commit()
                logger.info(f"成功为表 {table_name} 添加 {field_name} 字段")
                return True
                
        except Exception as e:
            logger.error(f"添加 {field_name} 字段失败: {e}")
            self.connection.rollback()
            return False
    
    def transform_geometry_to_wgs84(self, geometry):
        """将几何对象从投影坐标系转换为 WGS84 经纬度坐标系"""
        try:
            # 使用 shapely 的 transform 函数进行坐标转换
            def transform_coords(x, y, z=None):
                # 转换坐标
                lon, lat = self.transformer.transform(x, y)
                return lon, lat

            # 转换几何对象
            transformed_geom = transform(transform_coords, geometry)
            return transformed_geom

        except Exception as e:
            logger.error(f"几何对象坐标转换失败: {e}")
            return None

    def _swap_coordinates_in_wkt(self, wkt_text: str) -> str:
        """
        交换 WKT 中的坐标顺序 (经度,纬度) -> (纬度,经度)
        MySQL 8.0 的 SRID 4326 期望 (纬度,经度) 格式
        """
        try:
            import re

            # 匹配坐标对的正则表达式
            coord_pattern = r'([0-9.-]+)\s+([0-9.-]+)'

            def swap_coords(match):
                x, y = match.groups()
                # 交换 x 和 y (经度和纬度)
                return f"{y} {x}"

            # 替换所有坐标对
            swapped_wkt = re.sub(coord_pattern, swap_coords, wkt_text)
            return swapped_wkt

        except Exception as e:
            logger.error(f"交换 WKT 坐标失败: {e}")
            return wkt_text
    
    def convert_wkb_to_wgs84_geometry(self, table_name: str, field_name: str = 'geom_wgs84', batch_size: int = 50) -> bool:
        """将 WKB 数据转换为 WGS84 经纬度坐标系的 GEOMETRY"""
        try:
            logger.info("开始将 WKB 数据转换为 WGS84 经纬度坐标系...")
            
            # 获取需要转换的记录
            with self.connection.cursor() as cursor:
                cursor.execute(f"""
                    SELECT id, shape 
                    FROM `{table_name}` 
                    WHERE shape IS NOT NULL AND `{field_name}` IS NULL
                    ORDER BY id
                """)
                records = cursor.fetchall()
            
            if not records:
                logger.info("没有需要转换的 WKB 记录")
                return True
            
            logger.info(f"找到 {len(records)} 条需要转换的记录")
            
            converted_count = 0
            failed_count = 0
            
            # 批量处理
            for i in range(0, len(records), batch_size):
                batch = records[i:i + batch_size]
                batch_updates = []
                
                for record_id, wkb_data in batch:
                    try:
                        # 解析 WKB 数据
                        geometry = wkb.loads(wkb_data)
                        
                        # 转换为 WGS84 坐标系
                        wgs84_geometry = self.transform_geometry_to_wgs84(geometry)
                        
                        if wgs84_geometry:
                            # MySQL 8.0 的 SRID 4326 期望 POINT(纬度 经度) 格式
                            # 需要交换坐标顺序
                            wgs84_wkt_swapped = self._swap_coordinates_in_wkt(wgs84_geometry.wkt)

                            # 验证坐标范围
                            if hasattr(wgs84_geometry, 'centroid'):
                                centroid = wgs84_geometry.centroid
                                if centroid.x < -180 or centroid.x > 180 or centroid.y < -90 or centroid.y > 90:
                                    logger.warning(f"记录 {record_id} 坐标超出有效范围: 经度={centroid.x}, 纬度={centroid.y}")
                                    failed_count += 1
                                    continue

                            batch_updates.append((wgs84_wkt_swapped, record_id))
                        else:
                            failed_count += 1
                            logger.warning(f"记录 {record_id} 几何转换失败")
                            
                    except Exception as e:
                        failed_count += 1
                        logger.error(f"处理记录 {record_id} 失败: {e}")
                
                # 批量更新数据库
                if batch_updates:
                    with self.connection.cursor() as cursor:
                        # 逐条更新以便更好地处理错误
                        for wgs84_wkt, record_id in batch_updates:
                            try:
                                cursor.execute(f"""
                                    UPDATE `{table_name}`
                                    SET `{field_name}` = ST_GeomFromText(%s, 4326)
                                    WHERE id = %s
                                """, (wgs84_wkt, record_id))
                            except Exception as e:
                                logger.error(f"更新记录 {record_id} 失败: {e}")
                                logger.debug(f"WKT 数据: {wgs84_wkt[:200]}...")
                                failed_count += 1
                                continue

                        self.connection.commit()
                        converted_count += len(batch_updates)
                        logger.info(f"已转换 {converted_count}/{len(records)} 条记录")
            
            logger.info(f"WKB 到 WGS84 GEOMETRY 转换完成: 成功 {converted_count} 条, 失败 {failed_count} 条")
            return True
            
        except Exception as e:
            logger.error(f"WKB 到 WGS84 GEOMETRY 转换失败: {e}")
            self.connection.rollback()
            return False
    
    def add_spatial_index(self, table_name: str, field_name: str = 'geom_wgs84') -> bool:
        """为 WGS84 GEOMETRY 字段添加空间索引"""
        try:
            # 先修改字段为 NOT NULL（如果所有记录都有数据）
            with self.connection.cursor() as cursor:
                cursor.execute(f"SELECT COUNT(*) FROM `{table_name}` WHERE `{field_name}` IS NULL")
                null_count = cursor.fetchone()[0]
                
                if null_count == 0:
                    # 修改字段为 NOT NULL
                    cursor.execute(f"""
                        ALTER TABLE `{table_name}` 
                        MODIFY COLUMN `{field_name}` GEOMETRY NOT NULL COMMENT 'WGS84经纬度坐标系几何数据'
                    """)
                    self.connection.commit()
                    logger.info(f"字段 {field_name} 已修改为 NOT NULL")
                    
                    # 创建空间索引
                    index_name = f"idx_{field_name}_spatial"
                    cursor.execute(f"""
                        CREATE SPATIAL INDEX `{index_name}` ON `{table_name}` (`{field_name}`)
                    """)
                    self.connection.commit()
                    logger.info(f"成功为 {field_name} 字段创建空间索引")
                    return True
                else:
                    logger.warning(f"字段 {field_name} 有 {null_count} 条空记录，无法创建空间索引")
                    return False
                    
        except Exception as e:
            if 'Duplicate key name' in str(e):
                logger.info(f"空间索引已存在")
                return True
            else:
                logger.warning(f"创建空间索引失败: {e}")
                self.connection.rollback()
                return False
    
    def verify_wgs84_geometry(self, table_name: str, field_name: str = 'geom_wgs84', limit: int = 3):
        """验证 WGS84 GEOMETRY 数据"""
        try:
            logger.info(f"=== WGS84 GEOMETRY 数据验证 (前{limit}条) ===")
            
            with self.connection.cursor(pymysql.cursors.DictCursor) as cursor:
                cursor.execute(f"""
                    SELECT id, name,
                           ST_GeometryType(`{field_name}`) as geom_type,
                           ST_SRID(`{field_name}`) as srid,
                           ST_X(ST_Centroid(`{field_name}`)) as center_lng,
                           ST_Y(ST_Centroid(`{field_name}`)) as center_lat
                    FROM `{table_name}` 
                    WHERE `{field_name}` IS NOT NULL
                    ORDER BY id
                    LIMIT {limit}
                """)
                
                records = cursor.fetchall()
                
                for i, record in enumerate(records, 1):
                    logger.info(f"--- 记录 {i} ---")
                    logger.info(f"ID: {record['id']}")
                    logger.info(f"名称: {record['name']}")
                    logger.info(f"几何类型: {record['geom_type']}")
                    logger.info(f"空间参考系: {record['srid']} (4326=WGS84)")
                    logger.info(f"中心点经度: {record['center_lng']:.8f}°")
                    logger.info(f"中心点纬度: {record['center_lat']:.8f}°")
                    logger.info("")
                
        except Exception as e:
            logger.error(f"验证 WGS84 GEOMETRY 数据失败: {e}")
    
    def get_conversion_statistics(self, table_name: str, field_name: str = 'geom_wgs84'):
        """获取转换统计信息"""
        try:
            with self.connection.cursor() as cursor:
                # 总记录数
                cursor.execute(f"SELECT COUNT(*) FROM `{table_name}`")
                total = cursor.fetchone()[0]
                
                # 有 WGS84 GEOMETRY 数据的记录
                cursor.execute(f"SELECT COUNT(*) FROM `{table_name}` WHERE `{field_name}` IS NOT NULL")
                wgs84_count = cursor.fetchone()[0]
                
                # 坐标范围
                cursor.execute(f"""
                    SELECT 
                        MIN(ST_X(ST_Centroid(`{field_name}`))) as min_lng,
                        MAX(ST_X(ST_Centroid(`{field_name}`))) as max_lng,
                        MIN(ST_Y(ST_Centroid(`{field_name}`))) as min_lat,
                        MAX(ST_Y(ST_Centroid(`{field_name}`))) as max_lat
                    FROM `{table_name}` 
                    WHERE `{field_name}` IS NOT NULL
                """)
                coord_range = cursor.fetchone()
                
                logger.info("=== 转换统计信息 ===")
                logger.info(f"总记录数: {total}")
                logger.info(f"有 WGS84 GEOMETRY 数据: {wgs84_count} ({wgs84_count/total*100:.1f}%)")
                if coord_range[0] is not None:
                    logger.info(f"经度范围: {coord_range[0]:.6f}° ~ {coord_range[1]:.6f}°")
                    logger.info(f"纬度范围: {coord_range[2]:.6f}° ~ {coord_range[3]:.6f}°")
                
                return {
                    'total': total,
                    'wgs84_geometry': wgs84_count,
                    'coord_range': coord_range
                }
                
        except Exception as e:
            logger.error(f"获取统计信息失败: {e}")
            return None
    
    def close(self):
        """关闭数据库连接"""
        if self.connection:
            self.connection.close()
            logger.info("数据库连接已关闭")

def main():
    """主函数"""
    
    # 数据库配置
    db_config = {
        'host': 'localhost',
        'port': 3306,
        'user': 'root',
        'password': '123456',
        'database': 'land'
    }
    
    # 目标表名和字段名
    table_name = 'ecology_intelligent_land_plot'
    wgs84_field_name = 'geom_wgs84'  # WGS84 经纬度坐标系的 GEOMETRY 字段
    
    try:
        print("=" * 70)
        print("将 shape 转换为 WGS84 经纬度坐标系的 GEOMETRY 字段")
        print("=" * 70)
        print(f"目标表: {table_name}")
        print(f"新字段: {wgs84_field_name} (GEOMETRY 类型, WGS84 坐标系)")
        print(f"数据库: {db_config['host']}:{db_config['port']}/{db_config['database']}")
        print("-" * 70)
        
        # 创建转换器
        converter = WGS84GeometryConverter(db_config)
        
        # 显示当前状态
        print("\n当前状态:")
        stats = converter.get_conversion_statistics(table_name, wgs84_field_name)
        
        # 确认操作
        print("\n" + "-" * 70)
        print("将执行以下操作:")
        print(f"1. 添加 {wgs84_field_name} 字段（GEOMETRY 类型）")
        print("2. 将 shape (WKB) 从 CGCS2000 投影坐标转换为 WGS84 经纬度坐标")
        print("3. 存储为 GEOMETRY 类型，SRID=4326 (WGS84)")
        print("4. 创建空间索引")
        print("5. 验证转换结果")
        
        confirm = input("\n确认执行转换? (Y/n): ").lower().strip()
        if confirm == 'n':
            print("操作已取消")
            return
        
        print("\n开始转换...")
        print("=" * 70)
        
        # 1. 添加 WGS84 GEOMETRY 字段
        print(f"1. 添加 {wgs84_field_name} 字段...")
        if converter.add_wgs84_geometry_field(table_name, wgs84_field_name):
            print(f"✓ {wgs84_field_name} 字段添加成功")
        else:
            print(f"✗ {wgs84_field_name} 字段添加失败")
            return
        
        # 2. 转换 WKB 数据为 WGS84 GEOMETRY
        print("2. 转换 WKB 数据为 WGS84 经纬度坐标...")
        if converter.convert_wkb_to_wgs84_geometry(table_name, wgs84_field_name, batch_size=50):
            print("✓ WKB 到 WGS84 GEOMETRY 转换成功")
        else:
            print("✗ WKB 到 WGS84 GEOMETRY 转换失败")
            return
        
        # 3. 创建空间索引
        print("3. 创建空间索引...")
        if converter.add_spatial_index(table_name, wgs84_field_name):
            print("✓ 空间索引创建成功")
        else:
            print("⚠ 空间索引创建失败（不影响主要功能）")
        
        # 显示最终状态
        print("\n最终状态:")
        final_stats = converter.get_conversion_statistics(table_name, wgs84_field_name)
        
        # 验证转换结果
        print("\n转换结果验证:")
        converter.verify_wgs84_geometry(table_name, wgs84_field_name, limit=3)
        
        # 关闭连接
        converter.close()
        
        print("\n" + "=" * 70)
        print("✅ WGS84 经纬度坐标系 GEOMETRY 字段转换完成!")
        print()
        print("新增字段说明:")
        print(f"- {wgs84_field_name}: GEOMETRY - WGS84 经纬度坐标系几何数据")
        print("- SRID: 4326 (WGS84 地理坐标系)")
        print("- 支持标准的经纬度空间查询")
        print()
        print("使用示例:")
        print(f"-- 查看经纬度几何数据")
        print(f"SELECT id, name, ST_AsText({wgs84_field_name}) FROM {table_name} LIMIT 1;")
        print()
        print(f"-- 经纬度范围查询")
        print(f"SELECT * FROM {table_name} WHERE ST_Contains({wgs84_field_name}, ST_GeomFromText('POINT(105.6 30.6)', 4326));")
        print()
        print(f"详细日志: wgs84_geometry_conversion.log")
        
    except Exception as e:
        print(f"✗ 转换失败: {e}")
        logger.error(f"程序执行失败: {e}")

if __name__ == '__main__':
    main()
