#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
基于 Java 算法的完整坐标转换脚本
使用精确的 CGCS2000 到 WGS84 坐标转换算法
重新转换所有 ecology_intelligent_land_plot 表的经纬度坐标
"""

import pymysql
import logging
from typing import Dict, Any, Tu<PERSON>
from pyproj import CRS, Transformer
import shapely.wkb as wkb

# 配置日志
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(levelname)s - %(message)s',
    handlers=[
        logging.FileHandler('java_algorithm_full_conversion.log', encoding='utf-8'),
        logging.StreamHandler()
    ]
)
logger = logging.getLogger(__name__)

class JavaAlgorithmCoordinateConverter:
    """基于 Java 算法的完整坐标转换器"""
    
    def __init__(self, db_config: Dict[str, Any]):
        """
        初始化转换器
        
        Args:
            db_config: 数据库配置
        """
        self.db_config = db_config
        self.connection = None
        self.transformer = None
        self._connect_db()
        self._setup_transformer()
    
    def _connect_db(self):
        """连接数据库"""
        try:
            self.connection = pymysql.connect(
                host=self.db_config['host'],
                port=self.db_config['port'],
                user=self.db_config['user'],
                password=self.db_config['password'],
                database=self.db_config['database'],
                charset='utf8mb4',
                autocommit=False
            )
            logger.info(f"成功连接到数据库: {self.db_config['host']}:{self.db_config['port']}/{self.db_config['database']}")
        except Exception as e:
            logger.error(f"数据库连接失败: {e}")
            raise
    
    def _setup_transformer(self):
        """设置坐标转换器 - 完全基于 Java 代码中的 WKT 定义"""
        try:
            # 使用与 Java 代码完全相同的 CGCS2000 3度带第35带投影坐标系定义
            cgcs2000_wkt = '''PROJCS["CGCS2000 3 Degree GK Zone 35",
    GEOGCS["China Geodetic Coordinate System 2000",
        DATUM["D China 2000",
            SPHEROID["CGCS2000",6378137.0,298.257222101],
            TOWGS84[0,0,0,0,0,0,0]],
        PRIMEM["Greenwich",0.0],
        UNIT["Degree",0.0174532925199433]],
    PROJECTION["Gauss_Kruger"],
    PARAMETER["latitude_of_origin",0.0],
    PARAMETER["central_meridian",105.0],
    PARAMETER["scale_factor",1.0],
    PARAMETER["false_easting",35500000.0],
    PARAMETER["false_northing",0.0],
    UNIT["Meter",1.0]]'''
            
            # 创建坐标系
            source_crs = CRS.from_wkt(cgcs2000_wkt)
            target_crs = CRS.from_epsg(4326)  # WGS84
            
            # 创建转换器
            self.transformer = Transformer.from_crs(source_crs, target_crs, always_xy=True)
            logger.info("基于 Java 算法的坐标转换器初始化成功")
            
        except Exception as e:
            logger.error(f"坐标转换器初始化失败: {e}")
            raise
    
    def convert_wkb_to_bbox(self, wkb_data: bytes) -> str:
        """
        将 WKB 数据转换为边界框 - 完全模拟 Java 算法
        
        Args:
            wkb_data: WKB 二进制数据
            
        Returns:
            str: 边界框字符串，格式：minLng,minLat,maxLng,maxLat
        """
        try:
            logger.debug("开始转换WKB字符串到边界框")
            
            # 解析 WKB 数据为几何对象
            geometry = wkb.loads(wkb_data)
            
            # 获取几何对象的边界框
            bounds = geometry.bounds  # (minx, miny, maxx, maxy)
            min_x, min_y, max_x, max_y = bounds
            
            logger.debug(f"原始坐标范围 - minX: {min_x}, maxX: {max_x}, minY: {min_y}, maxY: {max_y}")
            
            # 转换坐标系 (从 CGCS2000 到 WGS84) - 模拟 Java 的 convertCgcs2000ToWgs84Bbox 方法
            return self._convert_cgcs2000_to_wgs84_bbox(min_x, min_y, max_x, max_y)
            
        except Exception as e:
            logger.error(f"WKB字符串转换失败: {e}")
            raise RuntimeError(f"WKB字符串转换失败: {e}")
    
    def _convert_cgcs2000_to_wgs84_bbox(self, min_x: float, min_y: float, max_x: float, max_y: float) -> str:
        """
        使用精确参数将 CGCS2000 坐标转换为 WGS84 边界框 - 完全模拟 Java 算法
        
        Args:
            min_x: 最小X坐标
            min_y: 最小Y坐标
            max_x: 最大X坐标
            max_y: 最大Y坐标
            
        Returns:
            str: WGS84边界框字符串
        """
        try:
            logger.debug("使用精确参数转换坐标系")
            
            # 转换四个角点 - 模拟 Java 代码的逻辑
            bottom_left = (min_x, min_y)    # 左下角
            top_right = (max_x, max_y)      # 右上角
            bottom_right = (max_x, min_y)   # 右下角
            top_left = (min_x, max_y)       # 左上角
            
            # 转换所有角点
            bl_wgs84 = self.transformer.transform(bottom_left[0], bottom_left[1])
            tr_wgs84 = self.transformer.transform(top_right[0], top_right[1])
            br_wgs84 = self.transformer.transform(bottom_right[0], bottom_right[1])
            tl_wgs84 = self.transformer.transform(top_left[0], top_left[1])
            
            logger.debug(f"转换后的经纬度:")
            logger.debug(f"左下角: 经度={bl_wgs84[0]}, 纬度={bl_wgs84[1]}")
            logger.debug(f"右上角: 经度={tr_wgs84[0]}, 纬度={tr_wgs84[1]}")
            logger.debug(f"右下角: 经度={br_wgs84[0]}, 纬度={br_wgs84[1]}")
            logger.debug(f"左上角: 经度={tl_wgs84[0]}, 纬度={tl_wgs84[1]}")
            
            # 计算边界框 - 模拟 Java 代码的 Math.min/Math.max 逻辑
            all_lons = [bl_wgs84[0], tr_wgs84[0], br_wgs84[0], tl_wgs84[0]]
            all_lats = [bl_wgs84[1], tr_wgs84[1], br_wgs84[1], tl_wgs84[1]]
            
            bbox_min_lon = min(all_lons)
            bbox_max_lon = max(all_lons)
            bbox_min_lat = min(all_lats)
            bbox_max_lat = max(all_lats)
            
            # WMS BBOX 格式 - 模拟 Java 代码的 StringUtil.format
            bbox = f"{bbox_min_lon},{bbox_min_lat},{bbox_max_lon},{bbox_max_lat}"
            logger.debug(f"WMS BBOX格式: {bbox}")
            
            return bbox
            
        except Exception as e:
            logger.error(f"精确转换失败: {e}")
            raise
    
    def get_geometry_center_coordinates(self, wkb_data: bytes) -> Tuple[float, float]:
        """
        获取几何对象的中心点坐标
        
        Args:
            wkb_data: WKB 二进制数据
            
        Returns:
            Tuple[float, float]: (lng, lat)
        """
        try:
            # 解析 WKB 数据
            geometry = wkb.loads(wkb_data)
            
            # 获取中心点
            centroid = geometry.centroid
            x, y = centroid.x, centroid.y
            
            # 转换坐标
            lng, lat = self.transformer.transform(x, y)
            return lng, lat
            
        except Exception as e:
            logger.error(f"获取中心点失败: {e}")
            raise
    
    def convert_all_coordinates(self, table_name: str, batch_size: int = 100) -> bool:
        """
        使用 Java 算法转换所有记录的坐标
        
        Args:
            table_name: 表名
            batch_size: 批处理大小
            
        Returns:
            bool: 转换是否成功
        """
        try:
            logger.info("开始使用 Java 算法转换所有坐标...")
            
            # 获取所有有 WKB 数据的记录
            with self.connection.cursor() as cursor:
                cursor.execute(f"""
                    SELECT id, shape 
                    FROM `{table_name}` 
                    WHERE shape IS NOT NULL
                    ORDER BY id
                """)
                records = cursor.fetchall()
            
            if not records:
                logger.warning("没有找到 WKB 数据")
                return False
            
            logger.info(f"找到 {len(records)} 条需要转换的记录")
            
            converted_count = 0
            failed_count = 0
            
            # 批量处理
            for i in range(0, len(records), batch_size):
                batch = records[i:i + batch_size]
                batch_updates = []
                
                for record_id, wkb_data in batch:
                    try:
                        # 使用 Java 算法获取中心点坐标
                        lng, lat = self.get_geometry_center_coordinates(wkb_data)
                        
                        # 使用 Java 算法获取边界框
                        bbox_str = self.convert_wkb_to_bbox(wkb_data)
                        
                        batch_updates.append((lng, lat, bbox_str, record_id))
                        
                    except Exception as e:
                        failed_count += 1
                        logger.error(f"处理记录 {record_id} 失败: {e}")
                
                # 批量更新数据库
                if batch_updates:
                    with self.connection.cursor() as cursor:
                        cursor.executemany(f"""
                            UPDATE `{table_name}` 
                            SET lng = %s, lat = %s, bbox = %s 
                            WHERE id = %s
                        """, batch_updates)
                        self.connection.commit()
                        converted_count += len(batch_updates)
                        logger.info(f"已转换 {converted_count}/{len(records)} 条记录")
            
            logger.info(f"坐标转换完成: 成功 {converted_count} 条, 失败 {failed_count} 条")
            return True
            
        except Exception as e:
            logger.error(f"转换坐标失败: {e}")
            self.connection.rollback()
            return False
    
    def verify_conversion_results(self, table_name: str, limit: int = 5):
        """验证转换结果"""
        try:
            logger.info("=== Java 算法转换结果验证 ===")
            
            with self.connection.cursor(pymysql.cursors.DictCursor) as cursor:
                cursor.execute(f"""
                    SELECT id, name, lng, lat, bbox
                    FROM `{table_name}` 
                    WHERE lng IS NOT NULL AND lat IS NOT NULL
                    ORDER BY id
                    LIMIT {limit}
                """)
                
                records = cursor.fetchall()
                
                for i, record in enumerate(records, 1):
                    logger.info(f"--- 记录 {i} ---")
                    logger.info(f"ID: {record['id']}")
                    logger.info(f"名称: {record['name']}")
                    logger.info(f"经度: {record['lng']:.8f}")
                    logger.info(f"纬度: {record['lat']:.8f}")
                    logger.info(f"边界框: {record['bbox']}")
                    logger.info("")
                
        except Exception as e:
            logger.error(f"验证转换结果失败: {e}")
    
    def get_conversion_statistics(self, table_name: str):
        """获取转换统计信息"""
        try:
            with self.connection.cursor() as cursor:
                # 总记录数
                cursor.execute(f"SELECT COUNT(*) FROM `{table_name}`")
                total = cursor.fetchone()[0]
                
                # 有 WKB 数据的记录
                cursor.execute(f"SELECT COUNT(*) FROM `{table_name}` WHERE shape IS NOT NULL")
                wkb_count = cursor.fetchone()[0]
                
                # 有坐标数据的记录
                cursor.execute(f"SELECT COUNT(*) FROM `{table_name}` WHERE lng IS NOT NULL AND lat IS NOT NULL")
                coord_count = cursor.fetchone()[0]
                
                # 有边界框数据的记录
                cursor.execute(f"SELECT COUNT(*) FROM `{table_name}` WHERE bbox IS NOT NULL")
                bbox_count = cursor.fetchone()[0]
                
                # 坐标范围
                cursor.execute(f"SELECT MIN(lng), MAX(lng), MIN(lat), MAX(lat) FROM `{table_name}` WHERE lng IS NOT NULL")
                coord_range = cursor.fetchone()
                
                logger.info("=== 转换统计信息 ===")
                logger.info(f"总记录数: {total}")
                logger.info(f"有 WKB 数据: {wkb_count}")
                logger.info(f"有坐标数据: {coord_count}")
                logger.info(f"有边界框数据: {bbox_count}")
                if coord_range[0] is not None:
                    logger.info(f"经度范围: {coord_range[0]:.6f} ~ {coord_range[1]:.6f}")
                    logger.info(f"纬度范围: {coord_range[2]:.6f} ~ {coord_range[3]:.6f}")
                
                return {
                    'total': total,
                    'wkb': wkb_count,
                    'coord': coord_count,
                    'bbox': bbox_count,
                    'coord_range': coord_range
                }
                
        except Exception as e:
            logger.error(f"获取统计信息失败: {e}")
            return None
    
    def close(self):
        """关闭数据库连接"""
        if self.connection:
            self.connection.close()
            logger.info("数据库连接已关闭")

def main():
    """主函数"""
    
    # 数据库配置
    db_config = {
        'host': 'localhost',
        'port': 3306,
        'user': 'root',
        'password': '123456',
        'database': 'land'
    }
    
    # 目标表名
    table_name = 'ecology_intelligent_land_plot'
    
    try:
        print("=" * 70)
        print("基于 Java 算法的完整坐标转换")
        print("使用精确的 CGCS2000 到 WGS84 坐标转换")
        print("=" * 70)
        print(f"目标表: {table_name}")
        print(f"数据库: {db_config['host']}:{db_config['port']}/{db_config['database']}")
        print("-" * 70)
        
        # 创建转换器
        converter = JavaAlgorithmCoordinateConverter(db_config)
        
        # 显示当前状态
        print("\n转换前状态:")
        stats = converter.get_conversion_statistics(table_name)
        
        # 确认操作
        print("\n" + "-" * 70)
        print("⚠️  重要提示：此操作将重新转换所有记录的坐标！")
        print("将执行以下操作:")
        print("1. 使用与 Java 代码完全相同的 CGCS2000 坐标系定义")
        print("2. 重新转换所有 WKB 数据为精确的经纬度坐标")
        print("3. 重新计算所有边界框")
        print("4. 更新所有记录的 lng, lat, bbox 字段")
        print("5. 覆盖现有的坐标数据")
        
        confirm = input("\n确认执行完整的坐标转换? (输入 'YES' 确认): ").strip()
        if confirm != 'YES':
            print("操作已取消")
            return
        
        print("\n开始转换...")
        print("=" * 70)
        
        # 执行完整的坐标转换
        if converter.convert_all_coordinates(table_name, batch_size=50):
            print("✓ 坐标转换成功完成")
        else:
            print("✗ 坐标转换失败")
            return
        
        # 显示最终状态
        print("\n转换后状态:")
        final_stats = converter.get_conversion_statistics(table_name)
        
        # 验证转换结果
        print("\n转换结果验证:")
        converter.verify_conversion_results(table_name, limit=3)
        
        # 关闭连接
        converter.close()
        
        print("\n" + "=" * 70)
        print("✅ Java 算法坐标转换完成!")
        print()
        print("转换说明:")
        print("- 使用与您的 Java 代码完全相同的坐标系定义")
        print("- 精确的 CGCS2000 3度带第35带 → WGS84 转换")
        print("- 所有坐标都基于 WKB 几何数据重新计算")
        print("- 边界框使用四个角点转换后的最值计算")
        print()
        print("验证查询:")
        print(f"SELECT id, name, lng, lat, bbox FROM {table_name} ORDER BY id LIMIT 5;")
        print()
        print(f"详细日志: java_algorithm_full_conversion.log")
        
    except Exception as e:
        print(f"✗ 转换失败: {e}")
        logger.error(f"程序执行失败: {e}")

if __name__ == '__main__':
    main()
