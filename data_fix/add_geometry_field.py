#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
为 ecology_intelligent_land_plot 表添加 GEOMETRY 类型字段
将 shape (WKB) 或 shape_wkt (WKT) 转换为 MySQL GEOMETRY 类型
"""

import pymysql
import logging
from typing import Dict, Any

# 配置日志
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(levelname)s - %(message)s',
    handlers=[
        logging.FileHandler('geometry_field_conversion.log', encoding='utf-8'),
        logging.StreamHandler()
    ]
)
logger = logging.getLogger(__name__)

class GeometryFieldProcessor:
    """GEOMETRY 字段处理器"""
    
    def __init__(self, db_config: Dict[str, Any]):
        """
        初始化处理器
        
        Args:
            db_config: 数据库配置
        """
        self.db_config = db_config
        self.connection = None
        self._connect_db()
    
    def _connect_db(self):
        """连接数据库"""
        try:
            self.connection = pymysql.connect(
                host=self.db_config['host'],
                port=self.db_config['port'],
                user=self.db_config['user'],
                password=self.db_config['password'],
                database=self.db_config['database'],
                charset='utf8mb4',
                autocommit=False
            )
            logger.info(f"成功连接到数据库: {self.db_config['host']}:{self.db_config['port']}/{self.db_config['database']}")
        except Exception as e:
            logger.error(f"数据库连接失败: {e}")
            raise
    
    def check_field_exists(self, table_name: str, field_name: str) -> bool:
        """检查字段是否存在"""
        try:
            with self.connection.cursor() as cursor:
                cursor.execute("""
                    SELECT COUNT(*) 
                    FROM information_schema.columns 
                    WHERE table_schema = %s AND table_name = %s AND column_name = %s
                """, (self.db_config['database'], table_name, field_name))
                result = cursor.fetchone()
                exists = result[0] > 0
                logger.info(f"表 {table_name} 中字段 {field_name} {'存在' if exists else '不存在'}")
                return exists
        except Exception as e:
            logger.error(f"检查字段是否存在失败: {e}")
            return False
    
    def add_geometry_field(self, table_name: str, field_name: str = 'geom') -> bool:
        """添加 GEOMETRY 类型字段"""
        try:
            # 检查字段是否已存在
            if self.check_field_exists(table_name, field_name):
                logger.warning(f"字段 '{field_name}' 已存在于表 {table_name} 中")
                return True
            
            # 添加 GEOMETRY 字段
            alter_sql = f"""
            ALTER TABLE `{table_name}` 
            ADD COLUMN `{field_name}` GEOMETRY DEFAULT NULL COMMENT 'MySQL GEOMETRY 类型几何数据'
            """
            
            with self.connection.cursor() as cursor:
                cursor.execute(alter_sql)
                self.connection.commit()
                logger.info(f"成功为表 {table_name} 添加 {field_name} 字段")
                return True
                
        except Exception as e:
            logger.error(f"添加 {field_name} 字段失败: {e}")
            self.connection.rollback()
            return False
    
    def add_geometry_index(self, table_name: str, field_name: str = 'geom') -> bool:
        """为 GEOMETRY 字段添加空间索引"""
        try:
            # 检查索引是否已存在
            index_name = f"idx_{field_name}_spatial"
            with self.connection.cursor() as cursor:
                cursor.execute("""
                    SELECT COUNT(*) 
                    FROM information_schema.statistics 
                    WHERE table_schema = %s AND table_name = %s AND index_name = %s
                """, (self.db_config['database'], table_name, index_name))
                result = cursor.fetchone()
                
                if result[0] > 0:
                    logger.info(f"空间索引 {index_name} 已存在于表 {table_name}")
                    return True
                
                # 创建空间索引
                index_sql = f"""
                CREATE SPATIAL INDEX `{index_name}` ON `{table_name}` (`{field_name}`)
                """
                
                cursor.execute(index_sql)
                self.connection.commit()
                logger.info(f"成功为表 {table_name} 的 {field_name} 字段创建空间索引")
                return True
                
        except Exception as e:
            logger.warning(f"创建空间索引失败（这是可选操作）: {e}")
            self.connection.rollback()
            return False
    
    def convert_wkb_to_geometry(self, table_name: str, field_name: str = 'geom', batch_size: int = 100) -> bool:
        """将 WKB 数据转换为 GEOMETRY 类型"""
        try:
            logger.info("开始将 WKB 数据转换为 GEOMETRY 类型...")
            
            # 获取有 WKB 数据但没有 GEOMETRY 数据的记录
            with self.connection.cursor() as cursor:
                cursor.execute(f"""
                    SELECT COUNT(*) 
                    FROM `{table_name}` 
                    WHERE shape IS NOT NULL AND `{field_name}` IS NULL
                """)
                total_count = cursor.fetchone()[0]
            
            if total_count == 0:
                logger.info("没有需要从 WKB 转换的记录")
                return True
            
            logger.info(f"找到 {total_count} 条需要从 WKB 转换的记录")
            
            # 批量转换
            converted_count = 0
            offset = 0
            
            while offset < total_count:
                with self.connection.cursor() as cursor:
                    # 使用 MySQL 的 ST_GeomFromWKB 函数转换
                    update_sql = f"""
                    UPDATE `{table_name}` 
                    SET `{field_name}` = ST_GeomFromWKB(shape)
                    WHERE shape IS NOT NULL AND `{field_name}` IS NULL
                    LIMIT {batch_size}
                    """
                    
                    cursor.execute(update_sql)
                    affected_rows = cursor.rowcount
                    self.connection.commit()
                    
                    converted_count += affected_rows
                    offset += batch_size
                    
                    logger.info(f"已转换 {converted_count}/{total_count} 条记录 (WKB -> GEOMETRY)")
                    
                    if affected_rows == 0:
                        break
            
            logger.info(f"WKB 到 GEOMETRY 转换完成: {converted_count} 条记录")
            return True
            
        except Exception as e:
            logger.error(f"WKB 到 GEOMETRY 转换失败: {e}")
            self.connection.rollback()
            return False
    
    def convert_wkt_to_geometry(self, table_name: str, field_name: str = 'geom', batch_size: int = 100) -> bool:
        """将 WKT 数据转换为 GEOMETRY 类型"""
        try:
            logger.info("开始将 WKT 数据转换为 GEOMETRY 类型...")
            
            # 获取有 WKT 数据但没有 GEOMETRY 数据的记录
            with self.connection.cursor() as cursor:
                cursor.execute(f"""
                    SELECT COUNT(*) 
                    FROM `{table_name}` 
                    WHERE shape_wkt IS NOT NULL AND `{field_name}` IS NULL
                """)
                total_count = cursor.fetchone()[0]
            
            if total_count == 0:
                logger.info("没有需要从 WKT 转换的记录")
                return True
            
            logger.info(f"找到 {total_count} 条需要从 WKT 转换的记录")
            
            # 批量转换
            converted_count = 0
            offset = 0
            
            while offset < total_count:
                with self.connection.cursor() as cursor:
                    # 使用 MySQL 的 ST_GeomFromText 函数转换
                    update_sql = f"""
                    UPDATE `{table_name}` 
                    SET `{field_name}` = ST_GeomFromText(shape_wkt)
                    WHERE shape_wkt IS NOT NULL AND `{field_name}` IS NULL
                    LIMIT {batch_size}
                    """
                    
                    cursor.execute(update_sql)
                    affected_rows = cursor.rowcount
                    self.connection.commit()
                    
                    converted_count += affected_rows
                    offset += batch_size
                    
                    logger.info(f"已转换 {converted_count}/{total_count} 条记录 (WKT -> GEOMETRY)")
                    
                    if affected_rows == 0:
                        break
            
            logger.info(f"WKT 到 GEOMETRY 转换完成: {converted_count} 条记录")
            return True
            
        except Exception as e:
            logger.error(f"WKT 到 GEOMETRY 转换失败: {e}")
            self.connection.rollback()
            return False
    
    def verify_geometry_data(self, table_name: str, field_name: str = 'geom', limit: int = 5):
        """验证 GEOMETRY 数据"""
        try:
            logger.info(f"=== GEOMETRY 数据验证 (前{limit}条) ===")
            
            with self.connection.cursor(pymysql.cursors.DictCursor) as cursor:
                cursor.execute(f"""
                    SELECT id, name,
                           ST_GeometryType(`{field_name}`) as geom_type,
                           ST_SRID(`{field_name}`) as srid,
                           ST_AsText(ST_Centroid(`{field_name}`)) as centroid_wkt
                    FROM `{table_name}` 
                    WHERE `{field_name}` IS NOT NULL
                    ORDER BY id
                    LIMIT {limit}
                """)
                
                records = cursor.fetchall()
                
                for i, record in enumerate(records, 1):
                    logger.info(f"--- 记录 {i} ---")
                    logger.info(f"ID: {record['id']}")
                    logger.info(f"名称: {record['name']}")
                    logger.info(f"几何类型: {record['geom_type']}")
                    logger.info(f"空间参考系: {record['srid']}")
                    logger.info(f"中心点: {record['centroid_wkt']}")
                    logger.info("")
                
        except Exception as e:
            logger.error(f"验证 GEOMETRY 数据失败: {e}")
    
    def get_conversion_statistics(self, table_name: str, field_name: str = 'geom'):
        """获取转换统计信息"""
        try:
            with self.connection.cursor() as cursor:
                # 总记录数
                cursor.execute(f"SELECT COUNT(*) FROM `{table_name}`")
                total = cursor.fetchone()[0]
                
                # 有 WKB 数据的记录
                cursor.execute(f"SELECT COUNT(*) FROM `{table_name}` WHERE shape IS NOT NULL")
                wkb_count = cursor.fetchone()[0]
                
                # 有 WKT 数据的记录
                cursor.execute(f"SELECT COUNT(*) FROM `{table_name}` WHERE shape_wkt IS NOT NULL")
                wkt_count = cursor.fetchone()[0]
                
                # 有 GEOMETRY 数据的记录
                cursor.execute(f"SELECT COUNT(*) FROM `{table_name}` WHERE `{field_name}` IS NOT NULL")
                geom_count = cursor.fetchone()[0]
                
                logger.info("=== 转换统计信息 ===")
                logger.info(f"总记录数: {total}")
                logger.info(f"有 WKB 数据: {wkb_count}")
                logger.info(f"有 WKT 数据: {wkt_count}")
                logger.info(f"有 GEOMETRY 数据: {geom_count}")
                
                return {
                    'total': total,
                    'wkb': wkb_count,
                    'wkt': wkt_count,
                    'geometry': geom_count
                }
                
        except Exception as e:
            logger.error(f"获取统计信息失败: {e}")
            return None
    
    def close(self):
        """关闭数据库连接"""
        if self.connection:
            self.connection.close()
            logger.info("数据库连接已关闭")

def main():
    """主函数"""
    
    # 数据库配置
    db_config = {
        'host': 'localhost',
        'port': 3306,
        'user': 'root',
        'password': '123456',
        'database': 'land'
    }
    
    # 目标表名和字段名
    table_name = 'ecology_intelligent_land_plot'
    geometry_field_name = 'geom'  # 新的 GEOMETRY 字段名
    
    try:
        print("=" * 60)
        print("为 ecology_intelligent_land_plot 表添加 GEOMETRY 字段")
        print("=" * 60)
        print(f"目标表: {table_name}")
        print(f"新字段: {geometry_field_name} (GEOMETRY 类型)")
        print(f"数据库: {db_config['host']}:{db_config['port']}/{db_config['database']}")
        print("-" * 60)
        
        # 创建处理器
        processor = GeometryFieldProcessor(db_config)
        
        # 显示当前状态
        print("\n当前状态:")
        stats = processor.get_conversion_statistics(table_name, geometry_field_name)
        
        # 确认操作
        print("\n" + "-" * 60)
        print("将执行以下操作:")
        print(f"1. 添加 {geometry_field_name} 字段（GEOMETRY 类型）")
        print("2. 将 shape (WKB) 数据转换为 GEOMETRY")
        print("3. 将 shape_wkt (WKT) 数据转换为 GEOMETRY")
        print("4. 为 GEOMETRY 字段创建空间索引")
        print("5. 验证转换结果")
        
        confirm = input("\n确认执行操作? (Y/n): ").lower().strip()
        if confirm == 'n':
            print("操作已取消")
            return
        
        print("\n开始处理...")
        
        # 1. 添加 GEOMETRY 字段
        print(f"1. 添加 {geometry_field_name} 字段...")
        if processor.add_geometry_field(table_name, geometry_field_name):
            print(f"✓ {geometry_field_name} 字段添加成功")
        else:
            print(f"✗ {geometry_field_name} 字段添加失败")
            return
        
        # 2. 转换 WKB 数据
        print("2. 转换 WKB 数据...")
        if processor.convert_wkb_to_geometry(table_name, geometry_field_name, batch_size=100):
            print("✓ WKB 数据转换成功")
        else:
            print("✗ WKB 数据转换失败")
        
        # 3. 转换 WKT 数据
        print("3. 转换 WKT 数据...")
        if processor.convert_wkt_to_geometry(table_name, geometry_field_name, batch_size=100):
            print("✓ WKT 数据转换成功")
        else:
            print("✗ WKT 数据转换失败")
        
        # 4. 创建空间索引
        print("4. 创建空间索引...")
        if processor.add_geometry_index(table_name, geometry_field_name):
            print("✓ 空间索引创建成功")
        else:
            print("⚠ 空间索引创建失败（不影响主要功能）")
        
        # 显示最终状态
        print("\n最终状态:")
        final_stats = processor.get_conversion_statistics(table_name, geometry_field_name)
        
        # 验证转换结果
        print("\n转换结果验证:")
        processor.verify_geometry_data(table_name, geometry_field_name, limit=3)
        
        # 关闭连接
        processor.close()
        
        print("\n" + "=" * 60)
        print("✓ GEOMETRY 字段处理完成!")
        print()
        print("新增字段说明:")
        print(f"- {geometry_field_name}: GEOMETRY - MySQL 原生几何数据类型")
        print("- 支持空间查询和空间索引")
        print("- 兼容 MySQL 空间函数")
        print()
        print("使用示例:")
        print(f"-- 查看几何类型")
        print(f"SELECT id, name, ST_GeometryType({geometry_field_name}) FROM {table_name} LIMIT 5;")
        print()
        print(f"-- 空间查询示例")
        print(f"SELECT * FROM {table_name} WHERE ST_Contains({geometry_field_name}, ST_GeomFromText('POINT(105.6 30.6)'));")
        print()
        print(f"详细日志请查看: geometry_field_conversion.log")
        
    except Exception as e:
        print(f"✗ 操作失败: {e}")
        logger.error(f"程序执行失败: {e}")

if __name__ == '__main__':
    main()
