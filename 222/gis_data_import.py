#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
GIS数据批量导入MySQL脚本
支持从GDB目录导入空间数据到MySQL数据库
"""

import os
import sys
import uuid
import json
import logging
from datetime import datetime
from pathlib import Path
from typing import List, Dict, Any, Optional

import pymysql
import geopandas as gpd
from shapely.geometry import Point, Polygon, LineString
from shapely import wkb, wkt
import pandas as pd
from sqlalchemy import create_engine, text
import fiona

# 配置日志
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(levelname)s - %(message)s',
    handlers=[
        logging.FileHandler('gis_import.log', encoding='utf-8'),
        logging.StreamHandler()
    ]
)
logger = logging.getLogger(__name__)

class GISDataImporter:
    """GIS数据导入器"""
    
    def __init__(self, db_config: Dict[str, Any]):
        """
        初始化导入器
        
        Args:
            db_config: 数据库配置
        """
        self.db_config = db_config
        self.engine = None
        self.connection = None
        self._connect_db()
    
    def _connect_db(self):
        """连接数据库"""
        try:
            # 创建SQLAlchemy引擎
            db_url = f"mysql+pymysql://{self.db_config['user']}:{self.db_config['password']}@{self.db_config['host']}:{self.db_config['port']}/{self.db_config['database']}?charset=utf8mb4"
            self.engine = create_engine(db_url, echo=False)
            
            # 创建PyMySQL连接用于执行原生SQL
            self.connection = pymysql.connect(
                host=self.db_config['host'],
                port=self.db_config['port'],
                user=self.db_config['user'],
                password=self.db_config['password'],
                database=self.db_config['database'],
                charset='utf8mb4'
            )
            logger.info("数据库连接成功")
        except Exception as e:
            logger.error(f"数据库连接失败: {e}")
            raise
    
    def _generate_uuid(self) -> str:
        """生成UUID（不带分隔符）"""
        return str(uuid.uuid4()).replace('-', '')
    
    def _get_current_time(self) -> str:
        """获取当前时间字符串"""
        return datetime.now().strftime('%Y-%m-%d %H:%M:%S')

    def _validate_coordinates(self, lng: float, lat: float) -> tuple:
        """
        验证并修正坐标值

        Args:
            lng: 经度
            lat: 纬度

        Returns:
            tuple: (修正后的经度, 修正后的纬度)
        """
        # 检查经度范围 (-180 到 180)
        if lng is not None:
            if lng < -180 or lng > 180:
                logger.warning(f"经度值超出范围: {lng}, 设置为None")
                lng = None
            elif abs(lng) > 1000:  # 可能是投影坐标系，需要转换
                logger.warning(f"经度值疑似投影坐标: {lng}, 设置为None")
                lng = None

        # 检查纬度范围 (-90 到 90)
        if lat is not None:
            if lat < -90 or lat > 90:
                logger.warning(f"纬度值超出范围: {lat}, 设置为None")
                lat = None
            elif abs(lat) > 1000:  # 可能是投影坐标系，需要转换
                logger.warning(f"纬度值疑似投影坐标: {lat}, 设置为None")
                lat = None

        return lng, lat

    def _process_field_value(self, value: Any, dtype: Any) -> Any:
        """
        处理字段值，确保类型一致性

        Args:
            value: 原始值
            dtype: pandas数据类型

        Returns:
            Any: 处理后的值
        """
        if pd.isna(value) or value is None:
            return None

        try:
            # 字符串类型
            if dtype == 'object':
                return str(value) if value is not None else None

            # 整数类型
            elif dtype in ['int64', 'int32', 'int16', 'int8']:
                if isinstance(value, (int, float)):
                    return int(value) if not pd.isna(value) else None
                else:
                    return int(float(str(value))) if str(value).replace('.', '').replace('-', '').isdigit() else None

            # 浮点数类型
            elif dtype in ['float64', 'float32']:
                if isinstance(value, (int, float)):
                    return float(value) if not pd.isna(value) else None
                else:
                    try:
                        return float(str(value))
                    except (ValueError, TypeError):
                        return None

            # 布尔类型
            elif dtype == 'bool':
                if isinstance(value, bool):
                    return 1 if value else 0
                elif isinstance(value, (int, float)):
                    return 1 if value != 0 else 0
                else:
                    str_val = str(value).lower()
                    return 1 if str_val in ['true', '1', 'yes', 'y'] else 0

            # 日期时间类型
            elif 'datetime' in str(dtype).lower():
                if pd.isna(value):
                    return None
                return pd.to_datetime(value).strftime('%Y-%m-%d %H:%M:%S')

            # 日期类型
            elif 'date' in str(dtype).lower():
                if pd.isna(value):
                    return None
                return pd.to_datetime(value).strftime('%Y-%m-%d')

            # 其他类型，转为字符串
            else:
                return str(value) if value is not None else None

        except Exception as e:
            logger.warning(f"字段值处理失败: {value} (type: {dtype}), error: {e}")
            return str(value) if value is not None else None

    def _sanitize_table_name(self, name: str) -> str:
        """
        清理表名，确保符合MySQL命名规范
        
        Args:
            name: 原始名称
            
        Returns:
            str: 清理后的表名
        """
        # 转换为小写
        clean_name = name.lower()
        # 替换特殊字符为下划线
        import re
        clean_name = re.sub(r'[^a-z0-9_]', '_', clean_name)
        # 移除连续的下划线
        clean_name = re.sub(r'_+', '_', clean_name)
        # 移除开头和结尾的下划线
        clean_name = clean_name.strip('_')
        # 确保不以数字开头
        if clean_name and clean_name[0].isdigit():
            clean_name = 'layer_' + clean_name
        return clean_name
    
    def analyze_gdb_schema(self, gdb_path: str, layer_name: str) -> Dict[str, str]:
        """
        分析GDB图层的字段结构，使用实际数据进行精确类型映射

        Args:
            gdb_path: GDB目录路径
            layer_name: 图层名称

        Returns:
            Dict[str, str]: 字段名到MySQL类型的映射
        """
        try:
            # 读取更多数据来分析字段类型，确保类型准确性
            gdf = gpd.read_file(gdb_path, layer=layer_name, rows=100)

            field_types = {}
            for column in gdf.columns:
                if column == 'geometry':
                    continue

                dtype = gdf[column].dtype
                sample_values = gdf[column].dropna()

                logger.debug(f"字段 {column}: dtype={dtype}, 样本数量={len(sample_values)}")

                # 根据pandas数据类型和实际数据映射到MySQL类型
                if dtype == 'object':
                    # 检查是否为字符串类型
                    if sample_values.empty:
                        field_types[column] = 'VARCHAR(255)'
                    else:
                        # 检查实际字符串长度
                        max_length = 0
                        for val in sample_values:
                            if val is not None:
                                str_len = len(str(val))
                                max_length = max(max_length, str_len)

                        # 根据最大长度选择合适的类型
                        if max_length == 0:
                            field_types[column] = 'VARCHAR(255)'
                        elif max_length > 2000:
                            field_types[column] = 'TEXT'
                        elif max_length > 1000:
                            field_types[column] = 'VARCHAR(2000)'
                        elif max_length > 500:
                            field_types[column] = 'VARCHAR(1000)'
                        elif max_length > 255:
                            field_types[column] = 'VARCHAR(500)'
                        else:
                            # 为字符串预留一些空间
                            safe_length = max(max_length * 2, 255)
                            field_types[column] = f'VARCHAR({min(safe_length, 500)})'

                elif dtype in ['int64', 'int32', 'int16', 'int8']:
                    # 检查整数范围
                    if not sample_values.empty:
                        min_val = sample_values.min()
                        max_val = sample_values.max()

                        if min_val >= -128 and max_val <= 127:
                            field_types[column] = 'TINYINT'
                        elif min_val >= -32768 and max_val <= 32767:
                            field_types[column] = 'SMALLINT'
                        elif min_val >= -2147483648 and max_val <= 2147483647:
                            field_types[column] = 'INT'
                        else:
                            field_types[column] = 'BIGINT'
                    else:
                        field_types[column] = 'BIGINT'

                elif dtype in ['float64', 'float32']:
                    # 检查浮点数精度
                    if not sample_values.empty:
                        # 分析小数位数
                        decimal_places = 0
                        max_val = 0
                        for val in sample_values:
                            if val is not None and not pd.isna(val):
                                max_val = max(max_val, abs(float(val)))
                                str_val = str(float(val))
                                if '.' in str_val and 'e' not in str_val.lower():
                                    decimal_places = max(decimal_places, len(str_val.split('.')[1]))

                        # 特殊处理面积和周长字段
                        column_lower = column.lower()
                        if 'area' in column_lower or 'shape_area' in column_lower:
                            # 面积字段可能很大，使用更大的精度
                            field_types[column] = f'DECIMAL(20,{min(decimal_places, 6)})'
                        elif 'length' in column_lower or 'perimeter' in column_lower or 'shape_length' in column_lower:
                            # 周长字段
                            field_types[column] = f'DECIMAL(18,{min(decimal_places, 6)})'
                        else:
                            # 根据数值范围和精度选择类型
                            if max_val > 999999999:
                                field_types[column] = f'DECIMAL(20,{min(decimal_places, 8)})'
                            elif max_val > 999999:
                                field_types[column] = f'DECIMAL(15,{min(decimal_places, 6)})'
                            else:
                                field_types[column] = f'DECIMAL(10,{min(decimal_places, 4)})'
                    else:
                        field_types[column] = 'DECIMAL(15,6)'

                elif dtype == 'bool':
                    field_types[column] = 'TINYINT(1)'
                elif 'datetime' in str(dtype).lower():
                    field_types[column] = 'DATETIME'
                elif 'date' in str(dtype).lower():
                    field_types[column] = 'DATE'
                else:
                    # 未知类型，默认为VARCHAR
                    logger.warning(f"未知数据类型 {dtype} for column {column}, 使用VARCHAR(255)")
                    field_types[column] = 'VARCHAR(255)'

            logger.info(f"图层 {layer_name} 字段分析完成: {field_types}")
            return field_types

        except Exception as e:
            logger.error(f"分析图层字段失败: {e}")
            return {}
    
    def create_dynamic_table(self, table_name: str, gdb_path: str, layer_name: str) -> bool:
        """
        根据GDB图层字段动态创建表
        
        Args:
            table_name: 表名
            gdb_path: GDB目录路径
            layer_name: 图层名称
            
        Returns:
            bool: 创建是否成功
        """
        try:
            # 分析字段结构
            field_types = self.analyze_gdb_schema(gdb_path, layer_name)
            
            # 构建字段定义
            field_definitions = []
            
            # 系统基础字段
            base_fields = [
                "`id` varchar(64) NOT NULL COMMENT '主键ID'",
                "`layer_name` varchar(255) NOT NULL COMMENT '图层名称'",
                "`geometry_type` varchar(50) DEFAULT NULL COMMENT '几何类型(POINT/LINESTRING/POLYGON)'",
                "`geometry_wkt` LONGTEXT DEFAULT NULL COMMENT '几何数据(WKT格式)'",
                "`geometry_wkb` LONGBLOB DEFAULT NULL COMMENT '几何数据(WKB格式)'",
                "`lng` decimal(12,8) DEFAULT NULL COMMENT '经度'",
                "`lat` decimal(12,8) DEFAULT NULL COMMENT '纬度'",
                "`area` decimal(20,6) DEFAULT NULL COMMENT '面积(平方米)'",
                "`perimeter` decimal(18,6) DEFAULT NULL COMMENT '周长(米)'"
            ]
            field_definitions.extend(base_fields)
            
            # 动态字段
            for field_name, field_type in field_types.items():
                # 处理字段名，确保符合MySQL命名规范
                safe_field_name = field_name.lower().replace(' ', '_').replace('-', '_')
                field_def = f"`{safe_field_name}` {field_type} DEFAULT NULL COMMENT '{field_name}字段'"
                field_definitions.append(field_def)
            
            # 系统管理字段
            system_fields = [
                "`source_file` varchar(500) DEFAULT NULL COMMENT '源文件路径'",
                "`import_time` datetime DEFAULT NULL COMMENT '导入时间'",
                "`del_flag` char(1) DEFAULT '0' COMMENT '删除标志(0存在 1删除)'",
                "`create_by` varchar(64) DEFAULT 'system' COMMENT '创建者'",
                "`create_time` datetime DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间'",
                "`update_by` varchar(64) DEFAULT 'system' COMMENT '更新者'",
                "`update_time` datetime DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间'",
                "`remark` varchar(500) DEFAULT NULL COMMENT '备注'"
            ]
            field_definitions.extend(system_fields)
            
            # 构建CREATE TABLE语句
            create_sql = f"""
            CREATE TABLE IF NOT EXISTS `{table_name}` (
                {','.join(field_definitions)},
                PRIMARY KEY (`id`),
                KEY `idx_layer_name` (`layer_name`),
                KEY `idx_geometry_type` (`geometry_type`),
                KEY `idx_lng_lat` (`lng`, `lat`),
                KEY `idx_del_flag` (`del_flag`)
            ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_bin COMMENT='GIS图层数据表';
            """
            
            with self.connection.cursor() as cursor:
                cursor.execute(create_sql)
                self.connection.commit()
                logger.info(f"表 {table_name} 创建成功")
                return True
                
        except Exception as e:
            logger.error(f"创建表失败: {e}")
            return False
    
    def read_gdb_layers(self, gdb_path: str) -> List[str]:
        """
        读取GDB目录中的图层列表
        
        Args:
            gdb_path: GDB目录路径
            
        Returns:
            List[str]: 图层名称列表
        """
        try:
            if not os.path.exists(gdb_path):
                logger.error(f"GDB目录不存在: {gdb_path}")
                return []
            
            if not os.path.isdir(gdb_path):
                logger.error(f"{gdb_path} 不是一个目录")
                return []
            
            layers = fiona.listlayers(gdb_path)
            logger.info(f"发现 {len(layers)} 个图层: {layers}")
            return layers
        except Exception as e:
            logger.error(f"读取GDB图层失败: {e}")
            return []

    def import_layer_data(self, gdb_path: str, layer_name: str, table_name: str,
                         batch_size: int = 1000) -> bool:
        """
        导入单个图层数据

        Args:
            gdb_path: GDB目录路径
            layer_name: 图层名称
            table_name: 目标表名
            batch_size: 批量插入大小

        Returns:
            bool: 导入是否成功
        """
        try:
            # 读取图层数据
            logger.info(f"开始读取图层: {layer_name}")
            gdf = gpd.read_file(gdb_path, layer=layer_name)

            if gdf.empty:
                logger.warning(f"图层 {layer_name} 为空")
                return True

            logger.info(f"图层 {layer_name} 包含 {len(gdf)} 个要素")
            logger.info(f"图层字段: {list(gdf.columns)}")

            # 批量处理数据
            total_imported = 0
            current_time = self._get_current_time()

            for i in range(0, len(gdf), batch_size):
                batch_data = gdf.iloc[i:i+batch_size]
                batch_records = []

                for idx, row in batch_data.iterrows():
                    try:
                        # 处理几何数据
                        geometry = row.geometry
                        geometry_type = geometry.geom_type if geometry else None

                        # 检查是否需要坐标系转换
                        converted_geometry = geometry
                        if geometry and gdf.crs and not gdf.crs.is_geographic:
                            # 转换几何数据到WGS84地理坐标系
                            try:
                                single_geom_gdf = gpd.GeoDataFrame([1], geometry=[geometry], crs=gdf.crs)
                                converted_gdf = single_geom_gdf.to_crs('EPSG:4326')
                                converted_geometry = converted_gdf.geometry.iloc[0]
                                logger.debug(f"几何数据已转换为地理坐标系")
                            except Exception as e:
                                logger.warning(f"几何数据坐标转换失败: {e}")
                                converted_geometry = geometry

                        # 使用转换后的几何数据
                        geometry_wkt = converted_geometry.wkt if converted_geometry else None
                        geometry_wkb = converted_geometry.wkb if converted_geometry else None

                        # 计算中心点坐标（使用转换后的几何数据）
                        lng, lat = None, None
                        if converted_geometry:
                            try:
                                if geometry_type == 'Point':
                                    lng, lat = converted_geometry.x, converted_geometry.y
                                else:
                                    centroid = converted_geometry.centroid
                                    lng, lat = centroid.x, centroid.y

                                # 验证坐标值
                                lng, lat = self._validate_coordinates(lng, lat)
                            except Exception as e:
                                logger.warning(f"计算坐标失败: {e}")
                                lng, lat = None, None

                        # 计算面积和周长
                        area = geometry.area if geometry and hasattr(geometry, 'area') else None
                        perimeter = geometry.length if geometry and hasattr(geometry, 'length') else None

                        # 构建基础记录
                        record = {
                            'id': self._generate_uuid(),
                            'layer_name': layer_name,
                            'geometry_type': geometry_type,
                            'geometry_wkt': geometry_wkt,
                            'geometry_wkb': geometry_wkb,
                            'lng': lng,
                            'lat': lat,
                            'area': area,
                            'perimeter': perimeter,
                            'source_file': gdb_path,
                            'import_time': current_time,
                            'del_flag': '0',
                            'create_by': 'system',
                            'create_time': current_time,
                            'update_by': 'system',
                            'update_time': current_time,
                            'remark': f'从{os.path.basename(gdb_path)}导入'
                        }

                        # 添加实际字段数据
                        for col in gdf.columns:
                            if col != 'geometry':
                                value = row[col]
                                # 处理字段名，确保符合MySQL命名规范
                                safe_field_name = col.lower().replace(' ', '_').replace('-', '_')

                                if pd.notna(value):
                                    # 根据数据类型进行适当的转换
                                    processed_value = self._process_field_value(value, gdf[col].dtype)
                                    record[safe_field_name] = processed_value
                                else:
                                    # 确保None值被正确处理
                                    record[safe_field_name] = None

                        batch_records.append(record)

                    except Exception as e:
                        logger.error(f"处理要素 {idx} 失败: {e}")
                        continue

                # 批量插入数据
                if batch_records:
                    self._batch_insert(table_name, batch_records)
                    total_imported += len(batch_records)
                    logger.info(f"已导入 {total_imported}/{len(gdf)} 个要素")

            logger.info(f"图层 {layer_name} 导入完成，共导入 {total_imported} 个要素")
            return True

        except Exception as e:
            logger.error(f"导入图层 {layer_name} 失败: {e}")
            return False

    def _batch_insert(self, table_name: str, records: List[Dict[str, Any]]):
        """批量插入数据，确保字段匹配和类型一致性"""
        if not records:
            return

        try:
            # 获取表结构信息
            table_columns = self._get_table_columns(table_name)
            if not table_columns:
                raise Exception(f"无法获取表 {table_name} 的字段信息")

            # 确保记录中的字段都存在于表中
            valid_columns = []
            for col in records[0].keys():
                if col in table_columns:
                    valid_columns.append(col)
                else:
                    logger.warning(f"字段 {col} 不存在于表 {table_name} 中，将被忽略")

            if not valid_columns:
                raise Exception("没有有效的字段可以插入")

            # 构建插入SQL
            placeholders = ', '.join(['%s'] * len(valid_columns))
            sql = f"INSERT INTO `{table_name}` ({', '.join([f'`{col}`' for col in valid_columns])}) VALUES ({placeholders})"

            # 准备数据，只包含有效字段
            values = []
            for record in records:
                row_values = []
                for col in valid_columns:
                    value = record.get(col)
                    # 确保None值被正确处理
                    row_values.append(value)
                values.append(row_values)

            # 执行批量插入
            with self.connection.cursor() as cursor:
                cursor.executemany(sql, values)
                self.connection.commit()
                logger.debug(f"成功插入 {len(values)} 条记录到表 {table_name}")

        except Exception as e:
            logger.error(f"批量插入失败: {e}")
            logger.error(f"SQL: {sql if 'sql' in locals() else 'SQL未生成'}")
            logger.error(f"记录样本: {records[0] if records else 'None'}")
            self.connection.rollback()
            raise

    def _get_table_columns(self, table_name: str) -> set:
        """获取表的字段列表"""
        try:
            with self.connection.cursor() as cursor:
                cursor.execute(f"DESCRIBE `{table_name}`")
                columns = cursor.fetchall()
                return {col[0] for col in columns}
        except Exception as e:
            logger.error(f"获取表字段失败: {e}")
            return set()

    def import_gdb_directory(self, gdb_dir: str, table_prefix: str = 'suining',
                           create_separate_tables: bool = True):
        """
        导入整个GDB目录

        Args:
            gdb_dir: GDB目录路径
            table_prefix: 表名前缀（默认为'suining'）
            create_separate_tables: 是否为每个图层创建单独的表（默认True）
        """
        try:
            gdb_path = Path(gdb_dir)
            if not gdb_path.exists():
                logger.error(f"GDB目录不存在: {gdb_dir}")
                return

            if not gdb_path.is_dir():
                logger.error(f"{gdb_dir} 不是一个目录")
                return

            # 读取图层
            layers = self.read_gdb_layers(str(gdb_path))
            if not layers:
                logger.warning("未发现任何图层")
                return

            logger.info(f"发现 {len(layers)} 个图层: {layers}")

            if create_separate_tables:
                # 为每个图层创建单独的表，使用图层名作为表名
                success_count = 0
                created_tables = []

                for layer_name in layers:
                    # 清理图层名，生成表名
                    clean_layer_name = self._sanitize_table_name(layer_name)
                    layer_table_name = f"{table_prefix}_{clean_layer_name}"

                    logger.info(f"正在为图层 '{layer_name}' 创建表: {layer_table_name}")

                    # 根据图层字段动态创建表
                    if self.create_dynamic_table(layer_table_name, str(gdb_path), layer_name):
                        if self.import_layer_data(str(gdb_path), layer_name, layer_table_name):
                            success_count += 1
                            created_tables.append(layer_table_name)
                            logger.info(f"✓ 图层 '{layer_name}' 成功导入到表 '{layer_table_name}'")
                        else:
                            logger.error(f"✗ 图层 '{layer_name}' 导入失败")
                    else:
                        logger.error(f"✗ 图层 '{layer_name}' 表创建失败")

                logger.info(f"导入完成: {success_count}/{len(layers)} 个图层成功")
                logger.info(f"创建的表: {created_tables}")

            else:
                # 所有图层导入到同一个表
                unified_table_name = f"{table_prefix}_unified"

                # 分析所有图层的字段，创建统一的表结构
                all_fields = {}
                for layer_name in layers:
                    layer_fields = self.analyze_gdb_schema(str(gdb_path), layer_name)
                    all_fields.update(layer_fields)

                # 创建包含所有字段的统一表
                if self._create_unified_table(unified_table_name, all_fields):
                    success_count = 0
                    for layer_name in layers:
                        logger.info(f"正在导入图层: {layer_name}")
                        if self.import_layer_data(str(gdb_path), layer_name, unified_table_name):
                            success_count += 1
                        else:
                            logger.error(f"图层 {layer_name} 导入失败")

                    logger.info(f"导入完成: {success_count}/{len(layers)} 个图层成功")
                    logger.info(f"统一表名: {unified_table_name}")
                else:
                    logger.error("统一表创建失败")
                    return

        except Exception as e:
            logger.error(f"导入GDB目录失败: {e}")

    def _create_unified_table(self, table_name: str, all_fields: Dict[str, str]) -> bool:
        """
        创建包含所有图层字段的统一表

        Args:
            table_name: 表名
            all_fields: 所有字段的类型映射

        Returns:
            bool: 创建是否成功
        """
        try:
            # 构建字段定义
            field_definitions = []

            # 系统基础字段
            base_fields = [
                "`id` varchar(64) NOT NULL COMMENT '主键ID'",
                "`layer_name` varchar(255) NOT NULL COMMENT '图层名称'",
                "`geometry_type` varchar(50) DEFAULT NULL COMMENT '几何类型(POINT/LINESTRING/POLYGON)'",
                "`geometry_wkt` LONGTEXT DEFAULT NULL COMMENT '几何数据(WKT格式)'",
                "`geometry_wkb` LONGBLOB DEFAULT NULL COMMENT '几何数据(WKB格式)'",
                "`lng` decimal(12,8) DEFAULT NULL COMMENT '经度'",
                "`lat` decimal(12,8) DEFAULT NULL COMMENT '纬度'",
                "`area` decimal(20,6) DEFAULT NULL COMMENT '面积(平方米)'",
                "`perimeter` decimal(18,6) DEFAULT NULL COMMENT '周长(米)'"
            ]
            field_definitions.extend(base_fields)

            # 动态字段
            for field_name, field_type in all_fields.items():
                # 处理字段名，确保符合MySQL命名规范
                safe_field_name = field_name.lower().replace(' ', '_').replace('-', '_')
                field_def = f"`{safe_field_name}` {field_type} DEFAULT NULL COMMENT '{field_name}字段'"
                field_definitions.append(field_def)

            # 系统管理字段
            system_fields = [
                "`source_file` varchar(500) DEFAULT NULL COMMENT '源文件路径'",
                "`import_time` datetime DEFAULT NULL COMMENT '导入时间'",
                "`del_flag` char(1) DEFAULT '0' COMMENT '删除标志(0存在 1删除)'",
                "`create_by` varchar(64) DEFAULT 'system' COMMENT '创建者'",
                "`create_time` datetime DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间'",
                "`update_by` varchar(64) DEFAULT 'system' COMMENT '更新者'",
                "`update_time` datetime DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间'",
                "`remark` varchar(500) DEFAULT NULL COMMENT '备注'"
            ]
            field_definitions.extend(system_fields)

            # 构建CREATE TABLE语句
            create_sql = f"""
            CREATE TABLE IF NOT EXISTS `{table_name}` (
                {','.join(field_definitions)},
                PRIMARY KEY (`id`),
                KEY `idx_layer_name` (`layer_name`),
                KEY `idx_geometry_type` (`geometry_type`),
                KEY `idx_lng_lat` (`lng`, `lat`),
                KEY `idx_del_flag` (`del_flag`)
            ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_bin COMMENT='GIS统一图层数据表';
            """

            with self.connection.cursor() as cursor:
                cursor.execute(create_sql)
                self.connection.commit()
                logger.info(f"统一表 {table_name} 创建成功，包含 {len(all_fields)} 个动态字段")
                return True

        except Exception as e:
            logger.error(f"创建统一表失败: {e}")
            return False

    def close(self):
        """关闭连接"""
        if self.connection:
            self.connection.close()
        if self.engine:
            self.engine.dispose()

def main():
    """主函数"""
    # 数据库配置
    db_config = {
        'host': 'localhost',
        'port': 3306,
        'user': 'root',
        'password': 'password',
        'database': 'smart_cloud'
    }

    # GDB目录路径
    gdb_path = '/path/to/your/遂宁市.gdb'

    # 目标表名前缀
    table_prefix = 'suining'

    try:
        # 创建导入器
        importer = GISDataImporter(db_config)

        # 导入数据（分表模式）
        importer.import_gdb_directory(gdb_path, table_prefix, create_separate_tables=True)

        # 关闭连接
        importer.close()

        logger.info("数据导入完成")

    except Exception as e:
        logger.error(f"程序执行失败: {e}")
        sys.exit(1)

if __name__ == '__main__':
    main()
