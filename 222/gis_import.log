2025-07-15 11:36:38,313 - INFO - 数据库连接成功
2025-07-15 11:36:38,349 - INFO - GDAL signalled an error: err_no=1, msg='PROJ: internal_proj_identify: /Users/<USER>/miniconda3/share/proj/proj.db contains DATABASE.LAYOUT.VERSION.MINOR = 2 whereas a number >= 3 is expected. It comes from another PROJ installation.'
2025-07-15 11:36:38,368 - INFO - 发现 3 个图层: ['GHFW', 'DYHF', 'DYCMYDBJGH']
2025-07-15 11:36:38,368 - INFO - 发现 3 个图层: ['GHFW', 'DYHF', 'DYCMYDBJGH']
2025-07-15 11:36:38,368 - INFO - 正在为图层 'GHFW' 创建表: 2_ghfw
2025-07-15 11:36:38,455 - INFO - 图层 GHFW 字段分析完成: {'SHAPE_Length': 'DECIMAL(18,6)', 'SHAPE_Area': 'DECIMAL(20,6)', 'BSM': 'VARCHAR(255)', 'YSDM': 'VARCHAR(255)', 'XZQDM': 'VARCHAR(255)', 'XZQMC': 'VARCHAR(255)', 'GHLX': 'VARCHAR(255)', 'GHCM': 'VARCHAR(255)', 'GHXMMC': 'VARCHAR(255)', 'GHMJ': 'DECIMAL(10,4)', 'GHFW': 'VARCHAR(255)', 'GHBZDW': 'VARCHAR(255)', 'GHZZBM': 'VARCHAR(255)', 'PZBM': 'VARCHAR(255)', 'PZSJ': 'DATETIME', 'PZWH': 'VARCHAR(255)', 'BZ': 'VARCHAR(255)'}
2025-07-15 11:36:38,461 - INFO - 表 2_ghfw 创建成功
2025-07-15 11:36:38,461 - INFO - 开始读取图层: GHFW
2025-07-15 11:36:38,462 - INFO - 图层 GHFW 包含 1 个要素
2025-07-15 11:36:38,462 - INFO - 图层字段: ['SHAPE_Length', 'SHAPE_Area', 'BSM', 'YSDM', 'XZQDM', 'XZQMC', 'GHLX', 'GHCM', 'GHXMMC', 'GHMJ', 'GHFW', 'GHBZDW', 'GHZZBM', 'PZBM', 'PZSJ', 'PZWH', 'BZ', 'geometry']
2025-07-15 11:36:38,514 - INFO - 已导入 1/1 个要素
2025-07-15 11:36:38,514 - INFO - 图层 GHFW 导入完成，共导入 1 个要素
2025-07-15 11:36:38,514 - INFO - ✓ 图层 'GHFW' 成功导入到表 '2_ghfw'
2025-07-15 11:36:38,514 - INFO - 正在为图层 'DYHF' 创建表: 2_dyhf
2025-07-15 11:36:38,517 - INFO - 图层 DYHF 字段分析完成: {'SHAPE_Length': 'DECIMAL(18,6)', 'SHAPE_Area': 'DECIMAL(20,6)', 'BSM': 'VARCHAR(255)', 'YSDM': 'VARCHAR(255)', 'XZQDM': 'VARCHAR(255)', 'XZQMC': 'VARCHAR(255)', 'DYBH': 'VARCHAR(255)', 'DYMC': 'VARCHAR(255)', 'DYLX': 'VARCHAR(255)', 'DYMJ': 'DECIMAL(10,4)', 'ZDGN': 'VARCHAR(255)', 'JSYDGM': 'DECIMAL(10,4)', 'BZ': 'VARCHAR(255)'}
2025-07-15 11:36:38,518 - INFO - 表 2_dyhf 创建成功
2025-07-15 11:36:38,518 - INFO - 开始读取图层: DYHF
2025-07-15 11:36:38,520 - INFO - 图层 DYHF 包含 1 个要素
2025-07-15 11:36:38,520 - INFO - 图层字段: ['SHAPE_Length', 'SHAPE_Area', 'BSM', 'YSDM', 'XZQDM', 'XZQMC', 'DYBH', 'DYMC', 'DYLX', 'DYMJ', 'ZDGN', 'JSYDGM', 'BZ', 'geometry']
2025-07-15 11:36:38,557 - INFO - 已导入 1/1 个要素
2025-07-15 11:36:38,557 - INFO - 图层 DYHF 导入完成，共导入 1 个要素
2025-07-15 11:36:38,557 - INFO - ✓ 图层 'DYHF' 成功导入到表 '2_dyhf'
2025-07-15 11:36:38,557 - INFO - 正在为图层 'DYCMYDBJGH' 创建表: 2_dycmydbjgh
2025-07-15 11:36:38,562 - INFO - 图层 DYCMYDBJGH 字段分析完成: {'SHAPE_Length': 'DECIMAL(18,6)', 'SHAPE_Area': 'DECIMAL(20,6)', 'BSM': 'VARCHAR(255)', 'YSDM': 'VARCHAR(255)', 'XZQDM': 'VARCHAR(255)', 'XZQMC': 'VARCHAR(255)', 'XXGHBZDYBH': 'VARCHAR(255)', 'XXGHBZDYMC': 'VARCHAR(255)', 'DKBH': 'VARCHAR(255)', 'CSLXKZMJ': 'DECIMAL(15,6)', 'CSLVXKZMJ': 'DECIMAL(15,6)', 'CSHXKZMJ': 'DECIMAL(10,4)', 'CSZXKZMJ': 'DECIMAL(10,4)', 'YDYHFLDM': 'VARCHAR(255)', 'YDYHFLMC': 'VARCHAR(255)', 'YDMJ': 'DECIMAL(10,4)', 'JSYDMJ': 'DECIMAL(10,4)', 'JSYDJG': 'VARCHAR(255)', 'DXKJGH': 'VARCHAR(255)', 'BZ': 'VARCHAR(255)'}
2025-07-15 11:36:38,563 - INFO - 表 2_dycmydbjgh 创建成功
2025-07-15 11:36:38,563 - INFO - 开始读取图层: DYCMYDBJGH
2025-07-15 11:36:38,568 - INFO - 图层 DYCMYDBJGH 包含 327 个要素
2025-07-15 11:36:38,568 - INFO - 图层字段: ['SHAPE_Length', 'SHAPE_Area', 'BSM', 'YSDM', 'XZQDM', 'XZQMC', 'XXGHBZDYBH', 'XXGHBZDYMC', 'DKBH', 'CSLXKZMJ', 'CSLVXKZMJ', 'CSHXKZMJ', 'CSZXKZMJ', 'YDYHFLDM', 'YDYHFLMC', 'YDMJ', 'JSYDMJ', 'JSYDJG', 'DXKJGH', 'BZ', 'geometry']
2025-07-15 11:36:38,833 - INFO - 已导入 327/327 个要素
2025-07-15 11:36:38,834 - INFO - 图层 DYCMYDBJGH 导入完成，共导入 327 个要素
2025-07-15 11:36:38,834 - INFO - ✓ 图层 'DYCMYDBJGH' 成功导入到表 '2_dycmydbjgh'
2025-07-15 11:36:38,834 - INFO - 导入完成: 3/3 个图层成功
2025-07-15 11:36:38,834 - INFO - 创建的表: ['2_ghfw', '2_dyhf', '2_dycmydbjgh']
2025-07-15 11:38:27,360 - INFO - 数据库连接成功
2025-07-15 11:38:27,399 - INFO - GDAL signalled an error: err_no=1, msg='PROJ: internal_proj_identify: /Users/<USER>/miniconda3/share/proj/proj.db contains DATABASE.LAYOUT.VERSION.MINOR = 2 whereas a number >= 3 is expected. It comes from another PROJ installation.'
2025-07-15 11:38:27,418 - INFO - 发现 3 个图层: ['GHFW', 'DYHF', 'DYCMYDBJGH']
2025-07-15 11:38:27,418 - INFO - 发现 3 个图层: ['GHFW', 'DYHF', 'DYCMYDBJGH']
2025-07-15 11:38:27,418 - INFO - 正在为图层 'GHFW' 创建表: 2_ghfw
2025-07-15 11:38:27,521 - INFO - 图层 GHFW 字段分析完成: {'SHAPE_Length': 'DECIMAL(18,6)', 'SHAPE_Area': 'DECIMAL(20,6)', 'BSM': 'VARCHAR(255)', 'YSDM': 'VARCHAR(255)', 'XZQDM': 'VARCHAR(255)', 'XZQMC': 'VARCHAR(255)', 'GHLX': 'VARCHAR(255)', 'GHCM': 'VARCHAR(255)', 'GHXMMC': 'VARCHAR(255)', 'GHMJ': 'DECIMAL(10,4)', 'GHFW': 'VARCHAR(255)', 'GHBZDW': 'VARCHAR(255)', 'GHZZBM': 'VARCHAR(255)', 'PZBM': 'VARCHAR(255)', 'PZSJ': 'DATETIME', 'PZWH': 'VARCHAR(255)', 'BZ': 'VARCHAR(255)'}
2025-07-15 11:38:27,527 - INFO - 表 2_ghfw 创建成功
2025-07-15 11:38:27,527 - INFO - 开始读取图层: GHFW
2025-07-15 11:38:27,529 - INFO - 图层 GHFW 包含 1 个要素
2025-07-15 11:38:27,529 - INFO - 图层字段: ['SHAPE_Length', 'SHAPE_Area', 'BSM', 'YSDM', 'XZQDM', 'XZQMC', 'GHLX', 'GHCM', 'GHXMMC', 'GHMJ', 'GHFW', 'GHBZDW', 'GHZZBM', 'PZBM', 'PZSJ', 'PZWH', 'BZ', 'geometry']
2025-07-15 11:38:27,587 - INFO - 已导入 1/1 个要素
2025-07-15 11:38:27,587 - INFO - 图层 GHFW 导入完成，共导入 1 个要素
2025-07-15 11:38:27,587 - INFO - ✓ 图层 'GHFW' 成功导入到表 '2_ghfw'
2025-07-15 11:38:27,587 - INFO - 正在为图层 'DYHF' 创建表: 2_dyhf
2025-07-15 11:38:27,590 - INFO - 图层 DYHF 字段分析完成: {'SHAPE_Length': 'DECIMAL(18,6)', 'SHAPE_Area': 'DECIMAL(20,6)', 'BSM': 'VARCHAR(255)', 'YSDM': 'VARCHAR(255)', 'XZQDM': 'VARCHAR(255)', 'XZQMC': 'VARCHAR(255)', 'DYBH': 'VARCHAR(255)', 'DYMC': 'VARCHAR(255)', 'DYLX': 'VARCHAR(255)', 'DYMJ': 'DECIMAL(10,4)', 'ZDGN': 'VARCHAR(255)', 'JSYDGM': 'DECIMAL(10,4)', 'BZ': 'VARCHAR(255)'}
2025-07-15 11:38:27,591 - INFO - 表 2_dyhf 创建成功
2025-07-15 11:38:27,591 - INFO - 开始读取图层: DYHF
2025-07-15 11:38:27,593 - INFO - 图层 DYHF 包含 1 个要素
2025-07-15 11:38:27,593 - INFO - 图层字段: ['SHAPE_Length', 'SHAPE_Area', 'BSM', 'YSDM', 'XZQDM', 'XZQMC', 'DYBH', 'DYMC', 'DYLX', 'DYMJ', 'ZDGN', 'JSYDGM', 'BZ', 'geometry']
2025-07-15 11:38:27,622 - INFO - 已导入 1/1 个要素
2025-07-15 11:38:27,622 - INFO - 图层 DYHF 导入完成，共导入 1 个要素
2025-07-15 11:38:27,622 - INFO - ✓ 图层 'DYHF' 成功导入到表 '2_dyhf'
2025-07-15 11:38:27,622 - INFO - 正在为图层 'DYCMYDBJGH' 创建表: 2_dycmydbjgh
2025-07-15 11:38:27,627 - INFO - 图层 DYCMYDBJGH 字段分析完成: {'SHAPE_Length': 'DECIMAL(18,6)', 'SHAPE_Area': 'DECIMAL(20,6)', 'BSM': 'VARCHAR(255)', 'YSDM': 'VARCHAR(255)', 'XZQDM': 'VARCHAR(255)', 'XZQMC': 'VARCHAR(255)', 'XXGHBZDYBH': 'VARCHAR(255)', 'XXGHBZDYMC': 'VARCHAR(255)', 'DKBH': 'VARCHAR(255)', 'CSLXKZMJ': 'DECIMAL(15,6)', 'CSLVXKZMJ': 'DECIMAL(15,6)', 'CSHXKZMJ': 'DECIMAL(10,4)', 'CSZXKZMJ': 'DECIMAL(10,4)', 'YDYHFLDM': 'VARCHAR(255)', 'YDYHFLMC': 'VARCHAR(255)', 'YDMJ': 'DECIMAL(10,4)', 'JSYDMJ': 'DECIMAL(10,4)', 'JSYDJG': 'VARCHAR(255)', 'DXKJGH': 'VARCHAR(255)', 'BZ': 'VARCHAR(255)'}
2025-07-15 11:38:27,628 - INFO - 表 2_dycmydbjgh 创建成功
2025-07-15 11:38:27,628 - INFO - 开始读取图层: DYCMYDBJGH
2025-07-15 11:38:27,634 - INFO - 图层 DYCMYDBJGH 包含 327 个要素
2025-07-15 11:38:27,634 - INFO - 图层字段: ['SHAPE_Length', 'SHAPE_Area', 'BSM', 'YSDM', 'XZQDM', 'XZQMC', 'XXGHBZDYBH', 'XXGHBZDYMC', 'DKBH', 'CSLXKZMJ', 'CSLVXKZMJ', 'CSHXKZMJ', 'CSZXKZMJ', 'YDYHFLDM', 'YDYHFLMC', 'YDMJ', 'JSYDMJ', 'JSYDJG', 'DXKJGH', 'BZ', 'geometry']
2025-07-15 11:38:27,907 - INFO - 已导入 327/327 个要素
2025-07-15 11:38:27,907 - INFO - 图层 DYCMYDBJGH 导入完成，共导入 327 个要素
2025-07-15 11:38:27,908 - INFO - ✓ 图层 'DYCMYDBJGH' 成功导入到表 '2_dycmydbjgh'
2025-07-15 11:38:27,908 - INFO - 导入完成: 3/3 个图层成功
2025-07-15 11:38:27,908 - INFO - 创建的表: ['2_ghfw', '2_dyhf', '2_dycmydbjgh']
2025-07-15 11:39:17,492 - INFO - 数据库连接成功
2025-07-15 11:39:17,531 - INFO - GDAL signalled an error: err_no=1, msg='PROJ: internal_proj_identify: /Users/<USER>/miniconda3/share/proj/proj.db contains DATABASE.LAYOUT.VERSION.MINOR = 2 whereas a number >= 3 is expected. It comes from another PROJ installation.'
2025-07-15 11:39:17,550 - INFO - 发现 3 个图层: ['GHFW', 'DYHF', 'DYCMYDBJGH']
2025-07-15 11:39:17,550 - INFO - 发现 3 个图层: ['GHFW', 'DYHF', 'DYCMYDBJGH']
2025-07-15 11:39:17,550 - INFO - 正在为图层 'GHFW' 创建表: 2_ghfw
2025-07-15 11:39:17,650 - INFO - 图层 GHFW 字段分析完成: {'SHAPE_Length': 'DECIMAL(18,6)', 'SHAPE_Area': 'DECIMAL(20,6)', 'BSM': 'VARCHAR(255)', 'YSDM': 'VARCHAR(255)', 'XZQDM': 'VARCHAR(255)', 'XZQMC': 'VARCHAR(255)', 'GHLX': 'VARCHAR(255)', 'GHCM': 'VARCHAR(255)', 'GHXMMC': 'VARCHAR(255)', 'GHMJ': 'DECIMAL(10,4)', 'GHFW': 'VARCHAR(255)', 'GHBZDW': 'VARCHAR(255)', 'GHZZBM': 'VARCHAR(255)', 'PZBM': 'VARCHAR(255)', 'PZSJ': 'DATETIME', 'PZWH': 'VARCHAR(255)', 'BZ': 'VARCHAR(255)'}
2025-07-15 11:39:17,674 - INFO - 表 2_ghfw 创建成功
2025-07-15 11:39:17,674 - INFO - 开始读取图层: GHFW
2025-07-15 11:39:17,676 - INFO - 图层 GHFW 包含 1 个要素
2025-07-15 11:39:17,676 - INFO - 图层字段: ['SHAPE_Length', 'SHAPE_Area', 'BSM', 'YSDM', 'XZQDM', 'XZQMC', 'GHLX', 'GHCM', 'GHXMMC', 'GHMJ', 'GHFW', 'GHBZDW', 'GHZZBM', 'PZBM', 'PZSJ', 'PZWH', 'BZ', 'geometry']
2025-07-15 11:39:17,734 - INFO - 已导入 1/1 个要素
2025-07-15 11:39:17,734 - INFO - 图层 GHFW 导入完成，共导入 1 个要素
2025-07-15 11:39:17,734 - INFO - ✓ 图层 'GHFW' 成功导入到表 '2_ghfw'
2025-07-15 11:39:17,734 - INFO - 正在为图层 'DYHF' 创建表: 2_dyhf
2025-07-15 11:39:17,737 - INFO - 图层 DYHF 字段分析完成: {'SHAPE_Length': 'DECIMAL(18,6)', 'SHAPE_Area': 'DECIMAL(20,6)', 'BSM': 'VARCHAR(255)', 'YSDM': 'VARCHAR(255)', 'XZQDM': 'VARCHAR(255)', 'XZQMC': 'VARCHAR(255)', 'DYBH': 'VARCHAR(255)', 'DYMC': 'VARCHAR(255)', 'DYLX': 'VARCHAR(255)', 'DYMJ': 'DECIMAL(10,4)', 'ZDGN': 'VARCHAR(255)', 'JSYDGM': 'DECIMAL(10,4)', 'BZ': 'VARCHAR(255)'}
2025-07-15 11:39:17,744 - INFO - 表 2_dyhf 创建成功
2025-07-15 11:39:17,744 - INFO - 开始读取图层: DYHF
2025-07-15 11:39:17,746 - INFO - 图层 DYHF 包含 1 个要素
2025-07-15 11:39:17,746 - INFO - 图层字段: ['SHAPE_Length', 'SHAPE_Area', 'BSM', 'YSDM', 'XZQDM', 'XZQMC', 'DYBH', 'DYMC', 'DYLX', 'DYMJ', 'ZDGN', 'JSYDGM', 'BZ', 'geometry']
2025-07-15 11:39:17,783 - INFO - 已导入 1/1 个要素
2025-07-15 11:39:17,783 - INFO - 图层 DYHF 导入完成，共导入 1 个要素
2025-07-15 11:39:17,783 - INFO - ✓ 图层 'DYHF' 成功导入到表 '2_dyhf'
2025-07-15 11:39:17,783 - INFO - 正在为图层 'DYCMYDBJGH' 创建表: 2_dycmydbjgh
2025-07-15 11:39:17,789 - INFO - 图层 DYCMYDBJGH 字段分析完成: {'SHAPE_Length': 'DECIMAL(18,6)', 'SHAPE_Area': 'DECIMAL(20,6)', 'BSM': 'VARCHAR(255)', 'YSDM': 'VARCHAR(255)', 'XZQDM': 'VARCHAR(255)', 'XZQMC': 'VARCHAR(255)', 'XXGHBZDYBH': 'VARCHAR(255)', 'XXGHBZDYMC': 'VARCHAR(255)', 'DKBH': 'VARCHAR(255)', 'CSLXKZMJ': 'DECIMAL(15,6)', 'CSLVXKZMJ': 'DECIMAL(15,6)', 'CSHXKZMJ': 'DECIMAL(10,4)', 'CSZXKZMJ': 'DECIMAL(10,4)', 'YDYHFLDM': 'VARCHAR(255)', 'YDYHFLMC': 'VARCHAR(255)', 'YDMJ': 'DECIMAL(10,4)', 'JSYDMJ': 'DECIMAL(10,4)', 'JSYDJG': 'VARCHAR(255)', 'DXKJGH': 'VARCHAR(255)', 'BZ': 'VARCHAR(255)'}
2025-07-15 11:39:17,795 - INFO - 表 2_dycmydbjgh 创建成功
2025-07-15 11:39:17,795 - INFO - 开始读取图层: DYCMYDBJGH
2025-07-15 11:39:17,801 - INFO - 图层 DYCMYDBJGH 包含 327 个要素
2025-07-15 11:39:17,801 - INFO - 图层字段: ['SHAPE_Length', 'SHAPE_Area', 'BSM', 'YSDM', 'XZQDM', 'XZQMC', 'XXGHBZDYBH', 'XXGHBZDYMC', 'DKBH', 'CSLXKZMJ', 'CSLVXKZMJ', 'CSHXKZMJ', 'CSZXKZMJ', 'YDYHFLDM', 'YDYHFLMC', 'YDMJ', 'JSYDMJ', 'JSYDJG', 'DXKJGH', 'BZ', 'geometry']
2025-07-15 11:39:18,079 - INFO - 已导入 327/327 个要素
2025-07-15 11:39:18,080 - INFO - 图层 DYCMYDBJGH 导入完成，共导入 327 个要素
2025-07-15 11:39:18,080 - INFO - ✓ 图层 'DYCMYDBJGH' 成功导入到表 '2_dycmydbjgh'
2025-07-15 11:39:18,080 - INFO - 导入完成: 3/3 个图层成功
2025-07-15 11:39:18,080 - INFO - 创建的表: ['2_ghfw', '2_dyhf', '2_dycmydbjgh']
