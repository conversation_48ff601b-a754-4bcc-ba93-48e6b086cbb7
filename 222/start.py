from gis_data_import import GISDataImporter

# 数据库配置
db_config = {
    'host': 'localhost',
    'port': 3306,
    'user': 'root',
    'password': '123456',
    'database': 'land'
}

# 创建导入器
importer = GISDataImporter(db_config)


# 导入GDB数据（分表模式）
importer.import_gdb_directory('/Users/<USER>/Downloads/河东新区灵泉-养生谷片区县级行政区详细规划成果数据/510900遂宁市河东新区灵泉-养生谷片区单元层面详细规划.gdb', '2', create_separate_tables=True)

# 关闭连接
importer.close()